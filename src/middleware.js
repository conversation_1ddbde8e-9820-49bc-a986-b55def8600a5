import { getToken } from 'next-auth/jwt';
import { NextResponse } from 'next/server';
import { verifyHeaders } from '@/utils/helper';
import messages from '@/utils/messages';

const AUTH_PATHS = ['/auth/login', '/auth/error'];
const ROOT_REDIRECT_PATHS = ['/', '/admin'];

function isStaticAsset(pathname) {
  return (
    pathname.includes('.') ||
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api/auth')
  );
}

function isAuthenticated(token) {
  return token && token.roleId === 1 && token.accessToken && !token.otpPending;
}

export async function middleware(request) {
  const { pathname } = request.nextUrl;

  // 1. Bypass static assets and public API
  if (isStaticAsset(pathname)) return NextResponse.next();

  // 2. Get token early
  const token = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET,
    secureCookie: (process.env.NEXT_PUBLIC_APP_ENV === 'live' || process.env.NEXT_PUBLIC_APP_ENV === 'production') && request.nextUrl.protocol === 'https:',
  });

  const isAuthPage = AUTH_PATHS.some(path => pathname.includes(path));
  const isApiRoute = pathname.startsWith('/api/v1/admin');

  // 3. Redirect logged-in users away from root/login to dashboard
  if (ROOT_REDIRECT_PATHS.includes(pathname)) {
    if (isAuthenticated(token)) {
      return NextResponse.redirect(new URL('/dashboard', request.url));
    } else {
      const response = NextResponse.redirect(new URL('/auth/login', request.url));
      request.cookies.getAll().forEach(cookie => response.cookies.delete(cookie.name));
      return response;
    }
  }

  // 4. Redirect authenticated users away from auth pages
  if (isAuthPage && isAuthenticated(token)) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // 5. Block unauthenticated or OTP-pending users from other pages
  if (!isAuthPage && (!token || token.otpPending)) {
    return NextResponse.redirect(new URL('/auth/login', request.url));
  }

  // 6. Handle secured API routes
  if (isApiRoute) {
    if (!isAuthenticated(token)) {
      return NextResponse.json({ error: messages.UNAUTHORIZED_ACCESS }, { status: 401 });
    }

    const headersValid = await verifyHeaders({
      'X-API-Username': request.headers.get('X-API-Username'),
      'X-API-Password': request.headers.get('X-API-Password'),
      'Authorization': request.headers.get('Authorization'),
    }, token.accessToken);

    if (!headersValid) {
      return NextResponse.json({ error: messages.UNAUTHORIZED_ACCESS }, { status: 401 });
    }
  }

  return NextResponse.next();
}

// Only match dynamic routes, not static assets
export const config = {
  matcher: [
    '/',
    '/admin/:path*',
    '/dashboard/:path*',
    '/api/v1/:path*',
    '/auth/login',
    '/auth/error',
  ],
};
