import { Sequelize } from 'sequelize';
import mysql2 from 'mysql2';
import { exec } from 'child_process';

process.setMaxListeners(30);
const appEnv = process.env.NEXT_PUBLIC_APP_ENV;

class Database {
  static instance = null;
  sequelize = null;
  isInitialized = false;
  connectionAttempts = 0;
  maxConnectionAttempts = 3;

  constructor() {
    if (Database.instance) {
      return Database.instance;
    }

    this.sequelize = new Sequelize(
      process.env.DB_NAME || 'wetrade',
      process.env.DB_USER || 'root',
      process.env.DB_PASSWORD,
      {
        host: process.env.NEXT_PUBLIC_APP_ENV === "local" ? 'localhost' : process.env.DB_HOST,
        port: process.env.DB_PORT || 3306,
        dialect: 'mysql',
        dialectModule: mysql2,
        logging: false,
        keepAliveInitialDelay: 30000,
        pool: {
          max: 50, // Reduced from 100 to prevent too many connections
          min: 5,  // Reduced from 10
          acquire: 180000,
          idle: 30000, // Reduced from 60000
          evict: 30000, // Reduced from 60000
          validate: async (connection) => {
            try {
              await connection.promise().query('SELECT 1');
              return true;
            } catch {
              return false;
            }
          }
        },
        retry: {
          match: [
            /Deadlock/i,
            /Lock wait timeout exceeded/i,
            /SequelizeConnectionError/,
            /SequelizeConnectionRefusedError/,
            /SequelizeHostNotFoundError/,
            /SequelizeHostNotReachableError/,
            /SequelizeInvalidConnectionError/,
            /SequelizeConnectionTimedOutError/,
            /SequelizeConnectionAcquireTimeoutError/,
            /Too many connections/i
          ],
          max: 5,
          backoffBase: 1000,
          backoffExponent: 1.5
        },
        define: {
          timestamps: false,
        },
        timezone: '+05:30',
        dialectOptions: {
          connectTimeout: 120000
        }
      }
    );

    this.attachSignalHandlers();

    Database.instance = this;
    return this;
  }

  attachSignalHandlers() {
    if (!global.__sequelizeSignalHandlersAttached) {
      ['SIGINT', 'SIGTERM', 'SIGQUIT'].forEach(signal => {
        process.once(signal, async () => {
          try {
            await this.sequelize.close();
            process.exit(0);
          } catch (error) {
            console.error('Error closing database connections:', error);
            process.exit(1);
          }
        });
      });

      process.once('uncaughtException', async (error) => {
        console.error('Uncaught exception:', error);
        try {
          await this.sequelize.close();
        } catch (closeError) {
          console.error('Error closing connections after uncaught exception:', closeError);
        } finally {
          process.exit(1);
        }
      });

      global.__sequelizeSignalHandlersAttached = true;
    }
  }

  async initialize() {
    if (this.isInitialized) return this.sequelize;

    try {
      // Check if we've already tried too many times in a short period
      if (this.connectionAttempts >= this.maxConnectionAttempts) {
        console.error(`Too many connection attempts (${this.connectionAttempts}). Waiting before trying again.`);
        await new Promise(resolve => setTimeout(resolve, 5000));
        this.connectionAttempts = 0;
      }
      
      this.connectionAttempts++;
      
      await this.sequelize.authenticate();
      this.setupPeriodicConnectionCheck();
      this.isInitialized = true;
      this.connectionAttempts = 0; // Reset counter on success
      return this.sequelize;
    } catch (error) {
      console.error('Unable to connect to the database:', error);
      
      // Handle too many connections error
      if (error.original && error.original.code === 'ER_CON_COUNT_ERROR') {
        console.error('Too many connections error detected during initialization');
        
        // Force close existing connections
        try {
          await this.sequelize.close();
        } catch (closeError) {
          console.error('Error closing connections:', closeError);
        }
        
        // For local environment, restart the server
        if (appEnv === 'local') {
          console.error('Restarting Next.js server due to too many connections...');
          this.restartNextServer();
          return this.sequelize;
        }
      }
      
      return this.reconnect();
    }
  }

  setupPeriodicConnectionCheck() {
    // Clear any existing interval to prevent duplicates
    if (this.connectionCheckInterval) {
      clearInterval(this.connectionCheckInterval);
    }
    
    const intervalId = setInterval(async () => {
      try {
        await this.sequelize.query('SELECT 1');
      } catch (error) {
        clearInterval(intervalId);
        this.isInitialized = false;
        
        // Handle too many connections error
        if (error.original && error.original.code === 'ER_CON_COUNT_ERROR') {
          console.error('Too many connections error detected during periodic check');
          
          // Force close existing connections
          try {
            await this.sequelize.close();
          } catch (closeError) {
            console.error('Error closing connections:', closeError);
          }
          
          if (appEnv === 'local') {
            console.error('Restarting Next.js server due to too many connections...');
            this.restartNextServer();
          } else {
            this.reconnect();
          }
        } else {
          this.reconnect();
        }
      }
    }, 60000);

    this.connectionCheckInterval = intervalId;
  }

  async reconnect(attempt = 1, maxAttempts = 5, initialDelay = 5000) {
    if (attempt > maxAttempts) {
      console.error(`Failed to reconnect after ${maxAttempts} attempts`);
      if (appEnv === 'local') {
        this.restartNextServer();
      }
      return this.sequelize;
    }

    const delay = initialDelay * Math.pow(1.5, attempt - 1);

    await new Promise(resolve => setTimeout(resolve, delay));

    try {
      await this.sequelize.authenticate();
      this.isInitialized = true;
      this.setupPeriodicConnectionCheck();
      return this.sequelize;
    } catch (error) {
      console.error(`Reconnection attempt ${attempt} failed:`, error);

      // Handle too many connections error
      if (error.original && error.original.code === 'ER_CON_COUNT_ERROR') {
        console.error('Too many connections error detected during reconnection');
        
        // Force close existing connections
        try {
          await this.sequelize.close();
        } catch (closeError) {
          console.error('Error closing connections:', closeError);
        }
        
        if (appEnv === 'local') {
          console.error('Restarting Next.js server due to too many connections...');
          this.restartNextServer();
          return this.sequelize;
        }
      }

      return this.reconnect(attempt + 1, maxAttempts, initialDelay);
    }
  }

  restartNextServer() {
    try {
      // For local environment
      if (appEnv === 'local') {
        console.error('Forcefully stopping Next.js server...');
        
        // First kill any existing Next.js processes
        exec('pkill -9 -f "node.*next"', (error, stdout, stderr) => {
          if (error && error.code !== 1) { // code 1 means no process found, which is fine
            console.error(`Error stopping Next.js server: ${error.message}`);
          }
          
          // Also kill any processes using the Next.js port
          exec('lsof -ti:6002 | xargs kill -9', (error, stdout, stderr) => {
            if (error && error.code !== 1) {
              console.error(`Error killing processes on port 6002: ${error.message}`);
            }
            
            // Wait a moment to ensure processes are fully terminated
            setTimeout(() => {
              console.error('Starting Next.js server again...');
              // Use spawn instead of exec to properly handle stdio and keep the process running
              const child = require('child_process').spawn('npm', ['run', 'dev'], {
                detached: true,
                stdio: ['ignore', 'inherit', 'inherit'],
                shell: true,
                cwd: process.cwd()
              });
              
              // Unref the child to allow the parent process to exit independently
              child.unref();
              
              console.error('Next.js server restart initiated with PID:', child.pid);
            }, 2000);
          });
        });
      }
    } catch (error) {
      console.error('Failed to restart Next.js server:', error);
    }
  }

  getTransaction = async (options = {}) => {
    const transactionOptions = {
      isolationLevel: Sequelize.Transaction.ISOLATION_LEVELS.READ_COMMITTED,
      timeout: 60000,
      ...options
    };

    let attempts = 0;
    const maxAttempts = 5;

    while (attempts < maxAttempts) {
      try {
        return await this.sequelize.transaction(transactionOptions);
      } catch (error) {
        attempts++;

        if (attempts >= maxAttempts) {
          console.error(`Failed to get transaction after ${maxAttempts} attempts:`, error);

          // Handle too many connections error
          if (error.original && error.original.code === 'ER_CON_COUNT_ERROR') {
            console.error('Too many connections error detected during transaction');
            
            // Force close existing connections
            try {
              await this.sequelize.close();
            } catch (closeError) {
              console.error('Error closing connections:', closeError);
            }
            
            if (appEnv === 'local') {
              console.error('Restarting Next.js server due to too many connections...');
              this.restartNextServer();
            }
          }

          throw error;
        }

        if (error.name === 'SequelizeConnectionAcquireTimeoutError' || 
            (error.original && error.original.code === 'ER_CON_COUNT_ERROR')) {
          const backoffTime = 1000 * Math.pow(2, attempts);
          await new Promise(resolve => setTimeout(resolve, backoffTime));
        } else {
          throw error;
        }
      }
    }
  }
}

const db = new Database();
const sequelize = db.sequelize;

sequelize.getTransaction = db.getTransaction.bind(db);

db.initialize().then(() => {
  // Database initialized successfully
}).catch(err => {
  console.error('Database initialization error:', err);
  // Handle too many connections error
  if (err.original && err.original.code === 'ER_CON_COUNT_ERROR') {
    db.restartNextServer();
  }
});

export default sequelize;