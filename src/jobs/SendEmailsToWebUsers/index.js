import { Helper<PERSON><PERSON>roller } from '@/sql/controllers';

const generateWelcomeEmailHtml = ({ link, profileLink }) => {
	return `
<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="color-scheme" content="dark light">
	<title>Wetrade / Newsletter</title>
	<style>
		:root { color-scheme: light dark; }
	    @media only screen and ( max-width: 559px) {
	    	.family_content { font-size: 26px !important; }
	    	body { background-color: #050023 !important; }
	    }
	    @media only screen and ( min-width: 401px ) and ( max-width: 559px ) {
	    	.text { font-size: 18px !important; line-height: 26px !important; }
	    	.invite_box { padding: 0 30px !important; }
	    	.footer_main { padding: 50px 30px 10px !important; }
	    	.winning_text { font-size: 28px !important; line-height: 36px !important; }
	    	.winning_pricing { font-size: 50px !important; }
	    	.things_heading { font-size: 23px !important; line-height: 29px !important; }
	    }
	    @media only screen and ( max-width: 400px ) {
	    	.text { font-size: 16px !important; line-height: 24px !important; }
	    	.invite_box { padding: 0 20px !important; }
	    	.footer_main { padding: 50px 20px 10px !important; }
	    	.footer_main table tr td br { display: none; }
	    	.winning_text { font-size: 24px !important; line-height: 32px !important; }
	    	.winning_pricing { font-size: 44px !important; }
	    	.things_heading { font-size: 20px !important; line-height: 26px !important; }
	    }
	    @media only screen and ( max-width: 374px ) {
	    	table tr td br { display: none; }
			.welcome_box { padding: 50px 15px 30px !important; }
	    }
	    @media (prefers-color-scheme: dark) {
			.email_bg { background: linear-gradient(46deg, #240077 0%, #240077 20%, #050023 56.35%, #050023 100%) !important; }
		  	.white_text { color: #fff !important; }
		  	.email_btn { background-color: #96FA02 !important; color: #000 !important; }
		  	.colors_text { color: #96FA02 !important; }
		  	.colors_heading { color: #8C6CD8 !important; }
		  	.footer_main { background-color: #F1F1F1 !important; }
		  	.color_black { color: #000 !important; }
		}
  </style>
</head>
<body style="margin: 0; padding: 0; list-style: none;">
<div class="wetrade_wrapp" style="max-width: 600px; background-color: #050023; margin: 0 auto;">
	<table cellpadding="0" cellspacing="0" border="0" width="100%" style="border-spacing: 0; border: none; background-color: #050023 !important; background-image: url('https://wetrade-media-dev.s3.amazonaws.com/images/webimages/newsletter_bg.png'); background-size: cover; background-repeat: no-repeat; background-position: center;">
      	<tr>
        	<td align="center" valign="middle">
          		<table cellpadding="0" cellspacing="0" border="0" width="100%" style="border-spacing: 0; border: none;">
				  	<tr align="center" valign="middle">
			        	<td align="center" valign="middle" style="padding-top: 35px; padding-bottom: 30px;">
			        		<img style="max-width: 100%;" src="https://wetrade-media-dev.s3.amazonaws.com/images/webimages/wetrade_logo.png" alt="wetrade">
			        	</td>
			        </tr>
	            	<tr align="center" valign="middle">
	            		<td align="center" valign="middle">
	            			<table cellpadding="0" cellspacing="0" border="0" width="100%" style="border-spacing: 0; border: none;">
		            			<tr align="center" valign="middle">
			            			<td class="email_bg" align="center" valign="middle" style="background: linear-gradient(46deg, #240077 0%, #240077 20%, #050023 56.35%, #050023 100%) !important; padding: 10px 10px 0;">
			            				<img style="max-width: 100%;" src="https://wetrade-media-dev.s3.amazonaws.com/images/webimages/mobile-img.png" alt="mobile-img">
			            			</td>
			            		</tr>
				           	</table>
	            		</td>
			        </tr>
					<tr align="center" valign="middle">
              			<td class="welcome_box" align="center" valign="middle" style="padding: 50px 40px 30px">
              				<h1 class="white_text" style="font-size: 45px; line-height: 47px; font-weight: 600; color: #ffffff; margin: 0;">Welcome</h1>
              				<h3 class="family_content white_text" style="font-size: 32px; line-height: 47px; color: #ffffff; margin: 0;">To The <span class="colors_heading" style="color: #8C6CD8;">WeTrade</span> Family!</h3>
              			</td>
            		</tr>
			        <tr align="center" valign="top">
			        	<td style="background-image: url('https://wetrade-media-dev.s3.amazonaws.com/images/webimages/about-us-bg.png'); background-repeat: no-repeat; background-size: cover; background-position: center bottom; padding: 0 15px;">
			        		<table cellpadding="0" cellspacing="0" border="0" width="100%" style="border-spacing: 0; border: none;">
			        			<tr align="center" valign="top">
			        				<td class="text white_text" align="center" valign="top" style="font-size: 20px; font-weight: 700; line-height: 26px; color: #ffffff; padding-top: 45px;">
										Thanks so much for joining our waitlist. <br /> If you haven't already, <br /> you can claim you username <a class="colors_text" style="color: #96FA02;" href="${link}">here</a>
									</td>
			        			</tr>
			        			<tr align="center" valign="top">
			        				<td class="text white_text" align="center" valign="top" style="font-size: 20px; font-weight: 700; line-height: 26px; color: #ffffff; padding-top: 90px;">
										Your early support is a tremendous <br /> help and it means the world to us.
									</td>
			        			</tr>
			        			<tr align="center" valign="top">
			        				<td class="text white_text" align="center" valign="top" style="font-size: 20px; font-weight: 700; line-height: 26px; color: #ffffff; padding-top: 80px;">
										We couldn't continue without the support of our <br /> early adopters such as yourself, which is why…
									</td>
			        			</tr>
			        		</table>
			        	</td>
			        </tr>
			        <tr align="center" valign="top">
			        	<td align="center" valign="middle" style="padding: 80px 20px 45px">
			        		<h3 class="winning_text white_text" style="font-size: 34px; font-weight: 400; line-height: 44px; color: #fff; margin: 0;">We want you to receive perks and a chance of winning</h3>
			        		<h1 class="winning_pricing colors_text" style="font-size: 60px; line-height: 74px; color: #96FA02; font-weight: 700; margin: 0;">$5,000</h1>
			        	</td>
			        </tr>
			        <tr align="center" valign="top">
			        	<td class="colors_heading" style="padding: 0 15px 10px; color: #8C6CD8; font-size: 25px; font-weight: 700; line-height: 44px;">We've kept things simple</td>
			        </tr>
			        <tr align="left" valign="middle">
			        	<td class="invite_box" align="left" valign="middle" style="padding: 0 85px">
			        		<table cellpadding="0" cellspacing="0" border="0" width="100%" style="border-spacing: 0; border: none;">
			        			<tr align="left" valign="middle">
			        				<td align="left" valign="middle">
			        					<div style="display: flex; justify-content: left; align-content: start; padding-bottom: 45px;">
			        						<div style="padding-right: 30px; padding-top: 20px;">
			        							<img style="max-width: 100%;" src="https://wetrade-media-dev.s3.amazonaws.com/images/webimages/icon-img1.png" alt="icon-img" />
			        						</div>
			        						<div>
			        							<h6 class="things_heading white_text" style="font-size: 25px; font-weight: 600; line-height: 44px; color: #ffffff; margin: 0; padding-bottom: 5px;">Share your link</h6>
			        							<p class="text white_text" style="font-size: 20px; line-height: 30px; color: #fff; margin: 0;">Invite your friends using your <br /> personal invite link.</p>
			        						</div>
			        					</div>
			        				</td>
			        			</tr>
			        			<tr align="left" valign="middle">
			        				<td align="left" valign="middle">
			        					<div style="display: flex; justify-content: left; align-content: start; padding-bottom: 45px;">
			        						<div style="padding-right: 30px; padding-top: 20px;">
			        							<img style="max-width: 100%;" src="https://wetrade-media-dev.s3.amazonaws.com/images/webimages/icon-img2.png" alt="icon-img" />
			        						</div>
			        						<div>
			        							<h6 class="things_heading white_text" style="font-size: 25px; font-weight: 600; line-height: 44px; color: #ffffff; margin: 0; padding-bottom: 5px;">Earn Points</h6>
			        							<p class="text white_text" style="font-size: 20px; line-height: 30px; color: #fff; margin: 0;">The more friends you refer, <br /> the more points you earn.</p>
			        						</div>
			        					</div>
			        				</td>
			        			</tr>
			        			<tr align="left" valign="middle">
			        				<td align="left" valign="middle">
			        					<div style="display: flex; justify-content: left; align-content: start; padding-bottom: 45px;">
			        						<div style="padding-right: 30px; padding-top: 20px;">
			        							<img style="max-width: 100%;" src="https://wetrade-media-dev.s3.amazonaws.com/images/webimages/icon-img3.png" alt="icon-img" />
			        						</div>
			        						<div>
			        							<h6 class="things_heading white_text" style="font-size: 25px; font-weight: 600; line-height: 44px; color: #ffffff; margin: 0; padding-bottom: 5px;">Unlock Perks and Prizes</h6>
			        							<p class="text white_text" style="font-size: 20px; line-height: 30px; color: #fff; margin: 0;">Use your points to receive <br /> perks and prizes.</p>
			        						</div>
			        					</div>
			        				</td>
			        			</tr>
			        			<tr align="left" valign="middle">
			        				<td align="left" valign="middle">
			        					<div style="display: flex; justify-content: left; align-content: start; padding-bottom: 45px;">
			        						<div style="padding-right: 30px; padding-top: 20px;">
			        							<img style="max-width: 100%;" src="https://wetrade-media-dev.s3.amazonaws.com/images/webimages/icon-img4.png" alt="icon-img" />
			        						</div>
			        						<div>
			        							<h6 class="things_heading white_text" style="font-size: 25px; font-weight: 600; line-height: 44px; color: #ffffff; margin: 0; padding-bottom: 5px;">Win $5k</h6>
			        							<p class="text white_text" style="font-size: 20px; line-height: 30px; color: #fff; margin: 0;">The top 10 refers with the most <br /> points get a change to win $5k.</p>
			        						</div>
			        					</div>
			        				</td>
			        			</tr>
			        			<tr align="center" valign="middle">
			        				<td align="center" valign="middle">
			        					<a class="email_btn" style="background-color: #96FA02; font-size: 20px; color: #000; padding: 20px 50px; display: inline-block; text-decoration: none; font-weight: 500; line-height: 120%; border-radius: 50px;" href="${profileLink}">Get your link and share</a>
			        				</td>
			        			</tr>
			        			<tr align="center" valign="middle">
			        				<td align="center" valign="middle" style="padding: 50px 0px 70px; color: #fff; font-size: 18px; font-weight: 500; color: #ffffff; line-height: 28px;">
			        					Anyone who signs up using your link will also get early access, be able to claim their username, and get a chance at winning more prizes and perks.
			        				</td>
			        			</tr>
			        		</table>
			        	</td>
			        </tr>
			        <tr>
			        	<td class="footer_main" style="background-color: #F1F1F1; padding: 50px 40px 10px"> 
			        		<table cellpadding="0" cellspacing="0" border="0" width="100%" style="border-spacing: 0; border: none;">
			        			<tr align="center" valign="middle">
			        				<td align="center" valign="middle">
			        					<a class="color_black" href="https://www.facebook.com/profile.php?id=100084169448132" style="padding: 0 10px; text-decoration: none;">
			        						<img style="max-width: 100%;" src="https://wetrade-media-dev.s3.amazonaws.com/images/webimages/facebook.png" alt="facebook">
			        					</a>
			        					<a class="color_black" href="https://twitter.com/wetradeinc" style="padding: 0 10px; text-decoration: none;">
			        						<img style="max-width: 100%;" src="https://wetrade-media-dev.s3.amazonaws.com/images/webimages/twitter.png" alt="twitter">
			        					</a>
			        					<a class="color_black" href="https://www.instagram.com/wetradeapp/" style="padding: 0 10px; text-decoration: none;">
			        						<img style="max-width: 100%;" src="https://wetrade-media-dev.s3.amazonaws.com/images/webimages/instagram.png" alt="instagram">
			        					</a>
			        					<a class="color_black" href="https://www.linkedin.com/company/wetrade-inc/" style="padding: 0 10px; text-decoration: none;">
			        						<img style="max-width: 100%;" src="https://wetrade-media-dev.s3.amazonaws.com/images/webimages/linkdin.png" alt="linkdin">
			        					</a>
			        				</td>
			        			</tr>
			        			<tr align="center" valign="middle">
			        				<td class="color_black" align="center" valign="middle" style="font-size: 16px; color: #050023; line-height: 24px; padding: 30px 0 10px">
			        					@Copyright WeTrade Inc, All rights reserved. <br />
										You recieved this email because you opted in via our website. <br />
										Don't like these emails?
			        				</td>
			        			</tr>
			        			<tr align="center" valign="middle">
			        				<td align="center" valign="middle" style="font-size: 16px; line-height: 24px; padding: 0px 0px 35px">
			        					<a class="colors_heading" style="color: #8C6CD8;" href="#">Unsubscribe</a>
			        				</td>
			        			</tr>
			        			<tr class="color_black" align="center" valign="middle">
			        				<td align="center" valign="middle" style="font-size: 16px; color: #69667B; line-height: 24px; padding: 0px">
			        					1317 Edgewater Dr, #5782, Orlando, FL 32804
			        				</td>
			        			</tr>
			        		</table>
			        	</td>
			        </tr>
          		</table>
        	</td>
      	</tr>
    </table>
</div>
</body>
</html>
    `;
}

class SendEmailJob {
	static async handleBulk(userInfos) {
		const subject = 'Welcome to WeTrade!';
		await Promise.all(
			userInfos.map(({ email, link, profileLink }) => {
				const html = generateWelcomeEmailHtml({ link, profileLink });
				return HelperController.sendEmail(email, subject, html);
			})
		);
	}
}

export default SendEmailJob;