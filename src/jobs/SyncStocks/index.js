import axios from 'axios';
import models from '@/sql/models';
const { stocks } = models;

class SyncStocksQueue {
    constructor() {
        this.jobs = [];
        this.processing = false;
        this.activeJobs = 0;
        this.maxWorkers = 3;
    }

    add(job) {
        this.jobs.push(job);
        if (!this.processing) this.process();
    }

    async process() {
        this.processing = true;

        while (this.jobs.length > 0 && this.activeJobs < this.maxWorkers) {
            const job = this.jobs.shift();
            this.activeJobs++;

            try {
                await this.executeJob(job);
            } catch (error) {
            } finally {
                this.activeJobs--;
                if (job.onComplete) job.onComplete();
                if (this.jobs.length > 0) this.process();
                else this.processing = false;
            }
        }
        if (this.jobs.length > 0 && this.activeJobs >= this.maxWorkers) {
            setTimeout(() => this.process(), 200);
        }
    }

    async executeJob(job) {
        if (job.type === 'logos') return this.fetchLogos(job.data);
        if (job.type === 'insert') return this.insertStocks(job.data);
        return false;
    }

    async fetchLogos({ stocks: stocksToUpdate, token }) {
        const logoUpdates = [];
        for (let i = 0; i < stocksToUpdate.length; i += 5) {
            const batch = stocksToUpdate.slice(i, i + 5);

            const batchPromises = batch.map(stock =>
                axios.get(`https://finnhub.io/api/v1/stock/profile2?symbol=${stock.symbol}&token=${token}`, { timeout: 10000 })
                    .then(({ data }) => data?.logo ? { id: stock.id, logo: data.logo, symbol: stock.symbol } : null)
                    .catch(err => {
                        return null;
                    })
            );
            const results = await Promise.allSettled(batchPromises);
            results
                .filter(r => r.status === 'fulfilled' && r.value)
                .forEach(r => logoUpdates.push(r.value));
            if (i + 5 < stocksToUpdate.length) await new Promise(resolve => setTimeout(resolve, 1000));
        }
        if (logoUpdates.length > 0) {
            try {
                for (const update of logoUpdates) {
                    await stocks.update(
                        { 
                            logo: update.logo,
                            createdAt: Math.round(Date.now() / 1000)
                        },
                        { 
                            where: { id: update.id }
                        }
                    );
                }
            } catch (err) {
                console.error('Error updating stock logos:', err);
            }
        }
        return true;
    }

    async insertStocks({ stocksData }) {
        if (!stocksData.length) return true;
        try {
            const batchSize = 300;
            for (let i = 0; i < stocksData.length; i += batchSize) {
                const batch = stocksData.slice(i, i + batchSize);
                await stocks.bulkCreate(batch, { ignoreDuplicates: true });
            }
            return true;
        } catch (error) {
            throw error;
        }
    }
}

export default SyncStocksQueue;