import models from '@/sql/models';
import { sendResponse } from "@/utils/helper";
import { Op } from 'sequelize';
import messages from '@/utils/messages';

const { users, comments } = models;

class CommentController {
    static async getList(request) {
        try {
            const params = new URLSearchParams(new URL(request.url).search);
            const id = params.get('id');
            const page = parseInt(params.get('page')) || 1;
            const perPage = parseInt(params.get('per_page')) || 10;
            const offset = (page - 1) * perPage;
            const excludeAttributes = ['platform', 'version', 'repliedUserId', 'reportCount'];

            const { rows: mainComments, count: totalCount } = await comments.findAndCountAll({
                where: { postId: id, parentId: 0 },
                attributes: { exclude: excludeAttributes },
                include: [{
                    model: users,
                    where: { isDelete: 0, roleId: { [Op.ne]: 1 } },
                    as: 'user',
                    attributes: ['id', 'userId', 'fullName', 'userName', 'profileImage'],
                }],
                offset,
                limit: perPage,
                order: [['createdAt', 'desc']],
                distinct: true
            });

            const commentsWithReplies = !mainComments.length ? [] : await (async () => {
                const commentIds = mainComments.map(comment => comment.id);
                const subComments = await comments.findAll({
                    where: { parentId: { [Op.in]: commentIds } },
                    attributes: { exclude: excludeAttributes },
                    include: [{
                        model: users,
                        where: { isDelete: 0, roleId: { [Op.ne]: 1 } },
                        as: 'user',
                        attributes: ['id', 'userId', 'fullName', 'userName', 'profileImage'],
                    }],
                    order: [['createdAt', 'asc']],
                });
                const subCommentsMap = {};
                subComments.forEach(comment => {
                    if (!subCommentsMap[comment.parentId]) {
                        subCommentsMap[comment.parentId] = [];
                    }
                    subCommentsMap[comment.parentId].push(comment.toJSON());
                });
                return mainComments.map(comment => {
                    const commentJson = comment.toJSON();
                    const replies = subCommentsMap[comment.id] || [];
                    return {
                        ...commentJson,
                        totalSubComments: replies.length,
                        replies
                    };
                });
            })();

            return sendResponse({
                status: 200,
                message: messages.SUCCESS,
                data: {
                    comments: commentsWithReplies,
                    pagination: {
                        total: totalCount,
                        page,
                        perPage,
                        totalPages: Math.ceil(totalCount / perPage)
                    }
                }
            });
        } catch (error) {
            return sendResponse({
                status: 500,
                message: messages.SOMETHING_WENT_WRONG || error.message,
            });
        }
    }

    static async deleteComment(request) {
        const transaction = await comments.sequelize.transaction();
        try {
            const formData = await request.formData();
            const { id } = Object.fromEntries(formData.entries());
            await Promise.all([
                comments.destroy({ where: { id }, transaction }),
                comments.destroy({ where: { parentId: id }, transaction })
            ]);
            await transaction.commit();
            return sendResponse({
                status: 200,
                message: messages.COMMENT_DELETED_SUCCESSFULLY,
            });
        } catch (error) {
            if (transaction && !transaction.finished) {
                await transaction.rollback();
            }
            return sendResponse({
                status: 500,
                message: messages.SOMETHING_WENT_WRONG || error.message,
            });
        }
    }

}

export default CommentController;