import models from '@/sql/models';
import { sendResponse, getSearchTerms } from "@/utils/helper";
import { Op, Sequelize } from 'sequelize';
import messages from '@/utils/messages';
import axios from 'axios';
import { SyncStocksQueue } from '@/jobs';

const { stocks, game_stocks } = models;

class StockController {
    static async getList(request) {
        try {
            const params = new URLSearchParams(new URL(request.url).search);
            const page = parseInt(params.get('page')) || 1;
            const perPage = parseInt(params.get('per_page')) || 10;
            const type = params.get('type') || '';
            const search = params.get('search') || '';
            const offset = (page - 1) * perPage;

            const conditions = {
                stockType: type === 'crypto' ? 'US' : { [Op.notLike]: 'US' },
                isDelisted: 0
            };
            let whereCondition = conditions;

            if (search.trim()) {
                const searchFields = [
                    { field: 'description', col: 'description' },
                    { field: 'currency', col: 'currency' },
                    { field: 'symbol', col: 'symbol' },
                    { field: 'type', col: 'type' }
                ];

                const exactSearch = search.trim();
                const exactMatchConditions = searchFields.map(({ field, col }) => ({
                    [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)),
                        Sequelize.fn('LOWER', exactSearch))
                }));

                const partialMatchConditions = searchFields.map(({ field, col }) => ({
                    [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)), {
                        [Op.like]: `%${exactSearch.toLowerCase()}%`
                    })
                }));

                let phraseConditions = [];
                if (search.includes(' ')) {
                    phraseConditions = searchFields.map(({ field, col }) => ({
                        [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)), {
                            [Op.like]: `%${exactSearch.toLowerCase()}%`
                        })
                    }));
                }

                const searchTerms = getSearchTerms(search).filter(term => term.trim());
                let termConditions = [];
                if (searchTerms.length) {
                    termConditions = searchTerms.map(term => {
                        const termLower = term.trim().toLowerCase();
                        return {
                            [Op.or]: searchFields.map(({ field, col }) => ({
                                [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)), {
                                    [Op.like]: `%${termLower}%`
                                })
                            }))
                        };
                    });
                }

                whereCondition = {
                    [Op.and]: [
                        {
                            [Op.or]: [
                                ...exactMatchConditions,
                                ...phraseConditions,
                                ...partialMatchConditions,
                                ...(termConditions.length ? [{ [Op.and]: termConditions }] : [])
                            ]
                        },
                        conditions
                    ]
                };
            }

            const [{ count, rows }, totalGameStocksCount] = await Promise.all([
                stocks.findAndCountAll({
                    where: whereCondition,
                    distinct: true,
                    offset,
                    limit: perPage,
                    order: [['isGameStock', 'desc']],
                    raw: true,
                }),
                stocks.count({
                    where: { stockType: type === 'crypto' ? 'US' : { [Op.notLike]: 'US' }, isGameStock: 1 },
                    raw: true
                })
            ]);
            return sendResponse({
                status: 200,
                message: messages.SUCCESS,
                data: { total: count, per_page: perPage, current_page: page, data: rows, totalGameStocksCount },
            });
        } catch (error) {
            return sendResponse({
                status: 500,
                message: messages.SOMETHING_WENT_WRONG || error.message,
            });
        }
    }

    static async updateStockStatus(request) {
        const transaction = await stocks.sequelize.transaction();
        try {
            const formData = await request.formData();
            const { id, status } = Object.fromEntries(formData.entries());
            const statusInt = parseInt(status);

            const stockRecord = await stocks.findOne({
                attributes: ['id', 'stockType'],
                where: { id },
                transaction
            });

            if (!stockRecord) {
                await transaction.rollback();
                return sendResponse({
                    status: 404,
                    message: messages.STOCK_NOT_FOUND || 'Stock not found'
                });
            }

            const stockType = stockRecord.stockType === 'US' ? 'crypto' : 'stock';
            const existingGameStock = await game_stocks.findOne({
                where: { stockId: id, type: stockType },
                transaction
            });

            if (statusInt === 1) {
                if (!existingGameStock || existingGameStock.isDelete === 1) {
                    const gameStockCount = await game_stocks.count({
                        where: { type: stockType, isDelete: 0 },
                        transaction
                    });

                    if (gameStockCount >= 25) {
                        await transaction.rollback();
                        return sendResponse({
                            status: 400,
                            message: messages.GAME_STOCK_MAX_LIMIT || 'Maximum limit of 25 game stocks reached.'
                        });
                    }
                }

                if (existingGameStock) {
                    await Promise.all([
                        game_stocks.update(
                            { isDelete: 0 },
                            { where: { id: existingGameStock.id }, transaction }
                        ),
                        stocks.update(
                            { isGameStock: 1 },
                            { where: { id }, transaction }
                        )
                    ]);
                } else {
                    await Promise.all([
                        game_stocks.create({
                            stockId: id,
                            type: stockType,
                            createdAt: Math.round(Date.now() / 1000)
                        }, { transaction }),
                        stocks.update(
                            { isGameStock: 1 },
                            { where: { id }, transaction }
                        )
                    ]);
                }
            } else if (existingGameStock) {
                await Promise.all([
                    game_stocks.update(
                        { isDelete: 1 },
                        { where: { id: existingGameStock.id }, transaction }
                    ),
                    stocks.update(
                        { isGameStock: 0 },
                        { where: { id }, transaction }
                    )
                ]);
            }

            await transaction.commit();
            return sendResponse({
                status: 200,
                message: messages[`STOCK_${statusInt === 1 ? 'ADDED_TO' : 'REMOVED_FROM'}_GAME_STOCK_SUCCESSFULLY`],
            });
        } catch (error) {
            if (transaction && !transaction.finished) await transaction.rollback();
            return sendResponse({
                status: error.status || 500,
                message: error.message || messages.SOMETHING_WENT_WRONG,
            });
        }
    }

    static async syncStocks(request) {
        try {
            const formData = await request.formData();
            const { stockType } = Object.fromEntries(formData.entries());

            const STOCK_TYPES = new Set([
                "US", "BITFINEX", "FXPIG", "COINBASE", "BINANCEUS",
                "KRAKEN", "BITTREX", "BITMEX", "POLONIEX", "GEMINI",
                "KUCOIN", "HITBTC", "OKEX", "HUOBI", "BINANCE"
            ]);

            if (!stockType || !STOCK_TYPES.has(stockType)) return sendResponse({ status: 400, message: messages.INVALID_STOCK_TYPE });

            const FINHUB_TOKEN = process.env.FINHUB_TOKEN;

            if (!FINHUB_TOKEN) return sendResponse({ status: 400, message: messages.FINHUB_TOKEN_NOT_FOUND });

            const queue = new SyncStocksQueue();
            const isUS = stockType === 'US';

            const apiUrl = isUS
                ? `https://finnhub.io/api/v1/stock/symbol?exchange=US&token=${FINHUB_TOKEN}`
                : `https://finnhub.io/api/v1/crypto/symbol?exchange=${stockType}&token=${FINHUB_TOKEN}`;

            const { data: apiResponse } = await axios.get(apiUrl, { timeout: 10000 });
            const apiSymbols = new Set(apiResponse.map(s => s.symbol));
            const apiMap = new Map(apiResponse.map(s => [s.symbol, s]));

            const existingStocks = await stocks.findAll({
                where: {
                    stockType,
                    isDelisted: { [Op.or]: [0, null] }
                },
                attributes: ['id', 'symbol', 'logo'],
                raw: true
            });
            const dbSymbols = new Set(existingStocks.map(s => s.symbol));

            const newSymbols = [...apiSymbols].filter(x => !dbSymbols.has(x));
            const delistedSymbols = [...dbSymbols].filter(x => !apiSymbols.has(x));

            const newStocksData = newSymbols.map(symbol => {
                const stock = apiMap.get(symbol);
                return {
                    currency: isUS ? stock.currency || '' : '',
                    description: stock.description || '',
                    displaySymbol: stock.displaySymbol || stock.symbol,
                    symbol: stock.symbol,
                    stockType,
                    type: isUS ? stock.type || '' : '',
                    createdAt: Math.round(Date.now() / 1000),
                    logo: '',
                    isDelisted: 0
                };
            });

            if (delistedSymbols.length > 0) {
                await stocks.update(
                    { isDelisted: 1, createdAt: Math.round(Date.now() / 1000) },
                    { where: { symbol: { [Op.in]: delistedSymbols }, stockType } }
                );
            }

            let insertPromise = Promise.resolve();
            if (newStocksData.length > 0) {
                insertPromise = new Promise(resolve => {
                    queue.add({
                        type: 'insert',
                        data: { stocksData: newStocksData },
                        onComplete: resolve
                    });
                });
            }

            await insertPromise;

            let logoCount = 0;
            if (isUS) {
                const stocksWithoutLogos = await stocks.findAll({
                    where: {
                        stockType: 'US',
                        isDelisted: 0,
                        [Op.or]: [{ logo: null }, { logo: '' }]
                    },
                    attributes: ['id', 'symbol', 'logo'],
                    raw: true
                });

                logoCount = stocksWithoutLogos.length;

                if (logoCount > 0) {
                    const batchSize = 25;
                    for (let i = 0; i < logoCount; i += batchSize) {
                        queue.add({
                            type: 'logos',
                            data: {
                                stocks: stocksWithoutLogos.slice(i, i + batchSize),
                                token: FINHUB_TOKEN
                            }
                        });
                    }
                }
            }

            return sendResponse({
                status: 200,
                message: messages[`${isUS ? 'CRYPTO' : 'STOCK'}_DATA_SYNCED_SUCCESSFULLY`],
                data: {
                    newStocks: newSymbols.length,
                    delistedStocks: delistedSymbols.length,
                    logosToProcess: logoCount,
                    syncStatus: `Sync completed. ${newSymbols.length} new, ${delistedSymbols.length} delisted. ${isUS ? `${logoCount} logos queued.` : 'No logos for stocks.'}`
                }
            });

        } catch (error) {
            return sendResponse({
                status: 500,
                message: error.message || messages.SOMETHING_WENT_WRONG,
            });
        }
    }
}

export default StockController;