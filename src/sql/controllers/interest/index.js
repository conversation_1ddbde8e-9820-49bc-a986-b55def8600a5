import models from '@/sql/models';
import { sendResponse, getSearchTerms, getExcludedFields, generateRandomString, s3UploadFile, deleteS3Item } from "@/utils/helper";
import { Op } from 'sequelize';
import messages from '@/utils/messages';
import { HelperController } from '@/sql/controllers';

const { interests, stocks } = models;

class InterestController {
    static async getList(request) {
        try {
            const params = new URLSearchParams(new URL(request.url).search);
            const interestId = parseInt(params.get('interestId')) || 0;
            const page = parseInt(params.get('page')) || 1;
            const perPage = parseInt(params.get('per_page')) || 10;
            const search = params.get('search') || '';
            const offset = (page - 1) * perPage;

            const conditions = { parent_id: interestId };

            let whereCondition = conditions;
            if (search.trim()) {
                const searchFields = [
                    { field: 'name', col: 'name' }
                ];

                const exactSearch = search.trim();
                const exactMatchConditions = searchFields.map(({ field, col }) => ({
                    [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)),
                        Sequelize.fn('LOWER', exactSearch))
                }));

                const partialMatchConditions = searchFields.map(({ field, col }) => ({
                    [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)), {
                        [Op.like]: `%${exactSearch.toLowerCase()}%`
                    })
                }));

                let phraseConditions = [];
                if (search.includes(' ')) {
                    phraseConditions = searchFields.map(({ field, col }) => ({
                        [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)), {
                            [Op.like]: `%${exactSearch.toLowerCase()}%`
                        })
                    }));
                }

                const searchTerms = getSearchTerms(search).filter(term => term.trim());
                let termConditions = [];
                if (searchTerms.length) {
                    termConditions = searchTerms.map(term => {
                        const termLower = term.trim().toLowerCase();
                        return {
                            [Op.or]: searchFields.map(({ field, col }) => ({
                                [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)), {
                                    [Op.like]: `%${termLower}%`
                                })
                            }))
                        };
                    });
                }

                whereCondition = {
                    [Op.and]: [
                        {
                            [Op.or]: [
                                ...exactMatchConditions,
                                ...phraseConditions,
                                ...partialMatchConditions,
                                ...(termConditions.length ? [{ [Op.and]: termConditions }] : [])
                            ]
                        },
                        conditions
                    ]
                };
            }

            const include = interestId ? [{
                model: stocks,
                as: 'stockDetails',
                attributes: ['id', 'symbol', 'description'],
            }] : [];

            const { count, rows } = await interests.findAndCountAll({
                where: whereCondition,
                include,
                distinct: true,
                offset,
                limit: perPage,
                order: [['id', 'desc']],
                raw: true,
                nest: true,
            });

            return sendResponse({
                status: 200,
                message: messages.SUCCESS,
                data: { total: count, per_page: perPage, current_page: page, data: rows },
            });
        } catch (error) {
            return sendResponse({
                status: 500,
                message: messages.SOMETHING_WENT_WRONG || error.message,
            });
        }
    }

    static async updateInterestDetails(request) {
        const transaction = await interests.sequelize.transaction();
        try {
            const formData = await request.formData();
            const requestData = Object.fromEntries(formData.entries());
            const excludedFields = getExcludedFields(requestData);
            const parentId = requestData.parent_id && requestData.parent_id !== 0 ? parseInt(requestData.parent_id) : 0;
            const isSubInterest = requestData.isSubInterest || false;
            const interestWhereCondition = {
                parent_id: parentId,
            };

            const existingInterest = await interests.findOne({
                where: {
                    name: requestData.name,
                    ...(requestData.id && { id: { [Op.ne]: requestData.id } })
                },
                transaction
            });

            if (existingInterest) {
                return sendResponse({
                    status: 400,
                    message: existingInterest.parent_id === 0 ? messages.INTEREST_ALREADY_EXISTS : messages.SUB_INTEREST_ALREADY_EXISTS,
                });
            }

            let addDetails = requestData.id ?
                await interests.findOne({
                    where: {
                        id: requestData.id,
                        ...interestWhereCondition
                    },
                    transaction
                }) :
                new interests({
                    ...interestWhereCondition,
                    isActive: 1,
                    ordering: 0,
                    pageUrl: '',
                    viewType: '',
                });
            const fields = Object.keys(requestData).filter(key => !excludedFields.includes(key));
            fields.forEach(field => {
                if (requestData[field] !== undefined && requestData[field] !== null) {
                    addDetails[field] = requestData[field];
                }
            });
            addDetails.image = addDetails.image ?? '';
            addDetails.createdAt = addDetails?.createdAt || Math.round(Date.now() / 1000);
            const savedRecord = await addDetails.save({ transaction });
            const recordId = savedRecord.id || requestData.id;

            if (isSubInterest && requestData?.image === '' && addDetails?.image) {
                await deleteS3Item(addDetails.image);
                await interests.update(
                    { image: '' },
                    { where: { id: recordId }, transaction }
                );
            }

            if (isSubInterest && requestData?.image && typeof requestData.image === 'object') {
                const compressedImage = await HelperController.compressImage(requestData.image);
                if (compressedImage && compressedImage !== null) {
                    let uploadResult = await s3UploadFile(compressedImage, 'interest/images');
                    if (uploadResult) {
                        if (addDetails?.image) {
                            deleteS3Item(addDetails.image);
                        }
                        await interests.update(
                            { image: uploadResult },
                            { where: { id: recordId }, transaction }
                        );
                    } else {
                        await transaction.rollback();
                        return sendResponse({
                            status: 500,
                            message: messages.FILE_UPLOAD_FAILED,
                        });
                    }
                }
            }

            await transaction.commit();

            return sendResponse({
                status: 200,
                message: messages[`${parentId === 0 ? 'INTEREST' : 'SUB_INTEREST'}_DETAILS_${requestData.id ? 'UPDATED' : 'ADDED'}_SUCCESSFULLY`]
            });
        } catch (error) {
            if (transaction && !transaction.finished) {
                await transaction.rollback();
            }
            return sendResponse({
                status: error.status || 500,
                message: messages.SOMETHING_WENT_WRONG || error.message,
            });
        }
    }

    static async updateInterestStatus(request) {
        const transaction = await interests.sequelize.transaction();
        try {
            const formData = await request.formData();
            const { id, status } = Object.fromEntries(formData.entries());

            const currentInterest = await interests.findOne({ where: { id } });

            if (!currentInterest) {
                return sendResponse({
                    status: 404,
                    message: messages.INTEREST_NOT_FOUND,
                });
            }

            await interests.update(
                { isActive: parseInt(status) },
                { where: { id }, transaction }
            );

            await transaction.commit();

            return sendResponse({
                status: 200,
                message: messages[`${currentInterest?.parent_id === 0 ? 'INTEREST' : 'SUB_INTEREST'}_${parseInt(status) === 1 ? 'ACTIVATED' : 'DEACTIVATED'}_SUCCESSFULLY`],
            });
        } catch (error) {
            if (transaction && !transaction.finished) {
                await transaction.rollback();
            }
            return sendResponse({
                status: error.status || 500,
                message: error.message || messages.SOMETHING_WENT_WRONG,
            });
        }
    }
}

export default InterestController;