import models from '@/sql/models';
import { sendResponse, getSearchTerms, getExcludedFields } from "@/utils/helper";
import { Op } from 'sequelize';
import messages from '@/utils/messages';
import { HelperController } from '@/sql/controllers';

const { users, followers, userinterests, interests } = models;

class UserController {
    static async getList(request) {
        try {
            const params = new URLSearchParams(new URL(request.url).search);
            const page = parseInt(params.get('page')) || 1;
            const perPage = parseInt(params.get('per_page')) || 10;
            const search = params.get('search') || '';
            const offset = (page - 1) * perPage;

            let whereCondition = { isDelete: 0 };

            if (search.trim()) {
                const searchFields = [
                    { field: 'email', col: 'email' },
                    { field: 'userName', col: 'userName' },
                    { field: 'fullName', col: 'fullName' },
                    { field: 'phoneNumber', col: 'phoneNumber' }
                ];

                const exactSearch = search.trim();
                const exactMatchConditions = searchFields.map(({ field, col }) => ({
                    [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)),
                        Sequelize.fn('LOWER', exactSearch))
                }));

                const partialMatchConditions = searchFields.map(({ field, col }) => ({
                    [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)), {
                        [Op.like]: `%${exactSearch.toLowerCase()}%`
                    })
                }));

                let phraseConditions = [];
                if (search.includes(' ')) {
                    phraseConditions = searchFields.map(({ field, col }) => ({
                        [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)), {
                            [Op.like]: `%${exactSearch.toLowerCase()}%`
                        })
                    }));
                }

                const searchTerms = getSearchTerms(search).filter(term => term.trim());
                let termConditions = [];
                if (searchTerms.length) {
                    termConditions = searchTerms.map(term => {
                        const termLower = term.trim().toLowerCase();
                        return {
                            [Op.or]: searchFields.map(({ field, col }) => ({
                                [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)), {
                                    [Op.like]: `%${termLower}%`
                                })
                            }))
                        };
                    });
                }

                whereCondition = {
                    [Op.and]: [
                        {
                            [Op.or]: [
                                ...exactMatchConditions,
                                ...phraseConditions,
                                ...partialMatchConditions,
                                ...(termConditions.length ? [{ [Op.and]: termConditions }] : [])
                            ]
                        },
                        { isDelete: 0 }
                    ]
                };
            }

            const { count, rows } = await users.findAndCountAll({
                where: whereCondition,
                distinct: true,
                roleId: { [Op.ne]: 1 },
                offset,
                limit: perPage,
                order: [['updateAt', 'desc']],
                raw: true,
                nest: true,
            });

            return sendResponse({
                status: 200,
                message: messages.SUCCESS,
                data: { total: count, per_page: perPage, current_page: page, data: rows },
            });
        } catch (error) {
            return sendResponse({
                status: 500,
                message: messages.SOMETHING_WENT_WRONG || error.message,
            });
        }
    }

    static async getUserDetails(request) {
        try {
            const params = new URLSearchParams(new URL(request.url).search);
            const userId = params.get('userId');
            const userWhereCondition = {
                roleId: { [Op.ne]: 1 },
            };

            const userDetails = await users.findOne({
                where: {
                    userId: userId,
                    ...userWhereCondition
                }
            });

            if (!userDetails) {
                return sendResponse({
                    status: 404,
                    message: messages.USER_NOT_FOUND,
                    data: null
                });
            }

            const checkUserExists = await HelperController.checkUserExists(userDetails.id);
            if (checkUserExists.status !== 200) {
                return sendResponse({
                    status: checkUserExists.status,
                    message: checkUserExists.message,
                    data: null
                });
            }
            
            const [userFollowers, userFollowing, userInterests] = await Promise.all([
                followers.findAll({
                    where: { followingId: userId, status: 1 },
                    include: [{
                        model: users,
                        as: 'followerDetails',
                        attributes: ['userId', 'profileImage', 'fullName'],
                        where: userWhereCondition,
                        required: false
                    }],
                    raw: true,
                    nest: true
                }),
                followers.findAll({
                    where: { followerId: userId, status: 1 },
                    include: [{
                        model: users,
                        as: 'followingDetails',
                        attributes: ['userId', 'profileImage', 'fullName'],
                        where: userWhereCondition,
                        required: false
                    }],
                    raw: true,
                    nest: true
                }),
                userinterests.findAll({
                    where: { userId },
                    include: [{
                        model: interests,
                        where: { parent_id: { [Op.ne]: 0 }, isActive: 1 },
                        attributes: ['name'],
                        required: false
                    }],
                    raw: true,
                    nest: true
                })
            ]);

            const result = userDetails.toJSON();
            result.interests = userInterests.length > 0
                ? userInterests.filter(i => i.interest?.name).map(i => ({ name: i.interest.name }))
                : [];
            result.followers = userFollowers.length > 0
                ? userFollowers.filter(f => f.followerDetails?.userId)
                : [];
            result.followings = userFollowing.length > 0
                ? userFollowing.filter(f => f.followingDetails?.userId)
                : [];

            return sendResponse({
                status: 200,
                message: messages.SUCCESS,
                data: result
            });
        } catch (error) {
            return sendResponse({
                status: 500,
                message: messages.SOMETHING_WENT_WRONG || error.message,
            });
        }
    }

    static async updateUserDetails(request) {
        const transaction = await users.sequelize.transaction();
        try {
            const formData = await request.formData();
            const requestData = Object.fromEntries(formData.entries());
            const excludedFields = getExcludedFields(requestData);

            const [existingEmail, existingUserName] = await Promise.all([
                users.findOne({
                    where: {
                        email: requestData.email,
                        ...(requestData.id && { id: { [Op.ne]: requestData.id } })
                    }
                }),
                users.findOne({
                    where: {
                        userName: requestData.userName,
                        ...(requestData.id && { id: { [Op.ne]: requestData.id } })
                    }
                })
            ]);

            if (existingEmail || existingUserName) {
                return sendResponse({
                    status: 400,
                    message: messages[`${existingEmail ? 'EMAIL' : 'USERNAME'}_ALREADY_EXISTS`],
                });
            }

            let addDetails = requestData.id ?
                await users.findOne({
                    where: {
                        id: requestData.id,
                        userId: requestData.userId
                    },
                    transaction
                }) :
                new users({
                    isDelete: 0,
                    roleId: 2,
                    isActive: 1,
                    isPrivate: 0,
                });

            const fields = Object.keys(requestData).filter(key => !excludedFields.includes(key));
            fields.forEach(field => {
                if (requestData[field] !== undefined && requestData[field] !== null) {
                    addDetails[field] = requestData[field];
                }
            });

            await addDetails.save({ transaction });
            await transaction.commit();

            return sendResponse({
                status: 200,
                message: messages[`USER_DETAILS_${requestData.id ? 'UPDATED' : 'ADDED'}_SUCCESSFULLY`],
            });
        } catch (error) {
            if (transaction && !transaction.finished) {
                await transaction.rollback();
            }
            return sendResponse({
                status: error.status || 500,
                message: messages.SOMETHING_WENT_WRONG || error.message,
            });
        }
    }

    static async updateUserStatus(request) {
        const transaction = await users.sequelize.transaction();
        try {
            const formData = await request.formData();
            const { id, status } = Object.fromEntries(formData.entries());

            const currentUser = await users.findOne({ where: { id } });

            if (!currentUser) {
                return sendResponse({
                    status: 404,
                    message: messages.USER_NOT_FOUND,
                });
            }

            await users.update(
                { isActive: parseInt(status) },
                { where: { id }, transaction }
            );

            await transaction.commit();

            return sendResponse({
                status: 200,
                message: messages[`USER_${parseInt(status) === 1 ? 'ACTIVATED' : 'DEACTIVATED'}_SUCCESSFULLY`],
            });
        } catch (error) {
            if (transaction && !transaction.finished) {
                await transaction.rollback();
            }
            return sendResponse({
                status: error.status || 500,
                message: error.message || messages.SOMETHING_WENT_WRONG,
            });
        }
    }

    static async deleteUser(request) {
        const transaction = await users.sequelize.transaction();
        try {
            const formData = await request.formData();
            const { id } = Object.fromEntries(formData.entries());

            const currentUser = await users.findOne({ where: { id } });

            if (!currentUser) {
                return sendResponse({
                    status: 404,
                    message: messages.USER_NOT_FOUND,
                });
            }

            await users.update(
                { isDelete: 1 },
                { where: { id }, transaction }
            );

            await transaction.commit();

            return sendResponse({
                status: 200,
                message: messages.USER_DELETED_SUCCESSFULLY,
            });
        } catch (error) {
            if (transaction && !transaction.finished) {
                await transaction.rollback();
            }
            return sendResponse({
                status: error.status || 500,
                message: messages.SOMETHING_WENT_WRONG || error.message,
            });
        }
    }
}

export default UserController;