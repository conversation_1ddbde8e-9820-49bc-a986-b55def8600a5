import models from '@/sql/models';
import { sendResponse } from "@/utils/helper";
import messages from '@/utils/messages';
import { Op } from 'sequelize';

const { users, interests, stocks, games, posts, web_users } = models;

class DashboardController {
    static async getStatistics() {
        try {
            const countQueries = {
                total_users: users.count({ where: { isDelete: 0 } }),
                total_active_users: users.count({ where: { isDelete: 0, isActive: 1 } }),
                total_inactive_users: users.count({ where: { isDelete: 0, isActive: 0 } }),
                total_interests: interests.count({ where: { isActive: 1, parent_id: 0 } }),
                total_stocks: stocks.count({ where: { stockType: { [Op.ne]: 'US' } } }),
                total_crypto: stocks.count({ where: { stockType: 'US' } }),
                total_ideas: posts.count({ where: { contentType: 'idea', isDelete: 0, isActive: 1, parentId: 0 } }),
                total_web_users: web_users.count({ where: { fakeUser: 0 } }),
                active_games: games.count({
                    where: {
                        isDelete: 0,
                        endTimeUtc: { [Op.gt]: new Date() }
                    }
                })
            };

            const result = {};
            await Promise.all(
                Object.entries(countQueries).map(async ([key, query]) => {
                    result[key] = await query || 0;
                })
            );

            return sendResponse({
                status: 200,
                message: messages.SUCCESS,
                data: result,
            });
        } catch (error) {            
            return sendResponse({
                status: 500,
                message: messages.SOMETHING_WENT_WRONG || error.message
            });
        }
    }
}

export default DashboardController;