import models from '@/sql/models';
import { sendResponse, getSearchTerms, getExcludedFields, validateBulkUploadHeaders, generateRandomString } from "@/utils/helper";
import { Op, Sequelize } from 'sequelize';
import messages from '@/utils/messages';
import * as XLSX from 'xlsx';
import Papa from 'papaparse';
import { SendEmailsToWebUsers } from '@/jobs';

const { web_users, user_reward_history } = models;

class WebUserController {
    static async getList(request) {
        try {
            const params = new URLSearchParams(new URL(request.url).search);
            const page = parseInt(params.get('page')) || 1;
            const perPage = parseInt(params.get('per_page')) || 10;
            const search = params.get('search') || '';
            const offset = (page - 1) * perPage;

            let whereCondition = {};

            if (search.trim()) {
                const searchFields = [
                    { field: 'email', col: 'email' },
                    { field: 'userName', col: 'userName' },
                    { field: 'fullName', col: 'fullName' }
                ];

                const exactSearch = search.trim();
                const exactMatchConditions = searchFields.map(({ field, col }) => ({
                    [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)),
                        Sequelize.fn('LOWER', exactSearch))
                }));

                const partialMatchConditions = searchFields.map(({ field, col }) => ({
                    [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)), {
                        [Op.like]: `%${exactSearch.toLowerCase()}%`
                    })
                }));

                let phraseConditions = [];
                if (search.includes(' ')) {
                    phraseConditions = searchFields.map(({ field, col }) => ({
                        [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)), {
                            [Op.like]: `%${exactSearch.toLowerCase()}%`
                        })
                    }));
                }

                const searchTerms = getSearchTerms(search).filter(term => term.trim());
                let termConditions = [];
                if (searchTerms.length) {
                    termConditions = searchTerms.map(term => {
                        const termLower = term.trim().toLowerCase();
                        return {
                            [Op.or]: searchFields.map(({ field, col }) => ({
                                [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)), {
                                    [Op.like]: `%${termLower}%`
                                })
                            }))
                        };
                    });
                }

                whereCondition = {
                    [Op.and]: [
                        {
                            [Op.or]: [
                                ...exactMatchConditions,
                                ...phraseConditions,
                                ...partialMatchConditions,
                                ...(termConditions.length ? [{ [Op.and]: termConditions }] : [])
                            ]
                        }
                    ]
                };
            }

            const { count, rows } = await web_users.findAndCountAll({
                where: whereCondition,
                distinct: true,
                offset,
                limit: perPage,
                order: [['created_at', 'desc']],
                raw: true,
                nest: true,
            });

            return sendResponse({
                status: 200,
                message: messages.SUCCESS,
                data: { total: count, per_page: perPage, current_page: page, data: rows },
            });
        } catch (error) {
            return sendResponse({
                status: 500,
                message: messages.SOMETHING_WENT_WRONG || error.message,
            });
        }
    }

    static async getWebUserDetails(request) {
        try {
            const params = new URLSearchParams(new URL(request.url).search);
            const userId = params.get('userId');
            const userDetails = await web_users.findOne({
                where: { id: userId },
                attributes: ['id', 'email', 'userName', 'fullName', 'created_at', 'pageUrl', 'referralCode']
            });

            if (!userDetails) {
                return sendResponse({
                    status: 404,
                    message: messages.USER_DETAILS_NOT_FOUND,
                    data: null
                });
            }

            const [referralUsers, rewardHistory, totalPoints] = await Promise.all([
                web_users.findAll({
                    where: { referralBy: userId },
                    attributes: ['id', 'email', 'userName', 'fullName', 'created_at', 'pageUrl', 'referralCode'],
                    raw: true,
                    nest: true
                }),
                user_reward_history.findAll({
                    where: { webuser_id: userId },
                    attributes: ['id', 'webuser_id', 'points', 'message', 'created_at'],
                    raw: true,
                    nest: true
                }),
                user_reward_history.findAll({
                    attributes: [
                        [Sequelize.fn('SUM', Sequelize.col('points')), 'totalPoints']
                    ],
                    where: {
                        webuser_id: userId
                    },
                    raw: true
                })
            ]);

            const webUserDetails = userDetails.toJSON();
            webUserDetails.referralUsers = referralUsers.length > 0 ? referralUsers : [];
            webUserDetails.rewardHistory = rewardHistory.length > 0 ? rewardHistory : [];
            webUserDetails.totalPoints = totalPoints.length > 0 ? totalPoints[0].totalPoints : 0;

            return sendResponse({
                status: 200,
                message: messages.SUCCESS,
                data: webUserDetails
            });
        } catch (error) {
            return sendResponse({
                status: 500,
                message: messages.SOMETHING_WENT_WRONG || error.message,
            });
        }
    }

    static async updateWebUserDetails(request) {
        const transaction = await web_users.sequelize.transaction();
        try {
            const formData = await request.formData();
            const requestData = Object.fromEntries(formData.entries());
            const excludedFields = getExcludedFields(requestData);

            const [existingEmail, existingUserName] = await Promise.all([
                web_users.findOne({
                    where: {
                        email: requestData.email,
                        ...(requestData.id && { id: { [Op.ne]: requestData.id } })
                    }
                }),
                web_users.findOne({
                    where: {
                        userName: requestData.userName,
                        ...(requestData.id && { id: { [Op.ne]: requestData.id } })
                    }
                })
            ]);

            if (existingEmail || existingUserName) {
                return sendResponse({
                    status: 400,
                    message: messages[`${existingEmail ? 'EMAIL' : 'USERNAME'}_ALREADY_EXISTS`],
                });
            }

            let addDetails =
                await web_users.findOne({
                    where: {
                        id: requestData.id
                    },
                    transaction
                });

            const fields = Object.keys(requestData).filter(key => !excludedFields.includes(key));
            fields.forEach(field => {
                if (requestData[field] !== undefined && requestData[field] !== null) {
                    addDetails[field] = requestData[field];
                }
            });

            await addDetails.save({ transaction });
            await transaction.commit();

            return sendResponse({
                status: 200,
                message: messages[`WEB_USER_DETAILS_${requestData.id ? 'UPDATED' : 'ADDED'}_SUCCESSFULLY`],
            });
        } catch (error) {
            if (transaction && !transaction.finished) {
                await transaction.rollback();
            }
            return sendResponse({
                status: error.status || 500,
                message: messages.SOMETHING_WENT_WRONG || error.message,
            });
        }
    }

    static async updateWebUserStatus(request) {
        const transaction = await web_users.sequelize.transaction();
        try {
            const formData = await request.formData();
            const { id, status } = Object.fromEntries(formData.entries());

            const currentUser = await web_users.findOne({ where: { id } });

            if (!currentUser) {
                return sendResponse({
                    status: 404,
                    message: messages.USER_NOT_FOUND,
                });
            }

            await web_users.update(
                { isBlock: parseInt(status) },
                { where: { id }, transaction }
            );

            await transaction.commit();

            return sendResponse({
                status: 200,
                message: messages[`USER_${parseInt(status) === 1 ? 'BLOCKED' : 'UNBLOCKED'}_SUCCESSFULLY`],
            });
        } catch (error) {
            if (transaction && !transaction.finished) {
                await transaction.rollback();
            }
            return sendResponse({
                status: error.status || 500,
                message: error.message || messages.SOMETHING_WENT_WRONG,
            });
        }
    }

    static async bulkUploadWebUsers(request) {
        const expectedHeaders = ['Email', 'FullName'];

        try {
            const formData = await request.formData();
            const file = formData.get('web_users_file');

            if (!file || typeof file !== 'object') {
                return sendResponse({
                    status: 400,
                    message: 'No file uploaded or invalid file',
                });
            }

            const fileExt = file.name.split('.').pop().toLowerCase();
            if (!['csv', 'xlsx', 'xls', 'xlsm'].includes(fileExt)) {
                return sendResponse({
                    status: 400,
                    message: messages.INVALID_FILE_FORMAT,
                });
            }

            let sheetData = [];
            let headers = [];

            if (fileExt === 'csv') {
                const text = await file.text();
                const parseResult = Papa.parse(text, {
                    header: true,
                    skipEmptyLines: true,
                    transformHeader: header => header.trim(),
                    transform: value => value?.trim() || '',
                    fastMode: true,
                });

                if (parseResult.errors?.length > 0) {
                    return sendResponse({
                        status: 400,
                        message: messages.INVALID_FILE_FORMAT,
                    });
                }

                sheetData = parseResult.data;
                headers = parseResult.meta.fields;
            } else {
                const fileData = await file.arrayBuffer();
                const workbook = XLSX.read(fileData, {
                    type: 'array',
                    cellDates: true,
                    dateNF: 'yyyy-mm-dd',
                    raw: false
                });

                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];

                sheetData = XLSX.utils.sheet_to_json(worksheet, {
                    defval: '',
                    raw: false,
                    blankrows: false,
                    header: 'A'
                });

                if (sheetData.length > 0) {
                    const headerRow = sheetData.shift();
                    headers = Object.values(headerRow)
                        .map(h => String(h || '').trim())
                        .filter(h => h && !h.startsWith('__EMPTY'));

                    sheetData = sheetData
                        .filter(row => Object.values(row).some(val => val != null && String(val).trim() !== ''))
                        .map(row => {
                            const processed = {};
                            let i = 0;
                            for (const key in row) {
                                if (headers[i]) {
                                    processed[headers[i]] = String(row[key] || '').trim();
                                    i++;
                                }
                            }
                            return processed;
                        });
                }
            }

            if (!sheetData?.length) {
                return sendResponse({
                    status: 400,
                    message: 'No data found in the uploaded file',
                });
            }

            const validationResult = validateBulkUploadHeaders(headers, expectedHeaders);
            if (validationResult.status !== 200) {
                return sendResponse({
                    status: validationResult.status,
                    message: validationResult.message,
                });
            }

            const fieldMappings = {
                'Email': 'email',
                'FullName': 'fullName'
            };

            const webUsersData = sheetData.map(row => {
                const webUsers = {};
                for (const [csvField, dbField] of Object.entries(fieldMappings)) {
                    webUsers[dbField] = String(row[csvField] || '').trim();
                }
                return webUsers;
            });

            if (webUsersData.length > 0) {
                return await this.processWebUsersData(webUsersData);
            }

            return sendResponse({
                status: 200,
                message: messages.WEB_USERS_CREATED_SUCCESSFULLY,
            });
        } catch (error) {
            return sendResponse({
                status: 500,
                message: messages.SOMETHING_WENT_WRONG || error.message,
            });
        }
    }

    static async processWebUsersData(webUsersData) {
        const now = Math.floor(Date.now() / 1000);
        const WEB_URL = (process.env.WEB_URL) ? process.env.WEB_URL : 'https://wetrade.co/';
        const appEnv = process.env.NEXT_PUBLIC_APP_ENV;

        const transaction = await web_users.sequelize.transaction();
        try {
            const inputEmailMap = new Map();
            webUsersData.forEach(user => {
                const email = (user.email || '').toLowerCase();
                if (email && !inputEmailMap.has(email)) {
                    inputEmailMap.set(email, user);
                }
            });
            const inputEmails = Array.from(inputEmailMap.keys());

            const existingUsers = await web_users.findAll({
                where: { email: inputEmails },
                attributes: ['email'],
                raw: true,
                transaction
            });

            const existingEmailSet = new Set(existingUsers.map(u => u.email.toLowerCase()));

            const newUsers = [];
            const emailToUserData = new Map();

            inputEmailMap.forEach((user, email) => {
                if (!existingEmailSet.has(email)) {
                    let pageUrl, referralCode;
                    do {
                        pageUrl = generateRandomString('alphanumeric', 16);
                        referralCode = generateRandomString('alphanumeric', 8);
                    } while (newUsers.some(u => u.pageUrl === pageUrl || u.referralCode === referralCode));
                    const userObj = {
                        fullName: user.fullName || '',
                        userName: '',
                        email: user.email,
                        referralBy: 0,
                        lastName: '',
                        mobileNumber: '',
                        checkPointLink: '',
                        referralCode,
                        pageUrl,
                        totalPoints: 1,
                        created_at: now,
                        fakeUser: 0
                    };
                    newUsers.push(userObj);
                    emailToUserData.set(email, { ...userObj });
                }
            });

            let createdUsers = [];
            if (newUsers.length > 0) {
                createdUsers = await web_users.bulkCreate(newUsers, { returning: true, transaction });
            }

            const rewardHistory = createdUsers.map(newUser => ({
                webuser_id: newUser.id,
                referralBy: 0,
                pointType: 1,
                points: 1,
                message: 'Signup point.',
                created_at: now
            }));

            if (rewardHistory.length > 0) {
                await user_reward_history.bulkCreate(rewardHistory, { transaction });
            }

            await transaction.commit();

            const userMailInfoData = createdUsers.map(newUser => ({
                email: newUser.email,
                link: WEB_URL + 'username/' + encodeURIComponent(newUser.email),
                profileLink: WEB_URL + 'profile?id=' + newUser.pageUrl
            }));

            if (appEnv === 'live' || appEnv === 'production') {
                setImmediate(() => {
                    SendEmailsToWebUsers.handleBulk(userMailInfoData).catch(() => { });
                });
            }

            return sendResponse({
                status: 200,
                message: messages.WEB_USERS_CREATED_SUCCESSFULLY,
                data: { created: createdUsers.length }
            });
        } catch (error) {
            if (transaction && !transaction.finished) {
                await transaction.rollback();
            }
            return sendResponse({
                status: 500,
                message: messages.SOMETHING_WENT_WRONG || error.message,
            });
        }
    }

}

export default WebUserController;