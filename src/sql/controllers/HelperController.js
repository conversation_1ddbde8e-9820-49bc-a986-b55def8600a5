import nodemailer from 'nodemailer';
import { GetObjectCommand } from "@aws-sdk/client-s3";
import { s3, sendResponse } from '@/utils/helper';
import messages from '@/utils/messages';
import sharp from 'sharp';
import models from '@/sql/models';

const { users } = models;

class HelperController {

    static async checkUserExists(id) {
        try {
            let currentUser = null;
            if (id) {
                currentUser = await users.findOne({ 
                    where: {
                        id
                    }
                });
                if (!currentUser) {
                    return {
                        status: 404,
                        message: messages.USER_NOT_FOUND,
                    };
                }
                if (currentUser && parseInt(currentUser.isActive) !== 1) {
                    return {
                        status: 400,
                        message: messages.USER_NOT_ACTIVE,
                    };
                }
                if (currentUser && parseInt(currentUser.isDelete) === 1) {
                    return {
                        status: 400,
                        message: messages.USER_DELETED_BY_ADMIN,
                    };
                }
                if (currentUser && parseInt(currentUser.isDelete) === 2) {
                    return {
                        status: 400,
                        message: messages.USER_DELETED_BY_USER,
                    };
                }
            }
            return {
                status: 200,
                message: messages.SUCCESS,
                data: currentUser,
            };
        } catch (error) {
            return {
                status: 500,
                message: messages.SOMETHING_WENT_WRONG || error.message,
            };
        }
    }

    static async sendEmail(to, subject, html) {
        try {
            const transporter = nodemailer.createTransport({
                host: process.env.SMTP_EMAIL_HOST,
                port: process.env.SMTP_EMAIL_PORT,
                auth: {
                    user: process.env.SMTP_EMAIL_USER,
                    pass: process.env.SMTP_EMAIL_PASSWORD,
                },
                tls: {
                    rejectUnauthorized: false,
                },
            });
            const mailOptions = {
                from: process.env.SMTP_EMAIL_FROM,
                to,
                subject,
                html,
            };
            return await transporter.sendMail(mailOptions);
        } catch (error) {
            throw error;
        }
    }

    static async getS3FileSize(request) {
        try {
            const { s3Url } = Object.fromEntries(new URLSearchParams(new URL(request.url).search));
            const url = new URL(s3Url);
            const bucketName = url.hostname.split('.')[0];
            const objectKey = url.pathname.slice(1);
            const getObjectCommand = new GetObjectCommand({
                Bucket: bucketName,
                Key: objectKey,
            });
            const response = await s3.send(getObjectCommand);
            if (response && response.Body) {
                return sendResponse({
                    status: 200,
                    message: messages.SUCCESS,
                    data: response.ContentLength,
                });
            } else {
                return sendResponse({
                    status: 404,
                    message: messages.FILE_NOT_FOUND,
                    data: 0,
                });
            }
        } catch (error) {
            return sendResponse({
                status: 500,
                message: messages.SOMETHING_WENT_WRONG || error.message,
                data: 0,
            });
        }
    }

    static async compressImage(file) {
        try {
            const arrayBuffer = await file.arrayBuffer();
            const inputBuffer = Buffer.from(arrayBuffer);

            let outputBuffer = await sharp(inputBuffer)
                .resize({ width: 1920, withoutEnlargement: true })
                .jpeg({ quality: 90 })
                .toBuffer();

            if (outputBuffer.length > 1024 * 1024) {
                outputBuffer = await sharp(inputBuffer)
                    .resize({ width: 1280, withoutEnlargement: true })
                    .jpeg({ quality: 60 })
                    .toBuffer();
            }
            return new File([outputBuffer], file.name, { type: 'image/jpeg' });
        } catch (error) {
            return null;
        }
    }
}

export default HelperController;