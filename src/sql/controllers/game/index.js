import models from '@/sql/models';
import { sendResponse, getSearchTerms } from "@/utils/helper";
import { Op, Sequelize } from 'sequelize';
// import { Op, fn, col, where, literal } from 'sequelize';
import messages from '@/utils/messages';

const { games, users } = models;

class GameController {
    static async getList(request) {
        try {
            const params = new URLSearchParams(new URL(request.url).search);
            const page = parseInt(params.get('page')) || 1;
            const perPage = parseInt(params.get('per_page')) || 10;
            const search = params.get('search') || '';
            const offset = (page - 1) * perPage;

            const includes = [{
                model: users,
                as: 'user',
                attributes: ['fullName'],
                required: false
            }];

            const conditions = { isDelete: 0 };
            let whereCondition = conditions;

            if (search.trim()) {
                const searchFields = [
                    { field: 'title', col: 'title' }
                ];

                const exactSearch = search.trim();
                const exactMatchConditions = searchFields.map(({ field, col }) => ({
                    [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)),
                        Sequelize.fn('LOWER', exactSearch))
                }));

                const partialMatchConditions = searchFields.map(({ field, col }) => ({
                    [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)), {
                        [Op.like]: `%${exactSearch.toLowerCase()}%`
                    })
                }));

                let phraseConditions = [];
                if (search.includes(' ')) {
                    phraseConditions = searchFields.map(({ field, col }) => ({
                        [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)), {
                            [Op.like]: `%${exactSearch.toLowerCase()}%`
                        })
                    }));
                }

                const searchTerms = getSearchTerms(search).filter(term => term.trim());
                let termConditions = [];
                if (searchTerms.length) {
                    termConditions = searchTerms.map(term => {
                        const termLower = term.trim().toLowerCase();
                        return {
                            [Op.or]: searchFields.map(({ field, col }) => ({
                                [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)), {
                                    [Op.like]: `%${termLower}%`
                                })
                            }))
                        };
                    });
                }

                whereCondition = {
                    [Op.and]: [
                        {
                            [Op.or]: [
                                ...exactMatchConditions,
                                ...phraseConditions,
                                ...partialMatchConditions,
                                ...(termConditions.length ? [{ [Op.and]: termConditions }] : [])
                            ]
                        },
                        conditions
                    ]
                };
            }

            const { count, rows: initialRows } = await games.findAndCountAll({
                where: whereCondition,
                include: includes,
                distinct: true,
                offset,
                limit: perPage,
                order: [['createdAt', 'desc']],
                raw: true,
                nest: true,
            });

            return sendResponse({
                status: 200,
                message: messages.SUCCESS,
                data: { total: count, per_page: perPage, current_page: page, data: initialRows },
            });
        } catch (error) {
            console.log(error);
            return sendResponse({
                status: 500,
                message: messages.SOMETHING_WENT_WRONG || error.message,
            });
        }
    }

    static async getGameDetails(request) {
        try {
            const params = new URLSearchParams(new URL(request.url).search);
            const id = params.get('id');
            const gameAttributes = ['id', 'userId', 'description', 'image', 'gif', 'createdAt'];
            const game = await games.findOne({
                where: { id },
                attributes: gameAttributes,
                include: [{
                    model: users,
                    as: 'user',
                    attributes: ['id', 'userId', 'fullName', 'userName', 'profileImage']
                }]
            });
            if (!game) {
                return sendResponse({
                    status: 404,
                    message: messages.IDEA_NOT_FOUND,
                });
            }
            const [totalComments, totalLikes, totalViews] = await Promise.all([
                comments.count({ where: { postId: id } }),
                likes.count({ where: { postId: id, commentId: 0 } }),
                post_viewers.count({ where: { postId: id } })
            ]);

            const gameDetails = game.toJSON();
            gameDetails.totalComments = totalComments || 0;
            gameDetails.totalLikes = totalLikes || 0;
            gameDetails.totalViews = totalViews || 0;

            return sendResponse({
                status: 200,
                message: messages.SUCCESS,
                data: gameDetails,
            });
        } catch (error) {
            return sendResponse({
                status: 500,
                message: messages.SOMETHING_WENT_WRONG || error.message,
            });
        }
    }

    static async updateGameStatus(request) {
        const transaction = await games.sequelize.transaction();
        try {
            const formData = await request.formData();
            const { id, status } = Object.fromEntries(formData.entries());

            const currentGame = await games.findOne({ where: { id } });

            if (!currentGame) {
                return sendResponse({
                    status: 404,
                    message: messages.IDEA_NOT_FOUND,
                });
            }

            await games.update(
                { isActive: parseInt(status) },
                { where: { id }, transaction }
            );

            await transaction.commit();

            return sendResponse({
                status: 200,
                message: messages[`IDEA_${parseInt(status) === 1 ? 'ACTIVATED' : 'DEACTIVATED'}_SUCCESSFULLY`],
            });
        } catch (error) {
            if (transaction && !transaction.finished) {
                await transaction.rollback();
            }
            return sendResponse({
                status: error.status || 500,
                message: error.message || messages.SOMETHING_WENT_WRONG,
            });
        }
    }

    static async deleteGame(request) {
        const transaction = await games.sequelize.transaction();
        try {
            const formData = await request.formData();
            const { id } = Object.fromEntries(formData.entries());

            const currentGame = await games.findOne({ where: { id } });

            if (!currentGame) {
                return sendResponse({
                    status: 404,
                    message: messages.IDEA_NOT_FOUND,
                });
            }

            await Promise.all([
                games.update(
                    { isDelete: 1 },
                    { where: { id }, transaction }
                ),
                games.update(
                    { isDelete: 1 },
                    { where: { parentId: id }, transaction }
                )
            ]);

            await transaction.commit();

            return sendResponse({
                status: 200,
                message: messages.IDEA_DELETED_SUCCESSFULLY,
            });
        } catch (error) {
            if (transaction && !transaction.finished) {
                await transaction.rollback();
            }
            return sendResponse({
                status: error.status || 500,
                message: messages.SOMETHING_WENT_WRONG || error.message,
            });
        }
    }
}

export default GameController;