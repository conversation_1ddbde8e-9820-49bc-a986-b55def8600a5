import models from '@/sql/models';
import { sendResponse, getSearchTerms } from "@/utils/helper";
import { Op, Sequelize } from 'sequelize';
import messages from '@/utils/messages';

const { posts, users, comments, likes, post_viewers } = models;

class IdeaController {
    static async getList(request) {
        try {
            const params = new URLSearchParams(new URL(request.url).search);
            const page = parseInt(params.get('page')) || 1;
            const perPage = parseInt(params.get('per_page')) || 10;
            const search = params.get('search') || '';
            const offset = (page - 1) * perPage;

            const includes = [{
                model: users,
                as: 'user',
                attributes: ['fullName'],
                required: true
            }];

            const conditions = { isDelete: 0, parentId: 0 };
            let whereCondition = conditions;

            if (search.trim()) {
                const searchFields = [
                    { field: 'description', col: 'description' },
                    { field: 'user.fullName', col: 'user.fullName' }
                ];

                const exactSearch = search.trim();
                const exactMatchConditions = searchFields.map(({ field, col }) => ({
                    [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)),
                        Sequelize.fn('LOWER', exactSearch))
                }));

                const partialMatchConditions = searchFields.map(({ field, col }) => ({
                    [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)), {
                        [Op.like]: `%${exactSearch.toLowerCase()}%`
                    })
                }));

                let phraseConditions = [];
                if (search.includes(' ')) {
                    phraseConditions = searchFields.map(({ field, col }) => ({
                        [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)), {
                            [Op.like]: `%${exactSearch.toLowerCase()}%`
                        })
                    }));
                }

                const searchTerms = getSearchTerms(search).filter(term => term.trim());
                let termConditions = [];
                if (searchTerms.length) {
                    termConditions = searchTerms.map(term => {
                        const termLower = term.trim().toLowerCase();
                        return {
                            [Op.or]: searchFields.map(({ field, col }) => ({
                                [field]: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col(col)), {
                                    [Op.like]: `%${termLower}%`
                                })
                            }))
                        };
                    });
                }

                whereCondition = {
                    [Op.and]: [
                        {
                            [Op.or]: [
                                ...exactMatchConditions,
                                ...phraseConditions,
                                ...partialMatchConditions,
                                ...(termConditions.length ? [{ [Op.and]: termConditions }] : [])
                            ]
                        },
                        conditions
                    ]
                };
            }

            const [{ count, rows: initialRows }, allReposts] = await Promise.all([
                posts.findAndCountAll({
                    where: whereCondition,
                    include: includes,
                    distinct: true,
                    offset,
                    limit: perPage,
                    order: [['updatedAt', 'desc']],
                    raw: true,
                    nest: true,
                }),
                posts.findAll({
                    where: {
                        parentId: { [Op.ne]: 0 },
                        isDelete: 0
                    },
                    attributes: ['parentId'],
                    raw: true
                })
            ]);

            const repostCounts = allReposts.reduce((counts, repost) => {
                counts[repost.parentId] = (counts[repost.parentId] || 0) + 1;
                return counts;
            }, {});

            const rows = initialRows.map(post => ({
                ...post,
                repostCount: repostCounts[post.id] || 0
            }));

            return sendResponse({
                status: 200,
                message: messages.SUCCESS,
                data: { total: count, per_page: perPage, current_page: page, data: rows },
            });
        } catch (error) {
            return sendResponse({
                status: 500,
                message: messages.SOMETHING_WENT_WRONG || error.message,
            });
        }
    }

    static async getIdeaDetails(request) {
        try {
            const params = new URLSearchParams(new URL(request.url).search);
            const id = params.get('id');
            const ideaAttributes = ['id', 'userId', 'description', 'image', 'gif', 'createdAt'];
            const idea = await posts.findOne({
                where: { id },
                attributes: ideaAttributes,
                include: [{
                    model: users,
                    as: 'user',
                    attributes: ['id', 'userId', 'fullName', 'userName', 'profileImage']
                }]
            });
            if (!idea) {
                return sendResponse({
                    status: 404,
                    message: messages.IDEA_NOT_FOUND,
                });
            }
            const [totalComments, totalLikes, totalViews] = await Promise.all([
                comments.count({ where: { postId: id } }),
                likes.count({ where: { postId: id, commentId: 0 } }),
                post_viewers.count({ where: { postId: id } })
            ]);

            const ideaDetails = idea.toJSON();
            ideaDetails.totalComments = totalComments || 0;
            ideaDetails.totalLikes = totalLikes || 0;
            ideaDetails.totalViews = totalViews || 0;

            return sendResponse({
                status: 200,
                message: messages.SUCCESS,
                data: ideaDetails,
            });
        } catch (error) {
            return sendResponse({
                status: 500,
                message: messages.SOMETHING_WENT_WRONG || error.message,
            });
        }
    }

    static async updateIdeaStatus(request) {
        const transaction = await posts.sequelize.transaction();
        try {
            const formData = await request.formData();
            const { id, status } = Object.fromEntries(formData.entries());

            const currentIdea = await posts.findOne({ where: { id } });

            if (!currentIdea) {
                return sendResponse({
                    status: 404,
                    message: messages.IDEA_NOT_FOUND,
                });
            }

            await posts.update(
                { isActive: parseInt(status) },
                { where: { id }, transaction }
            );

            await transaction.commit();

            return sendResponse({
                status: 200,
                message: messages[`IDEA_${parseInt(status) === 1 ? 'ACTIVATED' : 'DEACTIVATED'}_SUCCESSFULLY`],
            });
        } catch (error) {
            if (transaction && !transaction.finished) {
                await transaction.rollback();
            }
            return sendResponse({
                status: error.status || 500,
                message: error.message || messages.SOMETHING_WENT_WRONG,
            });
        }
    }

    static async deleteIdea(request) {
        const transaction = await posts.sequelize.transaction();
        try {
            const formData = await request.formData();
            const { id } = Object.fromEntries(formData.entries());

            const currentIdea = await posts.findOne({ where: { id } });

            if (!currentIdea) {
                return sendResponse({
                    status: 404,
                    message: messages.IDEA_NOT_FOUND,
                });
            }

            await Promise.all([
                posts.update(
                    { isDelete: 1 },
                    { where: { id }, transaction }
                ),
                posts.update(
                    { isDelete: 1 },
                    { where: { parentId: id }, transaction }
                )
            ]);

            await transaction.commit();

            return sendResponse({
                status: 200,
                message: messages.IDEA_DELETED_SUCCESSFULLY,
            });
        } catch (error) {
            if (transaction && !transaction.finished) {
                await transaction.rollback();
            }
            return sendResponse({
                status: error.status || 500,
                message: messages.SOMETHING_WENT_WRONG || error.message,
            });
        }
    }
}

export default IdeaController;