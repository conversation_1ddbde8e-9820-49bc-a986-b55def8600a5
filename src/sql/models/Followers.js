import _sequelize from 'sequelize';
const { Model, Sequelize } = _sequelize;

export default class followers extends Model {
  static init(sequelize, DataTypes) {
  return super.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    followerId: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    followingId: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    status: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1
    },
    createdAt: {
      type: DataTypes.INTEGER,
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'followers',
    timestamps: false,
    charset: 'utf8mb4',
    collate: 'utf8mb4_general_ci',
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
    ]
  });
  }

  static associate(models) {
    this.belongsTo(models.users, {
      as: "followerDetails",
      foreignKey: "followerId",
      targetKey: "userId"
    });
    this.belongsTo(models.users, {
      as: "followingDetails",
      foreignKey: "followingId",
      targetKey: "userId"
    });
  }
}