import _sequelize from 'sequelize';
const { Model, Sequelize } = _sequelize;

export default class posts extends Model {
  static init(sequelize, DataTypes) {
  return super.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    parentId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    userId: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    interestId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    channelId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    genreId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    contentType: {
      type: DataTypes.STRING(10),
      allowNull: false,
      comment: "idea,news,channel,twitter"
    },
    categoryId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    selectedCashTags: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    gif: {
      type: DataTypes.STRING(255),
      allowNull: false,
      defaultValue: ""
    },
    image: {
      type: DataTypes.STRING(255),
      allowNull: false,
      defaultValue: ""
    },
    thumbnail: {
      type: DataTypes.STRING(255),
      allowNull: false,
      defaultValue: ""
    },
    video: {
      type: DataTypes.STRING(255),
      allowNull: false,
      defaultValue: ""
    },
    latitude: {
      type: DataTypes.STRING(15),
      allowNull: false,
      defaultValue: ""
    },
    longitude: {
      type: DataTypes.STRING(15),
      allowNull: false,
      defaultValue: ""
    },
    title: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    totalLikes: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    totalComments: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    totalViews: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    platform: {
      type: DataTypes.STRING(10),
      allowNull: false
    },
    version: {
      type: DataTypes.STRING(10),
      allowNull: false
    },
    isActive: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1
    },
    isDelete: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.fn('current_timestamp')
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.fn('current_timestamp')
    },
    allowComments: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    allowDuet: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    allowStitch: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    reportCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    height: {
      type: DataTypes.FLOAT,
      allowNull: true,
      defaultValue: 0
    },
    width: {
      type: DataTypes.FLOAT,
      allowNull: true,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'posts',
    timestamps: false,
    charset: 'utf8mb4',
    collate: 'utf8mb4_general_ci',
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
    ]
  });
  }

  static associate(models) {
    this.belongsTo(models.users, { foreignKey: 'userId' });
  }
}