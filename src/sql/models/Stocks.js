import _sequelize from 'sequelize';
const { Model, Sequelize } = _sequelize;

export default class stocks extends Model {
  static init(sequelize, DataTypes) {
  return super.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    currency: {
      type: DataTypes.STRING(255),
      allowNull: false,
      defaultValue: ""
    },
    description: {
      type: DataTypes.STRING(255),
      allowNull: false,
      defaultValue: ""
    },
    displaySymbol: {
      type: DataTypes.STRING(255),
      allowNull: false,
      defaultValue: ""
    },
    symbol: {
      type: DataTypes.STRING(255),
      allowNull: false,
      defaultValue: ""
    },
    logo: {
      type: DataTypes.STRING(255),
      allowNull: false,
      defaultValue: ""
    },
    type: {
      type: DataTypes.STRING(255),
      allowNull: false,
      defaultValue: ""
    },
    isDelisted: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    stockType: {
      type: DataTypes.STRING(255),
      allowNull: false,
      defaultValue: "US"
    },
    isGameStock: {
      type: DataTypes.TINYINT,
      allowNull: false,
      defaultValue: 0
    },
    createdAt: {
      type: DataTypes.INTEGER,
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'stocks',
    timestamps: false,
    charset: 'utf8mb4',
    collate: 'utf8mb4_general_ci',
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
    ]
  });
  }
}