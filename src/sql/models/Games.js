import _sequelize from 'sequelize';
const { Model, Sequelize } = _sequelize;

export default class games extends Model {
  static init(sequelize, DataTypes) {
  return super.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    title: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    game_unique_name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      defaultValue: ""
    },        
    totalPlayers: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    startTimeUtc: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.fn('current_timestamp')
    },
    endTimeUtc: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.fn('current_timestamp')
    },    
    is_featured: {
      type: DataTypes.ENUM('1','0'),
      allowNull: false,
      defaultValue: "0"
    },
    isDelete: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    },
    createdBy: {
      type: DataTypes.ENUM('admin','user'),
      allowNull: false,
      defaultValue: "user"
    },
    createdAt: {
      type: DataTypes.INTEGER,
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'games',
    timestamps: false,
    charset: 'utf8mb4',
    collate: 'utf8mb4_general_ci',
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
    ]
  });
  }
  static associate(models) {
    this.belongsTo(models.users, { foreignKey: 'userId' });
  }
}