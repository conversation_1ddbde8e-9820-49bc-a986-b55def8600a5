import _sequelize from 'sequelize';
const { Model, Sequelize } = _sequelize;

export default class web_users extends Model {
  static init(sequelize, DataTypes) {
  return super.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    fullName: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    userName: {
      type: DataTypes.STRING(30),
      allowNull: false
    },
    email: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    mobileNumber: {
      type: DataTypes.STRING(30),
      allowNull: false,
      defaultValue: ""
    },
    pageUrl: {
      type: DataTypes.STRING(255),
      allowNull: false,
      defaultValue: ""
    },
    checkPointLink: {
      type: DataTypes.STRING(255),
      allowNull: false,
      defaultValue: ""
    },
    referralCode: {
      type: DataTypes.STRING(30),
      allowNull: false,
      defaultValue: ""
    },
    referralBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    referralCount: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    },
    totalPoints: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: "signup-1, referred-5, share-2"
    },
    isSubscription: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1
    },
    isBlock: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    created_at: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    timezone: {
      type: DataTypes.STRING(255),
      allowNull: false,
      defaultValue: ""
    },
    fakeUser: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'web_users',
    timestamps: false,
    charset: 'utf8mb4',
    collate: 'utf8mb4_general_ci',
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
    ]
  });
  }
}