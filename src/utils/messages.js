const messages = {};

// General messages
messages.SUCCESS = "Success";
messages.SOMETHING_WENT_WRONG = 'Something went wrong.';
messages.UNAUTHORIZED_ACCESS = "Unauthorized access.";
messages.INVALID_ACTION = "Invalid action.";
messages.UNEXPECTED_ERROR = "An unexpected error occurred.";
messages.INVALID_FILE_FORMAT = "Invalid file format.";
messages.FILE_NOT_FOUND = "File not found.";
messages.FINHUB_TOKEN_NOT_FOUND = "Finnhub token not found.";

// Authentication messages
messages.USER_NOT_FOUND = 'User not found.';
messages.INVALID_PASSWORD = 'Invalid password.';
messages.LOGGED_IN_SUCCESSFULLY = 'Logged in successfully.';
messages.LOGGED_OUT_SUCCESSFULLY = 'Logged out successfully.';
messages.EMAIL_ALREADY_EXISTS = 'Email already exists.';
messages.EMAIL_NOT_FOUND = 'Email not found.';

// File upload related messages
messages.FILES_UPLOADED_SUCCESSFULLY = "Files uploaded successfully.";
messages.FILES_UPLOAD_FAILED = "Files upload failed.";
messages.FILE_UPLOAD_FAILED = "File upload failed.";
messages.FILES_DELETED_SUCCESSFULLY = "Files deleted successfully.";
messages.FILES_DELETED_FAILED = "Files deletion failed.";

// User related messages
messages.USER_DETAILS_NOT_FOUND = "User details not found.";
messages.USERNAME_ALREADY_EXISTS = "Username already exists.";
messages.USER_DETAILS_ADDED_SUCCESSFULLY = "User details added successfully.";
messages.USER_DETAILS_UPDATED_SUCCESSFULLY = "User details updated successfully.";
messages.USER_DELETED_BY_ADMIN = "User deleted by admin.";
messages.USER_DELETED_BY_USER = "User deleted by user.";
messages.USER_ACTIVATED_SUCCESSFULLY = "User activated successfully.";
messages.USER_DEACTIVATED_SUCCESSFULLY = "User deactivated successfully.";
messages.USER_DELETED_SUCCESSFULLY = "User deleted successfully.";
messages.USER_BLOCKED_SUCCESSFULLY = "User blocked successfully.";
messages.USER_UNBLOCKED_SUCCESSFULLY = "User unblocked successfully.";

//Interest related messages
messages.INTEREST_DETAILS_NOT_FOUND = "Interest details not found.";
messages.INTEREST_DETAILS_ADDED_SUCCESSFULLY = "Interest details added successfully.";
messages.INTEREST_DETAILS_UPDATED_SUCCESSFULLY = "Interest details updated successfully.";
messages.INTEREST_DELETED_SUCCESSFULLY = "Interest deleted successfully.";
messages.INTEREST_ACTIVATED_SUCCESSFULLY = "Interest activated successfully.";
messages.INTEREST_DEACTIVATED_SUCCESSFULLY = "Interest deactivated successfully.";
messages.INTEREST_ALREADY_EXISTS = "Interest already exists.";

//Stock related messages
messages.STOCK_DETAILS_NOT_FOUND = "Stock details not found.";
messages.STOCK_ADDED_TO_GAME_STOCK_SUCCESSFULLY = "Stock added to game stock successfully.";
messages.STOCK_REMOVED_FROM_GAME_STOCK_SUCCESSFULLY = "Stock removed from game stock successfully.";
messages.STOCK_NOT_FOUND = "Stock not found.";
messages.STOCK_DATA_SYNCED_SUCCESSFULLY = "Stock data synced successfully.";
messages.CRYPTO_DATA_SYNCED_SUCCESSFULLY = "Crypto data synced successfully.";
messages.INVALID_STOCK_TYPE = "Invalid stock type.";

//Idea related messages    
messages.IDEA_NOT_FOUND = "Idea not found.";
messages.IDEA_ACTIVATED_SUCCESSFULLY = "Idea activated successfully.";
messages.IDEA_DEACTIVATED_SUCCESSFULLY = "Idea deactivated successfully.";
messages.IDEA_DELETED_SUCCESSFULLY = "Idea deleted successfully.";
messages.IDEA_DETAILS_NOT_FOUND = "Idea details not found.";

//Sub Interest related messages
messages.SUB_INTEREST_DETAILS_NOT_FOUND = "Sub Interest details not found.";
messages.SUB_INTEREST_DETAILS_ADDED_SUCCESSFULLY = "Sub Interest details added successfully.";
messages.SUB_INTEREST_DETAILS_UPDATED_SUCCESSFULLY = "Sub Interest details updated successfully.";
messages.SUB_INTEREST_DELETED_SUCCESSFULLY = "Sub Interest deleted successfully.";
messages.SUB_INTEREST_ACTIVATED_SUCCESSFULLY = "Sub Interest activated successfully.";
messages.SUB_INTEREST_DEACTIVATED_SUCCESSFULLY = "Sub Interest deactivated successfully.";
messages.SUB_INTEREST_ALREADY_EXISTS = "Sub Interest already exists.";

//Web User related messages
messages.WEB_USER_DETAILS_NOT_FOUND = "Web User details not found.";
messages.WEB_USER_DETAILS_ADDED_SUCCESSFULLY = "Web User details added successfully.";
messages.WEB_USER_DETAILS_UPDATED_SUCCESSFULLY = "Web User details updated successfully.";
messages.WEB_USERS_CREATED_SUCCESSFULLY = "Web Users created successfully.";

//Game related messages
messages.GAME_DETAILS_NOT_FOUND = "Game details not found.";
messages.GAME_DETAILS_ADDED_SUCCESSFULLY = "Game details added successfully.";
messages.GAME_DETAILS_UPDATED_SUCCESSFULLY = "Game details updated successfully.";
messages.GAME_DELETED_SUCCESSFULLY = "Game deleted successfully.";

//Game Stock related messages
messages.GAME_STOCK_MAX_LIMIT = "You have reached the maximum limit 25 of GAME STOCKS.";

//Comment related messages
messages.COMMENT_NOT_FOUND = "Comment not found.";
messages.COMMENT_DELETED_SUCCESSFULLY = "Comment deleted successfully.";
messages.COMMENT_DELETED_FAILED = "Comment deletion failed.";

export default messages;
