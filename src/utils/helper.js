import { S3Client, PutObjectCommand, DeleteObjectCommand, ListObjectsV2Command, DeleteObjectsCommand } from "@aws-sdk/client-s3";
import { NextResponse } from 'next/server';
import { toast } from "react-hot-toast";
import bcrypt from 'bcryptjs';
import cryptoRandomString from 'crypto-random-string';
import messages from './messages';

const S3_URL = process.env.NEXT_PUBLIC_AWS_S3_URL;
const NO_IMAGE_AVAILABLE = '/images/no-image.png';
let getSession;
let token;

if (typeof window !== 'undefined') {
    const nextAuth = require('next-auth/react');
    getSession = async () => {
        const session = await nextAuth.getSession();
        return [session?.user?.accessToken];
    };
    token = (await getSession())[0] || null;
}

export const getS3FilePath = (filePath) => {
    try {
        const url = `${S3_URL + filePath}`;
        return url ?? NO_IMAGE_AVAILABLE;
    } catch {
        return NO_IMAGE_AVAILABLE;
    }
};

export const getHeaders = () => {
    if (typeof window !== 'undefined' && token) {
        return {
            'X-API-Username': process.env.NEXT_PUBLIC_API_SECURE_USERNAME,
            'X-API-Password': process.env.NEXT_PUBLIC_API_SECURE_PASSWORD,
            'Authorization': `Bearer ${token}`
        };
    }
    return {};
};

export const verifyHeaders = async (headers, token) => {
    const expectedUsername = process.env.NEXT_PUBLIC_API_SECURE_USERNAME;
    const expectedPassword = process.env.NEXT_PUBLIC_API_SECURE_PASSWORD;
    const expectedAuthorization = `Bearer ${token}`;
    return headers['X-API-Username'] == expectedUsername &&
        headers['X-API-Password'] == expectedPassword &&
        headers['Authorization'] == expectedAuthorization;
};

export const s3 = new S3Client({
    region: process.env.NEXT_PUBLIC_AWS_REGION,
    credentials: {
        accessKeyId: process.env.NEXT_PUBLIC_AWS_KEY,
        secretAccessKey: process.env.NEXT_PUBLIC_AWS_SECRET,
    },
});

export const s3UploadFile = async (file, folder, userId = null) => {
    try {
        const bucketName = process.env.NEXT_PUBLIC_AWS_BUCKET;
        const envType = process.env.NEXT_PUBLIC_APP_ENV || "local";
        let fileName = `${Date.now()}_${file.name}`;
        let sanitizedFileName = fileName.replace(/[^a-zA-Z0-9_.-]/g, "_");
        if (sanitizedFileName.includes("svg+xml")) {
            sanitizedFileName = sanitizedFileName.replace("svg+xml", "svg");
        }
        let key = "";
        if (folder) {
            key += `${folder}/`;
            if (userId) {
                key += `${userId}/`;
            }
            key += sanitizedFileName;
        } else {
            key = sanitizedFileName;
        }
        if (envType !== "live") {
            key = `${envType}/${key}`;
        }
        const fileBuffer = Buffer.from(await file.arrayBuffer());
        const uploadParams = {
            Bucket: bucketName,
            Key: key,
            Body: fileBuffer,
            ContentType: file.type,
            ACL: "public-read",
        };
        await s3.send(new PutObjectCommand(uploadParams));
        return key;
    } catch (error) {
        console.error("S3 Upload Error:", error);
        return null;
    }
};

export const deleteS3Item = async (path) => {
    try {
        const bucketName = process.env.NEXT_PUBLIC_AWS_BUCKET;

        if (!path || path === "default_images/default.jpg") {
            return false;
        }
        const deleteParams = {
            Bucket: bucketName,
            Key: path,
        };
        await s3.send(new DeleteObjectCommand(deleteParams));
        return true;
    } catch (error) {
        console.error('Error deleting from S3:', error || 'Unknown error');
        return false;
    }
};

export const deleteS3Folder = async (path) => {
    try {
        if (!path) return false;
        const environment = process.env.NEXT_PUBLIC_APP_ENV || 'local';
        let folderPath = `${path}/`;
        if (environment !== 'live' || environment !== "production") {
            folderPath = `${environment}/${path}/`;
        }
        await deleteS3MultipleFiles(folderPath, [], true);
        return true;
    } catch (error) {
        console.error('Error deleting from S3:', error || 'Unknown error');
        return false;
    }
};

export const deleteS3MultipleFiles = async (s3BucketFolder, filesToDelete = [], isFolder = false) => {
    try {
        const bucketName = process.env.NEXT_PUBLIC_AWS_BUCKET;
        const listParams = {
            Bucket: bucketName,
            Prefix: s3BucketFolder
        };
        let objectsToDelete = [];
        let s3Paths = [];
        const listedObjects = await s3.send(new ListObjectsV2Command(listParams));
        if (listedObjects?.Contents?.length > 0) {
            if (!isFolder) {
                s3Paths = filesToDelete.map(img => img.image);
                objectsToDelete = listedObjects.Contents.filter(obj =>
                    s3Paths.includes(obj.Key)
                );
            } else {
                objectsToDelete = listedObjects.Contents;
            }
            if (objectsToDelete.length > 0) {
                const deleteParams = {
                    Bucket: bucketName,
                    Delete: {
                        Objects: objectsToDelete.map(({ Key }) => ({ Key }))
                    }
                };
                await s3.send(new DeleteObjectsCommand(deleteParams));
            }
            if (listedObjects?.IsTruncated) {
                await deleteS3Item(s3BucketFolder);
            }
        }
        return true;
    } catch (error) {
        console.error('Error deleting from S3:', error || 'Unknown error');
        return false;
    }
}

export const ucfirst = (str) => {
    return str.charAt(0).toUpperCase() + str.slice(1);
}

export const ucwords = (str) => {
    return str
        .toLowerCase()
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
}

export const sendResponse = ({ status = 200, data = null, message = 'Success', headers = {} }) => {
    const defaultHeaders = {
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Surrogate-Control': 'no-store',
        'Access-Control-Allow-Origin': '*',
    };
    return NextResponse.json(
        {
            status,
            message,
            ...(data && { data })
        },
        {
            status,
            headers: { ...defaultHeaders, ...headers }
        }
    );
};

export const preventSpaces = (event) => {
    if (event.keyCode === 32 || event.key === " ") {
        event.preventDefault();
        let val = event.target.value;
        const name = event.target.name;
        if (name.includes('password')) {
            val = val.replace(/[.\s]/g, '');
        } else {
            val = val.replace(/\s/g, '');
        }
        event.target.value = val;
    }
};
export const showToast = (message, type, options = {}) => {
    toast.dismiss();

    const defaultToastOptions = {
        position: "top-center",
        reverseOrder: false,
        pauseOnFocusLoss: false,
        draggable: false,
        pauseOnHover: false,
        theme: "light",
        duration: 3000,
    };
    const toastOptions = { ...defaultToastOptions, ...options };

    if (type === 'error' && !options.duration) {
        toastOptions.duration = 5000;
    }

    switch (type) {
        case "success":
            toast.success(message, toastOptions);
            break;
        case "error":
            toast.error(message, toastOptions);
            break;
        case "info":
            toast(message, { ...toastOptions, icon: '📢' });
            break;
        case "warning":
            toast(message, { ...toastOptions, icon: '⚠️' });
            break;
        default:
            toast(message, toastOptions);
    }
};

export const hashPassword = async (password) => {
    return await bcrypt.hash(password, 10);
};

export const comparePassword = async (password, hashedPassword) => {
    return await bcrypt.compare(password, hashedPassword);
};

export const getCurrentTimeZone = () => {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
}

export const formatDate = (date) => {
    //e.g. 2025-04-17 10:00:00 or 2025-04-17 | output: 17 Apr 2025
    return new Date(date).toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' });
}

export const formatDateWithTime = (date, isSecond = false) => {
    if (!date) return '';
    try {
        const dateObj = new Date(date);
        if (isNaN(dateObj.getTime())) return '';

        const options = {
            month: 'long',
            day: 'numeric',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: isSecond ? '2-digit' : undefined,
            hour12: isSecond ? false : true,
        };

        return dateObj.toLocaleString('en-US', options);
    } catch (error) {
        console.error('Error formatting date with time:', error);
        return '';
    }
}

export const formatDateToDBTimestamp = (date) => {
    //e.g. Mon Apr 21 2025 11:41:27 GMT+0530 (India Standard Time) | output: 2025-04-21 11:41:27
    let d;
    try {
        if (date instanceof Date) {
            d = date;
        } else if (typeof date === 'string') {
            d = new Date(date);
            if (isNaN(d.getTime())) {
                throw new Error('Invalid date');
            }
        } else {
            d = new Date();
        }
    } catch (error) {
        d = new Date();
    }

    const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    };

    const formattedParts = new Intl.DateTimeFormat('en-US', options).formatToParts(d);
    const dateParts = {};

    formattedParts.forEach(part => {
        if (part.type !== 'literal') {
            dateParts[part.type] = part.value;
        }
    });

    return `${dateParts.year}-${dateParts.month}-${dateParts.day} ${dateParts.hour}:${dateParts.minute}:${dateParts.second}`;
};


export const generateRandomString = (type = 'alphanumeric', length = 20) => {
    const randomString = cryptoRandomString({ length: length, type: type });
    return randomString;
}

export const truncateFileName = (fileName, maxLength = 50) => {
    const env = process.env.NEXT_PUBLIC_APP_ENV;
    const isProd = env === 'live' || env === 'production';

    const parts = fileName.split('/');
    let cleanName = isProd
        ? parts.slice(1).join('/')
        : parts.slice(2).join('/') || fileName;

    const dotIndex = cleanName.lastIndexOf('.');
    const hasExtension = dotIndex !== -1;

    const extension = hasExtension ? cleanName.slice(dotIndex + 1) : '';
    let name = hasExtension ? cleanName.slice(0, dotIndex) : cleanName;

    name = name.replace(/^\d+_/, '');

    const spaceForName = maxLength - (hasExtension ? extension.length + 1 : 0);

    if (name.length > spaceForName) {
        const ellipsis = '...';
        const start = Math.floor((spaceForName - ellipsis.length) * 0.7);
        const end = spaceForName - start - ellipsis.length;
        name = `${name.slice(0, start)}${ellipsis}${name.slice(-end)}`;
    }

    return hasExtension ? `${name}.${extension}` : name;
};

export const onlyNumbers = (event, isDecimal = false) => {
    if (event.type === "paste") {
        event.preventDefault();
        const pastedData = event.clipboardData.getData('text');
        if (!/^\d+$/.test(pastedData)) return false;
        const input = event.target;
        const { selectionStart, selectionEnd, value } = input;
        const newValue = value.slice(0, selectionStart) + pastedData + value.slice(selectionEnd);
        input.value = newValue;
        input.setSelectionRange(selectionStart + pastedData.length, selectionStart + pastedData.length);
        return false;
    }
    let allowedKeys = [
        'Backspace', 'Tab', 'Home', 'End', 'Delete',
        'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight',
        '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'
    ];

    if (isDecimal && event.key === '.') {
        allowedKeys.push('.');
    }

    if (
        (event.key === 'F5') ||
        ((event.ctrlKey || event.metaKey) &&
            (['a', 'A', 'c', 'C', 'v', 'V', 'x', 'X'].includes(event.key) ||
                (event.shiftKey && ['r', 'R'].includes(event.key)))
        ) ||
        allowedKeys.includes(event.key)
    ) {
        return true;
    } else {
        event.preventDefault();
        return false;
    }
}

export const onlyAlphaNumeric = (event) => {
    if (event.type === "paste") {
        event.preventDefault();
        const pastedData = event.clipboardData.getData('text');
        if (!/^[a-zA-Z0-9]+$/.test(pastedData)) return false;
        const input = event.target;
        const { selectionStart, selectionEnd, value } = input;
        const newValue = value.slice(0, selectionStart) + pastedData + value.slice(selectionEnd);
        input.value = newValue.toUpperCase();
        input.setSelectionRange(selectionStart + pastedData.length, selectionStart + pastedData.length);
        return false;
    }

    const allowedKeys = [
        'Backspace', 'Tab', 'Home', 'End', 'Delete',
        'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'
    ];
    const allowedAlphaNumKeys = [
        ...allowedKeys,
        ...Array.from({ length: 10 }, (_, i) => String.fromCharCode(i + 48)),
        ...Array.from({ length: 26 }, (_, i) => String.fromCharCode(i + 65)),
        ...Array.from({ length: 26 }, (_, i) => String.fromCharCode(i + 97))
    ];

    if (
        (event.key === 'F5') ||
        ((event.ctrlKey || event.metaKey) &&
            (['a', 'A', 'c', 'C', 'v', 'V', 'x', 'X'].includes(event.key) ||
                (event.shiftKey && ['r', 'R'].includes(event.key)))
        ) ||
        allowedAlphaNumKeys.includes(event.key)
    ) {
        return true;
    } else {
        event.preventDefault();
        return false;
    }
}

export const isParsableDate = (val) => {
    if (val instanceof Date) {
        return !isNaN(val);
    }

    if (typeof val !== 'string') {
        return false;
    }

    if (/^[A-Za-z0-9\s]+$/.test(val) &&
        !val.includes('/') &&
        !val.includes('-') &&
        !val.includes(':')) {
        return false;
    }

    const datePatterns = [
        /^\d{4}-\d{2}-\d{2}$/,
        /^\d{2}\/\d{2}\/\d{4}$/,
        /^\d{2}-\d{2}-\d{4}$/,
        /^\d{4}\/\d{2}\/\d{2}$/,
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z$/,
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?(([+-]\d{2}:\d{2})|Z)?$/,
        /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/,
        /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/,
        /^\d{2}\/\d{2}\/\d{4} \d{2}:\d{2}:\d{2}$/,
        /^\d{2}\/\d{2}\/\d{4} \d{2}:\d{2}$/,
        /^\d{1,2}\/\d{1,2}\/\d{4} \d{1,2}:\d{2}:\d{2} (AM|PM)$/,
        /^\d{1,2}\/\d{1,2}\/\d{4} \d{1,2}:\d{2} (AM|PM)$/,
        /^\d{1,2} [A-Za-z]{3,9} \d{4}$/,
        /^[A-Za-z]{3,9} \d{1,2}, \d{4}$/,
        /^[A-Za-z]{3} [A-Za-z]{3} \d{2} \d{4} \d{2}:\d{2}:\d{2} GMT[+-]\d{4}/
    ];

    const matchesPattern = datePatterns.some(pattern => pattern.test(val));

    if (matchesPattern) {
        const parsedDate = new Date(val);
        return !isNaN(parsedDate) && parsedDate.toString() !== 'Invalid Date';
    }

    const parsedDate = new Date(val);

    if (!isNaN(parsedDate) && parsedDate.toString() !== 'Invalid Date') {
        const year = parsedDate.getFullYear();
        return year > 1000 && year < 9999;
    }

    return false;
};

export const handleSelectChange = (fieldName, selected, setValue, trigger, isMulti = false) => {
    if (isMulti && Array.isArray(selected) && selected.length > 0) {
        setValue(fieldName, selected);
        trigger(fieldName);
    } else if (selected) {
        setValue(fieldName, selected.value);
        trigger(fieldName);
    } else {
        setValue(fieldName, isMulti ? [] : '');
    }
};

export const handleDateInputChange = (fieldName, date, setValue, trigger) => {
    setValue(fieldName, date);
    trigger(fieldName);
};

export const handleFileUpload = (fieldName, file, setValue, trigger, typeKey = null, fileType = null) => {
    if (file) {
        setValue(fieldName, file);
        if (typeKey && fileType) {
            setValue(typeKey, fileType);
        }
        trigger(fieldName);
    } else {
        setValue(fieldName, '');
        if (typeKey) {
            setValue(typeKey, '');
        }
    }
};

export const getExcludedFields = (requestData, excludedFields = []) => {
    return Object.keys(requestData).filter(field => {
        const fieldLower = field.toLowerCase();
        return (
            (['image', 'path', 'file', 'video', 'images', 'documents', 'document', 'files'].some(term => fieldLower.includes(term)) &&
                typeof requestData[field] === 'object') ||
            ['id', 'created_at', 'updated_at', 'createdAt', 'updatedAt'].includes(field) ||
            excludedFields.includes(field)
        );
    });
};

export const getDateFields = (requestData, excludedFields) => {
    const dateFields = [];
    const dateRegex = /^(\d{4}-\d{2}-\d{2}|\d{2}\/\d{2}\/\d{4}|\d{2}-\d{2}-\d{4})$/;

    if (Object.keys(requestData || {}).length > 0) {
        dateFields.push(...Object.entries(requestData)
            .filter(([key, value]) => {
                if (!value || (excludedFields && excludedFields.includes(key))) {
                    return false;
                }
                const keyIncludesDate = key.toLowerCase().includes('date');
                const isDateFormat = typeof value === 'string' && dateRegex.test(value);

                if (keyIncludesDate || isDateFormat) {
                    const date = new Date(value);
                    return date instanceof Date && !isNaN(date);
                }

                return false;
            })
            .map(([key]) => key));
    }
    return dateFields;
}

export const getSearchTerms = (search) => {
    if (!search) return [];
    const trimmedSearch = search.trim();
    return trimmedSearch.includes('|') ? trimmedSearch.split('|') : [trimmedSearch];
}

export const getSearchRecords = (e, setSearch, setCurrentPage, getList) => {
    const searchValue = e.target.value;
    setSearch(searchValue);
    setCurrentPage(1);

    return new Promise((resolve) => {
        let timeoutId;
        try {
            const currentSearchValue = searchValue;
            timeoutId = setTimeout(async () => {
                try {
                    if (currentSearchValue === searchValue) {
                        const formattedSearch = searchValue?.trim()
                            ?.split(/\s+/)
                            ?.filter(Boolean)
                            ?.join('|') ?? '';
                        await getList(1, formattedSearch);
                    }
                } catch (error) {
                    showToast('Search operation failed', 'error');
                } finally {
                    resolve();
                }
            }, 500);
        } catch (error) {
            showToast('Error setting up search', 'error');
            resolve();
        }
        return () => timeoutId && clearTimeout(timeoutId);
    });
}

export const validateBulkUploadHeaders = (headers, expectedHeaders) => {
    const missingHeaders = expectedHeaders.filter(
        expectedHeader => !headers.includes(expectedHeader)
    );
    const extraHeaders = headers.filter(
        header => !expectedHeaders.includes(header)
    );
    if (headers.length !== expectedHeaders.length || missingHeaders.length > 0 || extraHeaders.length > 0) {
        return {
            status: 400,
            message: `${messages.INVALID_FILE_FORMAT} Expected headers do not match with the uploaded file.`,
        };
    }
    return { status: 200, message: null };
}

export const formatTimestampToDate = (timestamp) => {
    if (!timestamp) return '';
    try {
        const date = new Date(timestamp);
        if (isNaN(date.getTime())) return '';
        const options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        };
        return date.toLocaleDateString('en-US', options);
    } catch (error) {
        console.error('Error formatting date:', error);
        return '';
    }
}

export const formatDateTimeCustom = (timestamp) => {
    if (!timestamp) return '';
    try {
        const isUnixTimestamp = /^\d{10}$/.test(String(timestamp));
        const timestampMs = isUnixTimestamp ? timestamp * 1000 : timestamp;
        const date = new Date(timestampMs);
        if (isNaN(date.getTime())) return '';
        const options = {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        };
        return date.toLocaleString('en-US', options);
    } catch (error) {
        console.error('Error formatting date:', error);
        return '';
    }
}

export const shortenString = (str, allowStrCount) => {
    return str.length > allowStrCount ? str.substring(0, (allowStrCount - 3)) + "..." : str;
};

export const setDisabledAndReadOnly = (e) => {
    e.preventDefault();
    e.target.disabled = true;
    e.target.readOnly = true;
    e.target.style.cursor = 'not-allowed';
}

export const formatCount = (count) => {
    if (!count && count !== 0) return '';

    try {
        const numCount = typeof count === 'string' ? parseInt(count, 10) : count;
        if (isNaN(numCount)) return '';
        if (numCount < 1000) {
            return numCount.toString();
        }
        const thousands = numCount / 1000;
        if (thousands % 1 === 0) {
            return `${thousands}K`;
        } else {
            return `${thousands.toFixed(1)}K`;
        }
    } catch (error) {
        showToast(error?.message || 'Error formatting count', 'error');
        return count.toString();
    }
};

export const getStockTypeOptions = (type) => {
    let stockExchanges = [
        'BITFINEX', 'FXPIG', 'COINBASE', 'BINANCEUS', 'KRAKEN',
        'BITTREX', 'BITMEX', 'POLONIEX', 'GEMINI', 'KUCOIN',
        'HITBTC', 'OKEX', 'HUOBI', 'BINANCE'
    ];
    if (type === 'crypto') {
        stockExchanges = ['US'];
    }
    return stockExchanges;
}