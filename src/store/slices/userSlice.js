import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  id: null,
  email: null,
  roleId: null,
  accessToken: null,
  otpPending: true,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUser: (state, action) => {
      const payload = action.payload;
      const updatedState = {
        id: payload.id !== undefined ? payload.id : state.id,
        email: payload.email !== undefined ? payload.email.toLowerCase() : state.email,
        roleId: payload.roleId !== undefined ? payload.roleId : state.roleId,
        accessToken: payload.accessToken !== undefined ? payload.accessToken : state.accessToken,
        otpPending: payload.otpPending !== undefined ? payload.otpPending : state.otpPending,
      };
      return updatedState;
    },
    clearUser: (state) => {
      return initialState;
    },
  },
});

export const { setUser, clearUser } = userSlice.actions;
export default userSlice.reducer;