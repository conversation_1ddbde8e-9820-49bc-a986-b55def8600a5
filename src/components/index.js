'use client'

import dynamic from 'next/dynamic';

const GlobalLazyMedia = dynamic(() => import('./GlobalLazyMedia'), { ssr: false });
const PageLoader = dynamic(() => import('./Layout/PageLoader'), { ssr: false });
const NotFound = dynamic(() => import('./NotFound'), { ssr: false });

//Layout Components
import Navbar from './Layout/Navbar';
import Sidebar from './Layout/SideBar';
import Loader from './Loader';
import CommonModal from './CommonModal';
import ClientProviders from './ClientProviders';
import AuthGuard from './AuthGuard';

//Common Components
import ActiveInActive from './Common/ActiveInActive';
import DeleteConfirmation from './Common/DeleteConfirmation';
import FollowersFollowingList from './Common/FollowersFollowingList';
import ImageRender from './Common/ImageRender';
import DataTableLoader from './Common/DataTableLoader';

//Form Components
import UserForm from './User/Form';
import WebUserForm from './WebUser/Form';
import InterestForm from './Interest/Form';
import BulkUploadForm from './WebUser/BulkUpload';

//Dashboard Components
const Dashboard = dynamic(() => import('./Dashboard'), { ssr: false });

//Common Components
const Common = {
    ActiveInActive,
    DeleteConfirmation,
    FollowersFollowingList,
    ImageRender,
    DataTableLoader,
};

//Auth Components
const Auth = {
    Login: dynamic(() => import('./Auth/Login')),
};

//User Components
const User = {
    List: dynamic(() => import('./User')),
    Details: dynamic(() => import('./User/Details')),
    Form: UserForm,
};

//WebUser Components
const WebUser = {
    List: dynamic(() => import('./WebUser')),
    Details: dynamic(() => import('./WebUser/Details')),
    Form: WebUserForm,
    BulkUpload: BulkUploadForm,
};

//Interest Components
const Interest = {
    List: dynamic(() => import('./Interest')),
    Form: InterestForm,
};

//Idea Components
const Idea = {
    List: dynamic(() => import('./Idea')),
    Details: dynamic(() => import('./Idea/Details')),
};

//Stock Components
const Stock = {
    List: dynamic(() => import('./Stock')),
};

//Game Components
const Game = {
    List: dynamic(() => import('./Game')),
};

export {
    Navbar,
    Sidebar,
    Loader,
    ClientProviders,
    AuthGuard,
    CommonModal,
    GlobalLazyMedia,
    PageLoader,
    NotFound,
    Dashboard,

    //common
    Common,

    // Modules
    Auth,
    User,
    WebUser,
    Interest,
    Idea,
    Stock,
    Game,
};
