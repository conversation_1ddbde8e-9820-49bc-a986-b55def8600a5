import React, { useState, useCallback, useRef } from "react";
import { signIn, getSession } from "next-auth/react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import { Loader } from "@/components";
import { showToast, preventSpaces, onlyNumbers } from "@/utils/helper";
import messages from "@/utils/messages";
import { useDispatch } from "react-redux";
import { setUser } from "@/store/slices/userSlice";

const Login = () => {
    const stepRef = useRef("credentials");
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [otp, setOtp] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [isSubmitDisable, setIsSubmitDisable] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const dispatch = useDispatch();

    const loginSchema = Yup.object().shape({
        email: Yup.string().trim().required("Email is required").email("Enter valid email"),
        password: Yup.string().required("Password is required").min(8, "At least 8 chars"),
    });

    const otpSchema = Yup.object().shape({
        otp: Yup.string()
            .required("OTP is required")
            .matches(/^[0-9]+$/, "Only digits allowed")
            .min(6, "Enter valid OTP")
            .max(6, "Enter valid OTP")
            .transform((value) => value.trim())
    });

    const {
        register: registerLogin,
        handleSubmit: handleSubmitLogin,
        formState: { errors: loginErrors }
    } = useForm({ resolver: yupResolver(loginSchema) });

    const {
        register: registerOtp,
        handleSubmit: handleSubmitOtp,
        formState: { errors: otpErrors }
    } = useForm({ resolver: yupResolver(otpSchema) });

    const handleLogin = useCallback(async (data) => {
        setIsLoading(true);
        try {
            setEmail(data.email);
            setPassword(data.password);

            const result = await signIn("credentials", {
                email: data.email,
                password: data.password,
                redirect: false
            });

            if (result?.error) {
                showToast(result.error, "error");
                return false;
            } else if (result.ok) {
                stepRef.current = "otp";
            }
        } catch (err) {
            showToast(err.message || "Login failed", "error");
        } finally {
            setIsLoading(false);
        }
    }, []);

    const handleOtpVerify = async (data) => {
        setIsLoading(true);
        try {
            const result = await signIn("credentials", {
                email,
                password,
                otp: data.otp,
                redirect: false
            });
            if (result?.error) {
                showToast(result.error, "error");
                return false;
            } else if (result.ok) {
                setIsSubmitDisable(true);
                const session = await getSession();
                if (session?.user) {
                    dispatch(setUser(session.user));
                }
                showToast(messages.LOGGED_IN_SUCCESSFULLY, "success");
                window.location.replace("/dashboard");
            }
        } catch (err) {
            showToast(err.message || "OTP verification failed", "error");
        } finally {
            setIsLoading(false);
        }
    };

    const loginHTML = () => {
        return (
            <form onSubmit={handleSubmitLogin(handleLogin)}>
                <div className="mb-4">
                    <label htmlFor="email" className="form-label">Email </label>
                    <div className="input-group">
                        <span className="input-group-text"><i className="fas fa-envelope"></i></span>
                        <input
                            type="email"
                            className="form-control"
                            id="email"
                            placeholder="Enter Email Address"
                            {...registerLogin("email")}
                            disabled={isLoading || isSubmitDisable}
                            onKeyDown={preventSpaces}
                            defaultValue={process.env.NEXT_PUBLIC_APP_ENV === 'local' ? '<EMAIL>' : ''}
                        />
                    </div>
                    {loginErrors.email && <div className="invalid-feedback" style={{ display: 'block' }}>{loginErrors.email.message}</div>}
                </div>
                <div className="mb-4">
                    <label htmlFor="password" className="form-label">Password </label>
                    <div className="input-group">
                        <span className="input-group-text"><i className="fas fa-lock"></i></span>
                        <input
                            type={showPassword ? "text" : "password"}
                            className="form-control"
                            id="password"
                            placeholder="Enter Password"
                            {...registerLogin("password")}
                            disabled={isLoading || isSubmitDisable}
                            onKeyDown={preventSpaces}
                            defaultValue={process.env.NEXT_PUBLIC_APP_ENV === 'local' ? '123456789' : ''}
                        />
                        <button className="btn btn-outline-secondary" type="button" onClick={() => setShowPassword(!showPassword)}>
                            <i className={showPassword ? "fas fa-eye-slash" : "fas fa-eye"}></i>
                        </button>
                    </div>
                    {loginErrors.password && <div className="invalid-feedback" style={{ display: 'block' }}>{loginErrors.password.message}</div>}
                </div>
                <button type="submit" className="btn btn-primary w-100 login-btn" disabled={isLoading || isSubmitDisable}>
                    {isLoading || isSubmitDisable ? (
                        <><span className="spinner-border spinner-border-sm me-2" /> Signing in...</>
                    ) : (
                        <><i className="fas fa-sign-in-alt me-2"></i> Log In</>
                    )}
                </button>
            </form>
        )
    }

    const otpHTML = () => {
        return (
            <form onSubmit={handleSubmitOtp(handleOtpVerify)}>
                <div className="mb-4">
                    <label htmlFor="otp" className="form-label">Enter OTP <span className="required-asterisk">*</span></label>
                    <div className="input-group">
                        <input
                            className="form-control"
                            type="text"
                            id="otp"
                            onChange={(e) => setOtp(e.target.value)}
                            onKeyDown={(e) => {
                                onlyNumbers(e);
                                if (e.key === 'Enter') {
                                    e.preventDefault();
                                    handleSubmitOtp(handleOtpVerify)();
                                }
                            }}
                            onPaste={onlyNumbers}
                            placeholder="Enter OTP"
                            {...registerOtp("otp")}
                            maxLength={6}
                            disabled={isLoading || isSubmitDisable}
                        />
                    </div>
                    {otpErrors.otp && <div className="invalid-feedback" style={{ display: 'block' }}>{otpErrors.otp.message}</div>}
                </div>
                <button className="btn btn-success w-100 login-btn" type="submit" disabled={isLoading || isSubmitDisable}>
                    {isLoading || isSubmitDisable ? (
                        <><span className="spinner-border spinner-border-sm me-2" /> Verifying...</>
                    ) : (
                        <><i className="fas fa-check-circle me-2"></i> Verify OTP</>
                    )}
                </button>
            </form>
        )
    }
    
    return (
        <div className="login-container">
            {(isLoading || isSubmitDisable) && <Loader />}
            <div className="login-card" data-aos="fade-up" data-aos-duration="600">
                <div className="login-header">
                    <div className="logo-container">
                        <img src="/images/logo.png" alt="WeTrade Logo" className="logo" />
                    </div>
                    {stepRef.current === "credentials" && <p>Welcome Back!</p>}
                    {stepRef.current === "otp" && <p>Verify OTP</p>}
                </div>
                <div className="login-body">
                    {stepRef.current === "credentials" ? (
                        loginHTML()
                    ) : (
                        otpHTML()
                    )}
                </div>
            </div>
        </div>
    );
};

export default Login;