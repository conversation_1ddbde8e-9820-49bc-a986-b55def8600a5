import React, { forwardRef, useImperativeHandle, useState, useEffect } from 'react';

const CommonModal = forwardRef((props, ref) => {
  const { title, children, size = 'md', staticModal = false, className, icon = null } = props;
  const [show, setShow] = useState(false);
  const [visible, setVisible] = useState(false);

  useImperativeHandle(ref, () => ({
    open: () => {
      setShow(true);
      requestAnimationFrame(() => {
        document.body.style.overflow = 'hidden';
        document.body.classList.add("modal-open");
        setVisible(true);
      });
    },
    close: () => {
      document.body.classList.remove("modal-open");
      document.body.style.overflow = '';
      setVisible(false);
      setTimeout(() => {
        setShow(false);
      }, 300);
    },
  }));

  useEffect(() => {
    const handleEscape = (event) => {
      if (event.key === 'Escape' && !staticModal) {
        ref.current?.close();
      }
    };

    const handleOutsideClick = (event) => {
      if (event.target.classList.contains('modal') && !staticModal) {
        ref.current?.close();
      }
    };

    if (show) {
      window.addEventListener('keydown', handleEscape);
      window.addEventListener('mousedown', handleOutsideClick);
    }

    return () => {
      window.removeEventListener('keydown', handleEscape);
      window.removeEventListener('mousedown', handleOutsideClick);
    };
  }, [show, staticModal, ref]);

  const handleClose = () => {
    if (ref && ref.current) {
      ref.current.close();
    }
  };

  if (!show) return null;

  const titleIcon = title.includes('Edit') ? 'fa-pencil-square' :
    title.includes('Add') ? 'fa-plus-circle' :
      title.includes('Delete') ? 'fa-trash' :
        title.includes('View') ? 'fa-eye' : icon;

  return (
    <>
      <div
        className={`modal fade ${visible ? 'show' : ''} ${staticModal ? 'static-modal' : ''}`}
        style={{ display: 'block' }}
        tabIndex="-1"
        role="dialog"
        aria-modal="true"
        data-backdrop={staticModal ? "static" : ''}
      >
        <div className={`modal-dialog modal-dialog-centered modal-${size}`}>
          <div className="modal-content">
            <div className="modal-header">
              <h5 className="modal-title">
                <i className={`fas ${icon || titleIcon}`}></i>
                {title}
              </h5>
              <button
                type="button"
                className="btn-close"
                onClick={handleClose}
                aria-label="Close"
              />
            </div>
            <div className={`modal-body ${className || ''}`}>
              {children}
            </div>
          </div>
        </div>
      </div>
      <div className={`modal-backdrop fade ${visible ? 'show' : ''}`} />
    </>
  );
});

CommonModal.displayName = 'CommonModal';

export default CommonModal;