import React, { useState, useEffect, useCallback } from 'react';
import { UserService } from '@/services';
import { useTitle } from '@/hooks/useTitle';
import { showToast, getS3FilePath, ucwords, formatDateWithTime, formatTimestampToDate } from '@/utils/helper';
import { Loader, NotFound, Common } from '@/components';
import messages from '@/utils/messages';
import Link from 'next/link';

const UserDetails = ({ userId }) => {
    useTitle("User Details");
    const [isContentLoad, setIsContentLoad] = useState(false);
    const [userData, setUserData] = useState(null);
    const [userDetailsResStatus, setUserDetailsResStatus] = useState(null);
    const [errorMessage, setErrorMessage] = useState('');

    const getUserDetails = useCallback(async (userId) => {
        try {
            setIsContentLoad(true);
            const response = await UserService.getUserDetails(userId);
            if (Object.keys(response?.data || {}).length > 0) {
                setUserData(response?.data);
                setUserDetailsResStatus(response?.status);
            }
        } catch (error) {
            setUserDetailsResStatus(404);
            showToast(error?.message || 'Error getting details', 'error');
            setErrorMessage(error?.message || 'Error getting details');
        } finally {
            setIsContentLoad(false);
        }
    }, [userId]);

    useEffect(() => {
        if (userId) {
            getUserDetails(userId);
        }
    }, [getUserDetails, userId]);

    const userDetailsHTML = () => {
        return (
            <div className="user-profile-container">
                <div className="row">
                    <div className="col-md-6">
                        <div className="profile-card">
                            <h3 className="profile-details-title">Profile Details</h3>
                            <div className="profile-header">
                                <div className="profile-avatar-container">
                                    <img
                                        src={userData?.profileImage ? getS3FilePath(userData?.profileImage) : "/images/user.png"}
                                        alt={userData?.fullName?.trim() ? ucwords(userData.fullName) : 'Anonymous User'}
                                        className="profile-avatar"
                                    />
                                </div>
                                <div className="profile-info">
                                    <h2 className="profile-name">{ucwords(userData?.fullName)}</h2>
                                    <div className="profile-join-date">Joined {formatDateWithTime(userData?.registerTime)}</div>
                                    <div className="profile-stats">
                                        <div className="stat-item">
                                            <div className="stat-count">{userData?.followers ? userData?.followers.length : 0}</div>
                                            <div className="stat-label">Followers</div>
                                        </div>
                                        <div className="stat-item">
                                            <div className="stat-count">{userData?.followings ? userData?.followings.length : 0}</div>
                                            <div className="stat-label">Followings</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="profile-detail-item">
                                <div className="detail-label">Full Name:</div>
                                <div className="detail-value">{userData?.fullName?.trim() ? ucwords(userData.fullName) : 'Anonymous User'}</div>
                            </div>
                            <div className="profile-detail-item">
                                <div className="detail-label">User Name:</div>
                                <div className="detail-value">{userData?.userName?.trim() || 'anonymous'}</div>
                            </div>
                            <div className="profile-detail-item">
                                <div className="detail-label">Email:</div>
                                <div className="detail-value">{userData?.email?.trim() || 'Not Provided'}</div>
                            </div>
                            <div className="profile-detail-item">
                                <div className="detail-label">Phone Number:</div>
                                <div className="detail-value">{userData?.phoneNumber || 'Not Provided'}</div>
                            </div>
                            <div className="profile-detail-item">
                                <div className="detail-label">Date of Birth:</div>
                                <div className="detail-value">{userData?.birthdate ? formatTimestampToDate(userData.birthdate) : 'Not Provided'}</div>
                            </div>
                            <div className="profile-detail-item">
                                <div className="detail-label">Status:</div>
                                <div className="status-indicator d-flex align-items-center">
                                    <span className={`badge fs-6 px-2 py-1 rounded-pill ${userData?.isActive === 1 ? 'bg-success' : 'bg-danger'}`}>
                                        {userData?.isActive === 1 ? 'Active' : 'Inactive'}
                                    </span>
                                </div>
                            </div>
                            <div className="profile-detail-item">
                                <div className="detail-label">Account Created:</div>
                                <div className="detail-value">{formatDateWithTime(userData?.registerTime)}</div>
                            </div>
                            <div className="profile-detail-item">
                                <div className="detail-label">User Interests:</div>
                                <div className="detail-value">
                                    <div className="user-interests">
                                        {userData?.interests && Array.isArray(userData?.interests) && userData?.interests?.length > 0 ? (
                                            userData?.interests?.map((interest, index) => (
                                                <span key={index} className="interest-tag" style={{ cursor: 'pointer' }}>{interest?.name}</span>
                                            ))
                                        ) : (
                                            <span className="text-muted">No interests found</span>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="col-md-6">
                        <div className="user-lists">
                            <Common.FollowersFollowingList data={userData?.followers} title="Followers" />
                            <Common.FollowersFollowingList data={userData?.followings} title="Followings" />
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <>
            {isContentLoad && <Loader />}
            <div className="content-wrapper">
                {/* Header */}
                <div className="content-header d-flex justify-content-between align-items-center">
                    <h1>{userData?.fullName?.trim() ? ucwords(userData.fullName) : 'Anonymous User'} Details</h1>
                    <div className="d-flex gap-3">
                        <Link href="/user/list" className="btn btn-back">
                            <i className="fas fa-arrow-left me-2"></i>Back to Users List
                        </Link>
                    </div>
                </div>

                {/* Main content */}
                <div className="main-content">
                    {userData && Object.keys(userData).length > 0 && userDetailsResStatus && userDetailsResStatus === 200 ? (
                        userDetailsHTML()
                    ) : (
                        !isContentLoad && userDetailsResStatus && userDetailsResStatus !== 200 && (
                            <div className="col-12">
                                <div className="card shadow-sm border-0">
                                    <NotFound message={errorMessage || messages.USER_DETAILS_NOT_FOUND || 'User details not found'} />
                                </div>
                            </div>
                        )
                    )}
                </div>
            </div>
        </>
    );
};

export default UserDetails;