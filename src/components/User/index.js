import React, { useState, useEffect, useCallback, useRef } from 'react';
import { UserService } from '@/services';
import { useTitle } from '@/hooks/useTitle';
import { showToast, getSearchRecords } from '@/utils/helper';
import DataTable from 'react-data-table-component';
import { Loader, CommonModal, User, Common } from '@/components';
import { useRouter } from 'next/navigation';

const UserList = () => {
    useTitle("Users");
    const router = useRouter();
    const [isContentLoad, setIsContentLoad] = useState(false);
    const [search, setSearch] = useState('');
    const [data, setData] = useState([]);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [currentPage, setCurrentPage] = useState(1);
    const [isLoading, setIsLoading] = useState(false);
    const [isSubmitLoading, setIsSubmitLoading] = useState(false);
    const [selectedUser, setSelectedUser] = useState(null);
    const [statusChangeInfo, setStatusChangeInfo] = useState({
        id: null,
        status: false,
        title: ''
    });
    const [deleteInfo, setDeleteInfo] = useState({
        id: null,
        title: ''
    });
    const modelRefs = {
        formModalRef: useRef(null),
        formRef: useRef(null),
        statusModalRef: useRef(null),
        deleteModalRef: useRef(null)
    };

    const getList = useCallback(async (page, searchQuery) => {
        try {
            setIsContentLoad(true);
            const response = await UserService.getList(page, perPage, searchQuery);
            if (response?.data) {
                setData(response.data.data);
                setTotalRows(response.data.total);
            }
        } catch (error) {
            showToast(error?.message || 'Error getting details', 'error');
        } finally {
            setIsContentLoad(false);
        }
    }, [perPage]);

    useEffect(() => {
        getList(currentPage, search);
    }, [getList, currentPage]);

    const handlePageChange = useCallback((page) => {
        setCurrentPage(page);
    }, []);

    const handlePerRowsChange = useCallback(async (newPerPage, page) => {
        setPerPage(newPerPage);
        setCurrentPage(page);
    }, []);

    const handleSearch = useCallback(async (e) => {
        getSearchRecords(e, setSearch, setCurrentPage, getList);
    }, [getList]);

    const handleFormAction = useCallback((e, user = null) => {
        e.stopPropagation();
        if (user && user?.id) {
            setSelectedUser(user);
            modelRefs?.formModalRef.current?.open();
            requestAnimationFrame(() => {
                if (modelRefs?.formRef?.current) {
                    modelRefs.formRef.current.setFormValues(user);
                }
            });
        } else {
            setSelectedUser(null);
            modelRefs?.formModalRef.current?.open();
            requestAnimationFrame(() => {
                if (modelRefs?.formRef?.current) {
                    modelRefs.formRef.current.resetForm();
                }
            });
        }
    }, [modelRefs]);

    const handleFormSubmit = useCallback(async (formData) => {
        try {
            setIsSubmitLoading(true);
            requestAnimationFrame(() => {
                if (modelRefs?.formRef?.current) {
                    modelRefs.formRef.current.enableSubmit();
                }
            });
            if (selectedUser) {
                formData.append('id', selectedUser.id);
                formData.append('userId', selectedUser.userId);
            }
            const response = await UserService.updateUserDetails(formData);
            if (response?.status === 200) {
                requestAnimationFrame(() => {
                    if (modelRefs?.formModalRef?.current) {
                        modelRefs.formModalRef.current?.close();
                    }
                });
                await getList(currentPage, search);
                showToast(response?.message || 'User details saved successfully', 'success');
            }
        } catch (error) {
            showToast(error?.message || 'Error saving user details', 'error');
        } finally {
            setIsSubmitLoading(false);
            requestAnimationFrame(() => {
                if (modelRefs?.formRef?.current) {
                    modelRefs.formRef.current.disableSubmit();
                }
            });
        }
    }, [modelRefs, selectedUser, currentPage, search]);

    const handleActionClickEvent = useCallback(async (e, userId) => {
        try {
            e.stopPropagation();
            setIsLoading(true);
            const button = e.target.closest('button');
            if (!button) return;
            const title = button.dataset.bsTitle || '';
            if (title && title.toLowerCase().includes('view')) {
                router.push(`/user/details/${userId}`);
            }
        } catch (error) {
            setIsLoading(false);
            showToast(error?.message || 'Error navigating to user details', 'error');
        }
    }, [router]);

    const handleStatusChange = useCallback(async (e, id, userName) => {
        e.preventDefault();
        e.stopPropagation();
        const newStatus = e.target.checked ? 1 : 0;
        setStatusChangeInfo({ id, status: newStatus, title: userName });
        requestAnimationFrame(() => {
            if (modelRefs.statusModalRef.current) {
                modelRefs.statusModalRef.current?.open();
            }
        });
    }, [modelRefs]);

    const confirmStatusChange = useCallback(async () => {
        try {
            setIsSubmitLoading(true);
            const response = await UserService.updateUserStatus(statusChangeInfo.id, statusChangeInfo.status);
            if (response?.status === 200) {
                showToast(response?.message || 'Status updated successfully', "success");
                await getList(currentPage, search);
                requestAnimationFrame(() => {
                    if (modelRefs.statusModalRef.current) {
                        modelRefs.statusModalRef.current?.close();
                    }
                });
            }
        } catch (error) {
            showToast(error?.message || 'Error updating status', "error");
        } finally {
            setIsSubmitLoading(false);
            requestAnimationFrame(() => {
                if (modelRefs.statusModalRef.current) {
                    modelRefs.statusModalRef.current?.close();
                }
            });
        }
    }, [statusChangeInfo, getList, currentPage, search]);

    const handleDeleteClickEvent = useCallback(async (e, id, userName) => {
        e.preventDefault();
        e.stopPropagation();
        setDeleteInfo({ id, title: userName });
        requestAnimationFrame(() => {
            if (modelRefs.deleteModalRef.current) {
                modelRefs.deleteModalRef.current?.open();
            }
        });
    }, [modelRefs]);

    const confirmDelete = useCallback(async () => {
        try {
            setIsSubmitLoading(true);
            const response = await UserService.deleteUser(deleteInfo.id);
            if (response?.status === 200) {
                showToast(response?.message || 'User deleted successfully', "success");
                await getList(currentPage, search);
                requestAnimationFrame(() => {
                    if (modelRefs.deleteModalRef.current) {
                        modelRefs.deleteModalRef.current?.close();
                    }
                });
            }
        } catch (error) {
            showToast(error?.message || 'Error deleting user', "error");
        } finally {
            setIsSubmitLoading(false);
            requestAnimationFrame(() => {
                if (modelRefs.deleteModalRef.current) {
                    modelRefs.deleteModalRef.current?.close();
                }
            });
        }
    }, [deleteInfo, getList, currentPage, search]);

    const columns = [
        {
            name: 'No.',
            selector: (row, index) => ((currentPage - 1) * perPage) + index + 1,
            width: '80px',
        },
        {
            name: 'User Name',
            selector: (row) => row?.userName || '',
            sortable: true,
            wrap: true,
            grow: 2
        },
        {
            name: 'Full Name',
            selector: (row) => row?.fullName || '',
            sortable: true,
            wrap: true,
            grow: 2
        },
        {
            name: 'Email',
            selector: (row) => row?.email || '',
            sortable: true,
            wrap: true,
            grow: 2
        },
        {
            name: 'Phone No.',
            selector: (row) => row?.phoneNumber || '',
        },
        {
            name: 'Status',
            selector: (row) => (
                <div className="form-check form-switch">
                    <input
                        className="form-check-input"
                        type="checkbox"
                        checked={row?.isActive === 1}
                        onChange={(e) => handleStatusChange(e, row.id, row.userName)}
                    />
                </div>
            ),
            center: 'true',
        },
        {
            name: 'Action',
            cell: (row) => (
                <div className="d-flex gap-1">
                    <button
                        type="button"
                        className="btn btn-sm btn-info"
                        onClick={(e) => handleActionClickEvent(e, row.userId)}
                        disabled={isLoading || isSubmitLoading}
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        data-bs-title="View User"
                    >
                        <i className="bi bi-eye"></i>
                    </button>
                    <button
                        type="button"
                        className="btn btn-sm btn-primary"
                        onClick={(e) => handleFormAction(e, row)}
                        disabled={isLoading || isSubmitLoading}
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        data-bs-title="Edit User"
                    >
                        <i className="bi bi-pencil"></i>
                    </button>
                    <button
                        type="button"
                        className="btn btn-sm btn-danger"
                        disabled={isLoading || isSubmitLoading}
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        data-bs-title="Delete User"
                        onClick={(e) => handleDeleteClickEvent(e, row.id, row.userName)}
                    >
                        <i className="bi bi-trash"></i>
                    </button>
                </div>
            ),
            ignoreRowClick: true,
            center: 'true',
        },
    ];

    const customStyles = {
        rows: { style: { minHeight: '50px' } },
        headCells: {
            style: {
                fontWeight: 'bold',
                fontSize: '14px',
            },
        },
    };

    return (
        <>
            {(isSubmitLoading || isLoading) && <Loader />}
            <div className="content-wrapper">
                {/* Header */}
                <div className="content-header">
                    <h1 id="page-title">Users List</h1>
                </div>

                {/* Main content */}
                <div className="main-content">
                    <div className="card">
                        <div className="card-body">
                            <div className="d-flex justify-content-between align-items-center mb-4">
                                <div className="entries-select"></div>
                                <div className="search-box position-relative">
                                    <label>Search:</label>
                                    <input
                                        type="text"
                                        placeholder="Search user..."
                                        className="form-control form-control-sm"
                                        value={search}
                                        onChange={handleSearch}
                                    />
                                    {search && (
                                        <button
                                            className="btn btn-sm btn-light position-absolute"
                                            style={{
                                                right: '5px',
                                                top: '50%',
                                                transform: 'translateY(-50%)',
                                                padding: '0.25rem 0.5rem',
                                                borderRadius: '50%',
                                                boxShadow: '0 0 3px rgba(0,0,0,0.2)',
                                                backgroundColor: '#7c66d8',
                                                color: 'white'
                                            }}
                                            onClick={async () => {
                                                setSearch('');
                                                setCurrentPage(1);
                                                await getList(1, '');
                                            }}
                                        >
                                            <i className="bi bi-x-lg fw-bold"></i>
                                        </button>
                                    )}
                                </div>
                            </div>

                            <div className="table">
                                <DataTable
                                    columns={columns}
                                    data={data}
                                    pagination
                                    paginationServer
                                    paginationTotalRows={totalRows}
                                    onChangeRowsPerPage={handlePerRowsChange}
                                    onChangePage={handlePageChange}
                                    highlightOnHover
                                    striped
                                    responsive
                                    customStyles={customStyles}
                                    progressPending={isContentLoad}
                                    progressComponent={<Common.DataTableLoader />}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Form Modal */}
            <CommonModal ref={modelRefs?.formModalRef} title={`${selectedUser && selectedUser?.id ? 'Edit' : 'Add New'} User`}>
                <User.Form
                    ref={modelRefs?.formRef}
                    onSubmit={handleFormSubmit}
                    cancelForm={() => {
                        requestAnimationFrame(() => {
                            if (modelRefs?.formModalRef.current) {
                                modelRefs?.formModalRef.current?.close();
                            }
                        });
                    }}
                />
            </CommonModal>

            {/* Status Change Modal */}
            <CommonModal
                ref={modelRefs.statusModalRef}
                staticModal={true}
                icon={statusChangeInfo.status === 1 ? 'bi bi-check-circle-fill' : 'bi bi-x-circle-fill'}
                title={`${statusChangeInfo.status === 1 ? 'Activate' : 'Deactivate'} User`}
            >
                <Common.ActiveInActive
                    statusChangeInfo={statusChangeInfo}
                    onConfirm={confirmStatusChange}
                    onCancel={() => {
                        requestAnimationFrame(() => {
                            if (modelRefs.statusModalRef.current) {
                                modelRefs.statusModalRef.current?.close();
                            }
                        });
                    }}
                />
            </CommonModal>

            {/* Delete Modal */}
            <CommonModal
                ref={modelRefs.deleteModalRef}
                staticModal={true}
                title="Delete User"
            >
                <Common.DeleteConfirmation deleteInfo={deleteInfo} onConfirm={confirmDelete} onCancel={() => {
                    requestAnimationFrame(() => {
                        if (modelRefs.deleteModalRef.current) {
                            modelRefs.deleteModalRef.current?.close();
                        }
                    });
                }} />
            </CommonModal>
        </>
    );
};

export default UserList;
