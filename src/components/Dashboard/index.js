import React, { useState, useEffect, useCallback } from 'react';
import { useTitle } from '@/hooks/useTitle';
import { Loader } from '@/components';
import { showToast } from '@/utils/helper';
import CountUp from 'react-countup';
import { DashboardService } from '@/services';

const Dashboard = () => {
    useTitle("Dashboard");
    const [statistics, setStatistics] = useState({});
    const [isLoading, setIsLoading] = useState(false);

    const getStatistics = useCallback(async () => {
        try {
            setIsLoading(true);
            const response = await DashboardService.getStatistics();
            if (Object.keys(response?.data || {}).length > 0) {
                setStatistics(response.data);
            }
        } catch (error) {
            showToast(error?.message || 'Error getting details', 'error');
        } finally {
            setIsLoading(false);
        }
    }, []);

    useEffect(() => {
        getStatistics();
    }, [getStatistics]);

    const dashboardStats = [
        {
            title: "Total Users",
            value: statistics && statistics?.total_users || 0,
            icon: "fas fa-users",
            cardClass: "users",
        },
        {
            title: "Active Users",
            value: statistics && statistics?.total_active_users || 0,
            icon: "fas fa-user-plus",
            cardClass: "active-users",
        },
        {
            title: "Inactive Users",
            value: statistics && statistics?.total_inactive_users || 0,
            icon: "fas fa-user-times",
            cardClass: "inactive-users",
        },
        {
            title: "Web Users",
            value: statistics && statistics?.total_web_users || 0,
            icon: "fas fa-globe",
            cardClass: "web-users",
        },
        {
            title: "Total Interests",
            value: statistics && statistics?.total_interests || 0,
            icon: "fas fa-star",
            cardClass: "interests",
        },
        {
            title: "Active Games",
            value: statistics && statistics?.active_games || 0,
            icon: "fas fa-gamepad",
            cardClass: "games",
        },
        {
            title: "Total Ideas",
            value: statistics && statistics?.total_ideas || 0,
            icon: "fas fa-lightbulb",
            cardClass: "ideas",
        },
        {
            title: "Stocks",
            value: statistics && statistics?.total_stocks || 0,
            icon: "fas fa-chart-line",
            cardClass: "stocks",
        },
        {
            title: "Crypto",
            value: statistics && statistics?.total_crypto || 0,
            icon: "fab fa-bitcoin",
            cardClass: "crypto",
        },
    ];

    const dashboardHTML = (data = []) => {
        return (
            data && Array.isArray(data) && data?.length > 0 && data.map((stat, index) => (
                <div
                    className="col-xl-4 col-md-6 mb-4"
                    data-aos="fade-up"
                    data-aos-duration={index * 50}
                    key={stat.title}
                >
                    <div className={`dashboard-stat-card ${stat.cardClass}`}>
                        <h3>{stat.title}</h3>
                        <div className="dashboard-stat-value">
                            <CountUp end={parseInt(stat.value, 10)} duration={2}
                                separator=","
                                decimals={0}
                                enableScrollSpy={true}
                                scrollSpyDelay={200} />
                        </div>
                        <div className="dashboard-stat-icon">
                            <i className={stat.icon}></i>
                        </div>
                    </div>
                </div>
            ))
        );
    };

    return (
        <div className="content-wrapper">
            {isLoading && <Loader />}
            <div className="content-header">
                <div className="d-flex align-items-center">
                    <i className="fas fa-chart-line me-2 fs-3"></i>
                    <h1 id="page-title">Statistics Overview</h1>
                </div>
            </div>

            <div className="dashboard-overview">
                <div className="row">
                    {dashboardHTML(dashboardStats || [])}
                </div>
            </div>
        </div>
    );
};

export default Dashboard;