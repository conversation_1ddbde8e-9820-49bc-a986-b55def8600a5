/* eslint-disable @next/next/no-img-element */
"use client";
import React, { useState, useCallback, useRef, useEffect } from "react";
import Link from "next/link";
import { signOut } from 'next-auth/react';
import { usePathname, useRouter } from "next/navigation";
import { useDispatch } from 'react-redux';
import { clearUser } from '@/store/slices/userSlice';
import { showToast } from "@/utils/helper";
import messages from "@/utils/messages";

const menuItems = [
    { text: "Dashboard", path: "/dashboard", icon: "fas fa-tachometer-alt", module: "dashboard" },
    { text: "Users", path: "/user/list", icon: "fas fa-users", module: "user" },
    { text: "Web Users", path: "/webuser/list", icon: "fas fa-globe-americas", module: "webuser" },
    { text: "Interests", path: "/interest/list", icon: "fas fa-star", module: "interest" },
    { text: "Ideas", path: "/idea/list", icon: "fas fa-lightbulb", module: "idea" },
    { text: "Stocks", path: "/stock/list", icon: "fas fa-chart-line", module: "stock" },
    { text: "Crypto", path: "/crypto/list", icon: "fab fa-bitcoin", module: "crypto" },
    { text: "Games", path: "/game/list", icon: "fas fa-gamepad", module: "game" },
];

const Sidebar = () => {
    const pathname = usePathname();
    const [isLogoutClicked, setIsLogoutClicked] = useState(false);
    const [loadingItem, setLoadingItem] = useState(null);
    const dispatch = useDispatch();
    const router = useRouter();
    const sidebarRef = useRef(null);
    const contentRef = useRef(null);
    const navigationStartedRef = useRef(false);

    useEffect(() => {
        setLoadingItem(null);
        navigationStartedRef.current = false;
    }, [pathname]);

    const handleSignOut = useCallback(async (event) => {
        event.preventDefault();
        try {
            setIsLogoutClicked(true);
            const body = document.body;
            if (body) {
                body.insertAdjacentHTML('beforeend', '<div class="loader-overlay"></div>');
            }
            dispatch(clearUser());
            await signOut({ redirect: false });
            showToast(messages.LOGGED_OUT_SUCCESSFULLY, "success");
            window.location.replace('/auth/login');
        } catch (error) {
            setIsLogoutClicked(false);
            showToast(messages.LOGGED_OUT_SUCCESSFULLY, "success");
            const body = document.body;
            if (body && document.readyState === 'complete') {
                const loader = body.querySelector('.loader-overlay');
                if (loader) loader.remove();
            }
        }
    }, [dispatch]);

    const isMenuActive = useCallback((menuPath, module = null) => {
        if (pathname === menuPath) return true;

        if (module && pathname.startsWith('/' + module + '/')) {
            return ['add', 'edit', 'details', 'list'].some(subpath =>
                pathname.includes(`/${module}/${subpath}`)
            );
        }

        return false;
    }, [pathname]);

    const handleCloseSidebar = useCallback((e, path, itemModule) => {
        e.preventDefault();
        if ((loadingItem === path) || (pathname === path)) return;
        const currentPath = path || "#";
        if (currentPath !== "#" && currentPath !== pathname) {
            setLoadingItem(currentPath);
            navigationStartedRef.current = true;
        }

        const isMobileOrTablet = typeof window !== 'undefined' && window.matchMedia("(max-width: 991px)").matches;
        if (isMobileOrTablet) {
            const content = contentRef.current || document.getElementById('content');
            const sidebar = sidebarRef.current || document.getElementById('sidebar');

            if (!contentRef.current && content) contentRef.current = content;
            if (!sidebarRef.current && sidebar) sidebarRef.current = sidebar;

            if (content && sidebar && content.classList.contains('active') && sidebar.classList.contains('active')) {
                content.classList.remove('active');
                sidebar.classList.remove('active');
            }
        }

        if (currentPath && currentPath !== "#") {
            try {
                router.push(currentPath);
                setTimeout(() => {
                    if (navigationStartedRef.current && loadingItem === currentPath) {
                        setLoadingItem(null);
                        navigationStartedRef.current = false;
                    }
                }, 3000);
            } catch (error) {
                setLoadingItem(null);
                navigationStartedRef.current = false;
            }
        }
    }, [router, pathname, loadingItem]);

    return (
        <nav id="sidebar" ref={sidebarRef}>
            <div className="sidebar-header">
                <img src="/images/logo.png" alt="WeTrade Logo" className="logo" style={{ cursor: 'pointer' }} onClick={() => router.push('/')} />
            </div>
            <ul className="list-unstyled components">
                {menuItems.map((item, index) => (
                    <li key={index} className={isMenuActive(item.path, item.module) ? "active" : ""} onClick={(e) => handleCloseSidebar(e, item.path, item.module)}>
                        <Link href={item.path} style={{ cursor: loadingItem === item.path ? 'not-allowed' : 'pointer' }}>
                            {loadingItem === item.path ? (
                                <>
                                    <i className="fas fa-spinner fa-spin" style={{ color: "#6bca16" }}></i>
                                    <span>Go to {item.text.toLowerCase()}...</span>
                                </>
                            ) : (
                                <>
                                    <i className={item.icon}></i>
                                    <span>{item.text}</span>
                                </>
                            )}
                        </Link>
                    </li>
                ))}
            </ul>
            <div className="sidebar-footer">
                <Link href="#" className="logout-link" onClick={handleSignOut} disabled={isLogoutClicked}>
                    {isLogoutClicked ? (
                        <>
                            <i className="fas fa-spinner fa-spin" style={{ color: "#6bca16" }}></i>
                            <span>Logging out...</span>
                        </>
                    ) : (
                        <>
                            <i className="fas fa-sign-out-alt"></i>
                            <span>Logout</span>
                        </>
                    )}
                </Link>
            </div>
        </nav>
    );
};

export default Sidebar;