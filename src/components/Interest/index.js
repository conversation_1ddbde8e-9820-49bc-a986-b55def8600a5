import React, { useState, useEffect, useCallback, useRef } from 'react';
import { InterestService } from '@/services';
import { useTitle } from '@/hooks/useTitle';
import { showToast, getSearchRecords, ucwords } from '@/utils/helper';
import DataTable from 'react-data-table-component';
import { Loader, CommonModal, Interest, Common } from '@/components';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

const InterestList = ({ interestId = 0, interestName = '' }) => {
    const isSubInterest = (interestId && interestId !== 0 && interestId !== null) || false;
    useTitle(isSubInterest ? "Interest Details" : "Interests");
    const router = useRouter();
    const [isContentLoad, setIsContentLoad] = useState(false);
    const [search, setSearch] = useState('');
    const [data, setData] = useState([]);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [currentPage, setCurrentPage] = useState(1);
    const [isLoading, setIsLoading] = useState(false);
    const [isSubmitLoading, setIsSubmitLoading] = useState(false);
    const [selectedInterest, setSelectedInterest] = useState(null);
    const [statusChangeInfo, setStatusChangeInfo] = useState({
        id: null,
        status: false,
        title: ''
    });
    const modelRefs = {
        formModalRef: useRef(null),
        formRef: useRef(null),
        statusModalRef: useRef(null),
    };

    const isInterestEditable = (id, name) => {
        if (id && id !== 0 && id !== null && name && (name.toLowerCase().trim() !== 'crypto' && name.toLowerCase().trim() !== 'stocks' && name.toLowerCase().trim() !== 'topics')) {
            return true;
        }
        return false;
    }

    const getList = useCallback(async (page, searchQuery) => {
        try {
            setIsContentLoad(true);
            const response = await InterestService.getList(page, perPage, searchQuery, interestId);
            if (response?.data) {
                setData(response.data.data);
                setTotalRows(response.data.total);
            }
        } catch (error) {
            showToast(error?.message || 'Error getting details', 'error');
        } finally {
            setIsContentLoad(false);
        }
    }, [perPage]);

    useEffect(() => {
        getList(currentPage, search);
    }, [getList, currentPage]);

    const handlePageChange = useCallback((page) => {
        setCurrentPage(page);
    }, []);

    const handlePerRowsChange = useCallback(async (newPerPage, page) => {
        setPerPage(newPerPage);
        setCurrentPage(page);
    }, []);

    const handleSearch = useCallback(async (e) => {
        getSearchRecords(e, setSearch, setCurrentPage, getList);
    }, [getList]);

    const handleFormAction = useCallback((e, interest = null) => {
        e.stopPropagation();
        if (interest && interest?.id) {
            setSelectedInterest(interest);
            modelRefs?.formModalRef.current?.open();
            requestAnimationFrame(() => {
                if (modelRefs?.formRef?.current) {
                    modelRefs.formRef.current.setFormValues(interest);
                }
            });
        } else {
            setSelectedInterest(null);
            modelRefs?.formModalRef.current?.open();
            requestAnimationFrame(() => {
                if (modelRefs?.formRef?.current) {
                    modelRefs.formRef.current.resetForm();
                }
            });
        }
    }, [modelRefs]);

    const handleFormSubmit = useCallback(async (formData) => {
        try {
            setIsSubmitLoading(true);
            requestAnimationFrame(() => {
                if (modelRefs?.formRef?.current) {
                    modelRefs.formRef.current.enableSubmit();
                }
            });
            if (selectedInterest) {
                formData.append('id', selectedInterest.id);
            }
            formData.append('parent_id', (interestId && interestId !== null && interestId !== 0) ? interestId : 0);
            const response = await InterestService.updateInterestDetails(formData);
            if (response?.status === 200) {
                requestAnimationFrame(() => {
                    if (modelRefs?.formModalRef?.current) {
                        modelRefs.formModalRef.current?.close();
                    }
                });
                await getList(currentPage, search);
                showToast(response?.message || 'Interest details saved successfully', 'success');
            }
        } catch (error) {
            showToast(error?.message || 'Error saving interest details', 'error');
        } finally {
            setIsSubmitLoading(false);
            requestAnimationFrame(() => {
                if (modelRefs?.formRef?.current) {
                    modelRefs.formRef.current.disableSubmit();
                }
            });
        }
    }, [modelRefs, selectedInterest, currentPage, search, isSubInterest, interestId]);

    const handleActionClickEvent = useCallback(async (e, id, name) => {
        try {
            e.stopPropagation();
            setIsLoading(true);
            const button = e.target.closest('button');
            if (!button) return;
            const title = button.dataset.bsTitle || '';
            if (title && title.toLowerCase().includes('view')) {
                router.push(`/interest/details/${id}?name=${name.replace(/\s+/g, '-').toLowerCase()}`);
            }
        } catch (error) {
            setIsLoading(false);
            showToast(error?.message || 'Error navigating to interest details', 'error');
        }
    }, [router]);

    const handleStatusChange = useCallback(async (e, id, name) => {
        e.preventDefault();
        e.stopPropagation();
        const newStatus = e.target.checked ? 1 : 0;
        setStatusChangeInfo({ id, status: newStatus, title: name });
        requestAnimationFrame(() => {
            if (modelRefs.statusModalRef.current) {
                modelRefs.statusModalRef.current?.open();
            }
        });
    }, [modelRefs]);

    const confirmStatusChange = useCallback(async () => {
        try {
            setIsSubmitLoading(true);
            const response = await InterestService.updateInterestStatus(statusChangeInfo.id, statusChangeInfo.status);
            if (response?.status === 200) {
                showToast(response?.message || 'Status updated successfully', "success");
                await getList(currentPage, search);
                requestAnimationFrame(() => {
                    if (modelRefs.statusModalRef.current) {
                        modelRefs.statusModalRef.current?.close();
                    }
                });
            }
        } catch (error) {
            showToast(error?.message || 'Error updating status', "error");
        } finally {
            setIsSubmitLoading(false);
            requestAnimationFrame(() => {
                if (modelRefs.statusModalRef.current) {
                    modelRefs.statusModalRef.current?.close();
                }
            });
        }
    }, [statusChangeInfo, getList, currentPage, search]);

    const columns = [
        {
            name: 'No.',
            selector: (row, index) => ((currentPage - 1) * perPage) + index + 1,
            width: '80px',
        },
        {
            name: 'Interest',
            selector: (row) => row?.name || '',
            sortable: true,
            wrap: true,
            grow: 2
        },
        {
            name: 'Is Active?',
            selector: (row) => (
                <div className="form-check form-switch">
                    <input
                        className="form-check-input"
                        type="checkbox"
                        checked={row?.isActive === 1}
                        onChange={(e) => handleStatusChange(e, row.id, row.name)}
                    />
                </div>
            ),
            center: 'true',
        },
        {
            name: 'Action',
            cell: (row) => (
                <div className="d-flex gap-1">
                    {(!interestId || interestId === 0) && <button
                        type="button"
                        className="btn btn-sm btn-info"
                        onClick={(e) => handleActionClickEvent(e, row.id, row.name)}
                        disabled={isLoading}
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        data-bs-title="View Interest"
                    >
                        <i className="bi bi-eye"></i>
                    </button>
                    }
                    {isInterestEditable(row.id, row.name) && (
                        <button
                            type="button"
                            className="btn btn-sm btn-primary"
                            onClick={(e) => handleFormAction(e, row)}
                            disabled={isLoading}
                            data-bs-toggle="tooltip"
                            data-bs-placement="top"
                            data-bs-title="Edit Interest"
                        >
                            <i className="bi bi-pencil"></i>
                        </button>
                    )}
                </div>
            ),
            ignoreRowClick: true,
            center: 'true',
        },
    ];

    const customStyles = {
        rows: { style: { minHeight: '50px' } },
        headCells: {
            style: {
                fontWeight: 'bold',
                fontSize: '14px',
            },
        },
    };

    return (
        <>
            {(isSubmitLoading || isLoading) && <Loader />}
            <div className="content-wrapper">
                {/* Header */}
                <div className="content-header d-flex justify-content-between align-items-center">
                    <h1 id="page-title">{isSubInterest && !interestName ? 'Sub Interests List' : 'Interests List'} {isSubInterest && interestName ? `of ${ucwords(interestName.replace(/-/g, ' '))}` : ''}</h1>
                    <div className="d-flex gap-3">
                        {isSubInterest && (
                            <Link href="/interest/list" className="btn btn-back">
                                <i className="fas fa-arrow-left me-2"></i>Back to Interests List
                            </Link>
                        )}
                        <button type="button" className="btn btn-add" onClick={handleFormAction}>
                            <i className="fas fa-plus me-1"></i> {isSubInterest ? 'Add Sub Interest' : 'Add Interest'}
                        </button>
                    </div>
                </div>

                {/* Main content */}
                <div className="main-content">
                    <div className="card">
                        <div className="card-body">
                            <div className="d-flex justify-content-between align-items-center mb-4">
                                <div className="entries-select"></div>
                                <div className="search-box position-relative">
                                    <label>Search:</label>
                                    <input
                                        type="text"
                                        placeholder={`Search ${isSubInterest ? 'sub interest' : 'interest'}...`}
                                        className="form-control form-control-sm"
                                        value={search}
                                        onChange={handleSearch}
                                    />
                                    {search && (
                                        <button
                                            className="btn btn-sm btn-light position-absolute"
                                            style={{
                                                right: '5px',
                                                top: '50%',
                                                transform: 'translateY(-50%)',
                                                padding: '0.25rem 0.5rem',
                                                borderRadius: '50%',
                                                boxShadow: '0 0 3px rgba(0,0,0,0.2)',
                                                backgroundColor: '#7c66d8',
                                                color: 'white'
                                            }}
                                            onClick={async () => {
                                                setSearch('');
                                                setCurrentPage(1);
                                                await getList(1, '');
                                            }}
                                        >
                                            <i className="bi bi-x-lg fw-bold"></i>
                                        </button>
                                    )}
                                </div>
                            </div>

                            <div className="table">
                                <DataTable
                                    columns={columns}
                                    data={data}
                                    pagination
                                    paginationServer
                                    paginationTotalRows={totalRows}
                                    onChangeRowsPerPage={handlePerRowsChange}
                                    onChangePage={handlePageChange}
                                    highlightOnHover
                                    striped
                                    responsive
                                    customStyles={customStyles}
                                    progressPending={isContentLoad}
                                    progressComponent={<Common.DataTableLoader />}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Form Modal */}
            <CommonModal ref={modelRefs?.formModalRef} title={`${selectedInterest && selectedInterest?.id ? 'Edit' : 'Add New'} ${isSubInterest ? 'Sub Interest' : 'Interest'}`}>
                <Interest.Form
                    ref={modelRefs?.formRef}
                    onSubmit={handleFormSubmit}
                    cancelForm={() => {
                        requestAnimationFrame(() => {
                            if (modelRefs?.formModalRef.current) {
                                modelRefs?.formModalRef.current?.close();
                            }
                        });
                    }}
                    isSubInterest={isSubInterest}
                    interestName={interestName}
                />
            </CommonModal>

            {/* Status Change Modal */}
            <CommonModal
                ref={modelRefs.statusModalRef}
                staticModal={true}
                icon={statusChangeInfo.status === 1 ? 'bi bi-check-circle-fill' : 'bi bi-x-circle-fill'}
                title={`${statusChangeInfo.status === 1 ? 'Activate' : 'Deactivate'} ${isSubInterest ? 'Sub Interest' : 'Interest'}`}
            >
                <Common.ActiveInActive
                    statusChangeInfo={statusChangeInfo}
                    onConfirm={confirmStatusChange}
                    onCancel={() => {
                        requestAnimationFrame(() => {
                            if (modelRefs.statusModalRef.current) {
                                modelRefs.statusModalRef.current?.close();
                            }
                        });
                    }}
                />
            </CommonModal>
        </>
    );
};

export default InterestList;
