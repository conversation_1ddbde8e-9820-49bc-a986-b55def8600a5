import React, { forwardRef, useImperativeHandle, useState, useCallback, useRef } from 'react';
import { useFormFields, Input, InputRadioSwitch, SearchableSelect } from '@/hooks/useFormRefs';
import { FileUploader } from '@/hooks/useFileRefs';
import { handleFileUpload, showToast, handleSelectChange, getS3FilePath } from '@/utils/helper';
import { StockService } from '@/services';
import * as yup from 'yup';


const Form = forwardRef(({ onSubmit, cancelForm, isSubInterest = false, interestName = '' }, ref) => {
    const INTEREST_NAME = interestName ? interestName.toLowerCase() : '';
    const IS_STOCK_OR_CRYPTO_INTEREST = isSubInterest && INTEREST_NAME && (INTEREST_NAME === 'crypto' || INTEREST_NAME === 'stocks');

    const interestValidationSchema = yup.object().shape({
        name: IS_STOCK_OR_CRYPTO_INTEREST ? yup.string().trim() : yup.string().trim().required('Interest name is required').min(3, 'Interest name must be at least 3 characters long').max(50, 'Interest name must be less than 50 characters long'),
        stock_id: yup.string()
            .when([], {
                is: () => IS_STOCK_OR_CRYPTO_INTEREST,
                then: (schema) => schema
                    .required('Please select interest.')
                    .test('is-valid', 'Please select interest.', value =>
                        value && value.trim() !== ''
                    ),
                otherwise: (schema) => schema.notRequired()
            }),
    });

    const {
        register,
        handleSubmit,
        errors,
        reset,
        watch,
        setValue,
        trigger,
        createFormData,
    } = useFormFields(interestValidationSchema);

    const formValues = watch();
    const [id, setId] = useState(null);
    const [isSubmitEnable, setIsSubmitEnable] = useState(false);
    const [selectedOption, setSelectedOption] = useState('');
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const interestImageRef = useRef(null);

    useImperativeHandle(ref, () => ({
        resetForm: () => {
            setId(null);
            reset();
        },
        setFormValues: async (data) => {
            if (data && data?.id) {
                setId(data.id);
            }
            let includedFields = ['name', 'isActive'];
            if (IS_STOCK_OR_CRYPTO_INTEREST) {
                includedFields.push('stock_id');
            }
            if (isSubInterest && data?.image && typeof data?.image === 'string') {
                const imagePath = getS3FilePath(data?.image);
                interestImageRef?.current?.setFilePreview(imagePath);
                setValue('image', data?.image);
            }
            Object.entries(data).forEach(([key, value]) => {
                if (includedFields.includes(key) && value) {
                    if (key === 'stock_id') {
                        setSelectedOption({
                            value: value,
                            label: `${data['stockDetails']?.symbol} : ${data['stockDetails']?.description}`
                        });
                        setValue('stock_id', value);
                    } else {
                        setValue(key, value);
                    }
                }
            });
        },
        disableSubmit: () => {
            setIsSubmitEnable(false);
        },
        enableSubmit: () => {
            setIsSubmitEnable(true);
        }
    }));

    const handleFormSubmit = useCallback((data) => {
        const formData = createFormData(data);
        const isActive = data?.isActive ? 1 : 0;
        formData.append('isActive', isActive);
        if (IS_STOCK_OR_CRYPTO_INTEREST) {
            formData.append('stock_id', data?.stock_id);
        }
        let isInterestImageRemoved = false;
        Object.entries(data).forEach(([key, value]) => {
            if (isSubInterest) {
                if (key === 'image' && value && interestImageRef?.current && typeof value !== 'object') {
                    delete data[key];
                } else if (key === 'image' && !value && interestImageRef?.current) {
                    isInterestImageRemoved = true;
                }
            }
        });
        if (isInterestImageRemoved) {
            formData.append('image', '');
        }
        formData.append('isSubInterest', isSubInterest);
        onSubmit(formData);
    }, [createFormData, onSubmit, INTEREST_NAME, isSubInterest]);

    const handleCancel = useCallback((e) => {
        e.preventDefault();
        ref.current?.resetForm();
        if (cancelForm) {
            cancelForm();
        }
    }, [cancelForm, ref]);

    const getStocks = useCallback(async (search, loadedOptions, { page }) => {
        if (!search || search.trim().length === 0) {
            return {
                options: [],
                hasMore: false,
                additional: { page: 1 },
            };
        }
        try {
            const perPage = 10;
            const type = INTEREST_NAME === 'crypto' ? 'crypto' : 'stock';
            const response = await StockService.getList(page, perPage, search, type);
            const responseData = response.data || {};
            const rows = responseData.data || [];
            const total = responseData.total || 0;
            let options = [];

            if (rows?.length) {
                const uniqueIds = new Set();
                options = rows.filter(row => {
                    if (!uniqueIds.has(row.id)) {
                        uniqueIds.add(row.id);
                        return true;
                    }
                    return false;
                }).map(row => ({
                    value: row.id,
                    label: `${row.symbol} : ${row.description}`
                }));
            }
            const hasMore = page * perPage < total;
            return {
                options: options,
                hasMore: hasMore,
                additional: {
                    page: page + 1,
                },
            };
        } catch (error) {
            showToast(error.message || 'Error loading stock options', 'error');
            return {
                options: [],
                hasMore: false,
                additional: { page: 1 },
            };
        }
    }, [INTEREST_NAME]);

    const handleSearchSelectOnChange = useCallback((selected) => {
        handleSelectChange('stock_id', selected, setValue, trigger);
        setSelectedOption(selected || '');
        setIsMenuOpen(selected ? false : true);
        if (selected && selected.label) {
            const lastColonIndex = selected.label.lastIndexOf(':');
            const selectedInterestName = lastColonIndex !== -1
                ? selected.label.substring(0, lastColonIndex).trim()
                : selected.label.trim();
            setValue('name', selectedInterestName);
        }
    }, [handleSelectChange, setValue, trigger]);

    const formHTML = () => {
        return (
            <form onSubmit={handleSubmit(handleFormSubmit)} encType="multipart/form-data" noValidate>
                {(!INTEREST_NAME || (INTEREST_NAME && INTEREST_NAME !== 'crypto' && INTEREST_NAME !== 'stocks')) && (
                    <div className="mb-3">
                        <Input
                            label="Interest"
                            name="name"
                            type="text"
                            {...register('name')}
                            placeholder="Enter interest name"
                            value={formValues?.name || ''}
                            icon="fas fa-tag"
                            error={errors?.name?.message}
                        />
                    </div>)}
                {IS_STOCK_OR_CRYPTO_INTEREST && (
                    <div className="mb-3">
                        <SearchableSelect
                            name="stock_id"
                            label="Interest"
                            value={isMenuOpen ? null : selectedOption}
                            onChange={handleSearchSelectOnChange}
                            loadOptions={getStocks}
                            placeholder="Search for interest"
                            error={errors?.stock_id?.message}
                            onMenuOpen={() => setIsMenuOpen(true)}
                            onMenuClose={() => setIsMenuOpen(false)}
                        />
                    </div>
                )}
                <InputRadioSwitch
                    label="Is Active?"
                    name="isActive"
                    {...register('isActive')}
                    value={formValues?.isActive || 0}
                    onChange={(checked) => setValue('isActive', checked ? 1 : 0)}
                />
                {isSubInterest && (
                    <div className="mb-3">
                        <div className="row">
                            <div className="col-12">
                                <FileUploader
                                    label="Image"
                                    isRequired={false}
                                    uploadType="image"
                                    error={errors.image?.message}
                                    ref={interestImageRef}
                                    onChange={(file) => handleFileUpload('image', file, setValue, trigger)}
                                />
                            </div>
                        </div>
                    </div>
                )}
                <div className="d-flex justify-content-end gap-2">
                    <button type="button" className="btn btn-cancel" disabled={isSubmitEnable} onClick={handleCancel}>
                        <i className="fas fa-times me-1"></i> Cancel
                    </button>
                    <button type="submit" className="btn btn-submit" disabled={isSubmitEnable}>
                        {isSubmitEnable ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                                Saving...
                            </>
                        ) : (
                            <>
                                <i className="fas fa-save me-1"></i> Save
                            </>
                        )}
                    </button>
                </div>
            </form>
        );
    };

    return (
        <>
            {formHTML()}
        </>
    );
});

Form.displayName = 'Form';

export default Form; 