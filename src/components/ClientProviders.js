'use client';

import { SessionProvider } from "next-auth/react";
import { AuthGuard } from '@/components/';
import ReduxProvider from '@/providers/ReduxProvider';
import { AuthMiddleware } from '@/middleware/authMiddleware';
import { Toaster } from "react-hot-toast";

export default function ClientProviders({ children }) {
    return (
        <>
            <ReduxProvider>
                <SessionProvider>
                    <AuthMiddleware>
                        <AuthGuard>{children}</AuthGuard>
                    </AuthMiddleware>
                </SessionProvider>
            </ReduxProvider>

            <Toaster
                position="top-center"
                reverseOrder={false}
                pauseOnFocusLoss={false}
                draggable={false}
                pauseOnHover={false}
                theme="light"
            />
        </>
    );
}