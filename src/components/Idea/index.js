import React, { useState, useEffect, useCallback, useRef } from 'react';
import { IdeaService } from '@/services';
import { useTitle } from '@/hooks/useTitle';
import { showToast, getSearchRecords, getS3FilePath, formatDateWithTime, shortenString } from '@/utils/helper';
import DataTable from 'react-data-table-component';
import { Loader, CommonModal, Common } from '@/components';
import { useRouter } from 'next/navigation';

const IdeaList = () => {
    useTitle("Ideas");
    const router = useRouter();
    const [isContentLoad, setIsContentLoad] = useState(false);
    const [search, setSearch] = useState('');
    const [data, setData] = useState([]);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [currentPage, setCurrentPage] = useState(1);
    const [isLoading, setIsLoading] = useState(false);
    const [isSubmitLoading, setIsSubmitLoading] = useState(false);
    const [statusChangeInfo, setStatusChangeInfo] = useState({
        id: null,
        status: false
    });

    const [deleteInfo, setDeleteInfo] = useState({
        id: null,
        title: ''
    });

    const modelRefs = {
        statusModalRef: useRef(null),
        deleteModalRef: useRef(null)
    };

    const getList = useCallback(async (page, searchQuery) => {
        try {
            setIsContentLoad(true);
            const response = await IdeaService.getList(page, perPage, searchQuery);
            if (response?.data) {
                setData(response.data.data);
                setTotalRows(response.data.total);
            }
        } catch (error) {
            showToast(error?.message || 'Error getting details', 'error');
        } finally {
            setIsContentLoad(false);
        }
    }, [perPage]);

    useEffect(() => {
        getList(currentPage, search);
    }, [getList, currentPage]);

    const handlePageChange = useCallback((page) => {
        setCurrentPage(page);
    }, []);

    const handlePerRowsChange = useCallback(async (newPerPage, page) => {
        setPerPage(newPerPage);
        setCurrentPage(page);
    }, []);

    const handleSearch = useCallback(async (e) => {
        getSearchRecords(e, setSearch, setCurrentPage, getList);
    }, [getList]);

    const handleActionClickEvent = useCallback(async (e, id) => {
        try {
            e.stopPropagation();
            setIsLoading(true);
            const button = e.target.closest('button');
            if (!button) return;
            const title = button.dataset.bsTitle || '';
            if (title && title.toLowerCase().includes('view')) {
                router.push(`/idea/details/${id}`);
            }
        } catch (error) {
            setIsLoading(false);
            console.error('Navigation error:', error);
        }
    }, [router]);

    const handleStatusChange = useCallback(async (e, id) => {
        e.preventDefault();
        e.stopPropagation();
        const newStatus = e.target.checked ? 1 : 0;
        setStatusChangeInfo({ id, status: newStatus });
        requestAnimationFrame(() => {
            if (modelRefs.statusModalRef.current) {
                modelRefs.statusModalRef.current?.open();
            }
        });
    }, [modelRefs]);

    const confirmStatusChange = useCallback(async () => {
        try {
            setIsSubmitLoading(true);
            const response = await IdeaService.updateIdeaStatus(statusChangeInfo.id, statusChangeInfo.status);
            if (response?.status === 200) {
                showToast(response?.message || 'Status updated successfully', "success");
                await getList(currentPage, search);
                requestAnimationFrame(() => {
                    if (modelRefs.statusModalRef.current) {
                        modelRefs.statusModalRef.current?.close();
                    }
                });
            }
        } catch (error) {
            showToast(error?.message || 'Error updating status', "error");
        } finally {
            setIsSubmitLoading(false);
            requestAnimationFrame(() => {
                if (modelRefs.statusModalRef.current) {
                    modelRefs.statusModalRef.current?.close();
                }
            });
        }
    }, [statusChangeInfo, getList, currentPage, search]);


    const handleDeleteClickEvent = useCallback(async (e, id) => {
        e.preventDefault();
        e.stopPropagation();
        setDeleteInfo({ id });
        requestAnimationFrame(() => {
            if (modelRefs.deleteModalRef.current) {
                modelRefs.deleteModalRef.current?.open();
            }
        });
    }, [modelRefs]);

    const confirmDelete = useCallback(async () => {
        try {
            setIsSubmitLoading(true);
            const response = await IdeaService.deleteIdea(deleteInfo.id);
            if (response?.status === 200) {
                showToast(response?.message || 'Idea deleted successfully', "success");
                await getList(currentPage, search);
                requestAnimationFrame(() => {
                    if (modelRefs.deleteModalRef.current) {
                        modelRefs.deleteModalRef.current?.close();
                    }
                });
            }
        } catch (error) {
            showToast(error?.message || 'Error deleting idea', "error");
        } finally {
            setIsSubmitLoading(false);
            requestAnimationFrame(() => {
                if (modelRefs.deleteModalRef.current) {
                    modelRefs.deleteModalRef.current?.close();
                }
            });
        }
    }, [deleteInfo, getList, currentPage, search]);

    const columns = [
        {
            name: 'No.',
            selector: (row, index) => ((currentPage - 1) * perPage) + index + 1,
            width: '80px',
            grow: 1,
        },
        {
            name: 'Name',
            selector: (row) => row?.user?.fullName || '',
            sortable: true,
            wrap: true,
            grow: 3,
        },
        {
            name: 'Description',
            selector: (row) => row?.description || '',
            sortable: true,
            wrap: true,
            grow: 6
        },
        {
            name: 'Gif',
            selector: (row) => null,
            cell: row => <Common.ImageRender src={row?.gif || '/images/no-image.png'} alt={row?.fullName || 'No image available'} height="80px" width="80px" />,
            center: 'true',
            grow: 3,
        },
        {
            name: 'Image',
            selector: (row) => null,
            cell: row => <Common.ImageRender src={row?.image ? getS3FilePath(row?.image) : '/images/no-image.png'} alt={row?.fullName || 'No image available'} height="80px" width="80px" />,
            center: 'true',
            grow: 3,
        },
        {
            name: 'Likes',
            selector: (row) => row?.totalLikes || 0,
            center: 'true',
            sortable: true,
            grow: 3,
        },
        {
            name: 'Comments',
            selector: (row) => row?.totalComments || 0,
            center: 'true',
            sortable: true,
            grow: 4,
        },
        {
            name: 'Report',
            selector: (row) => row?.reportCount || 0,
            center: 'true',
            sortable: true,
            grow: 3,
        },
        {
            name: 'Repost',
            selector: (row) => row?.repostCount || 0,
            center: 'true',
            sortable: true,
            grow: 3,
        },
        {
            name: 'Status',
            selector: (row) => (
                <div className="form-check form-switch">
                    <input
                        className="form-check-input"
                        type="checkbox"
                        checked={row?.isActive === 1}
                        onChange={(e) => handleStatusChange(e, row.id)}
                    />
                </div>
            ),
            center: 'true',
            grow: 3,
        },
        {
            name: 'Date',
            selector: (row) => formatDateWithTime(row?.updatedAt) || 'N/A',
            sortable: true,
            wrap: true,
            grow: 3,
        },
        {
            name: 'Action',
            cell: (row) => (
                <div className="d-flex gap-1">
                    <button
                        type="button"
                        className="btn btn-sm btn-info"
                        onClick={(e) => handleActionClickEvent(e, row.id)}
                        disabled={isLoading || isSubmitLoading}
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        data-bs-title="View Idea"
                    >
                        <i className="bi bi-eye"></i>
                    </button>
                    <button
                        type="button"
                        className="btn btn-sm btn-danger"
                        disabled={isLoading || isSubmitLoading}
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        data-bs-title="Delete Idea"
                        onClick={(e) => handleDeleteClickEvent(e, row.id)}
                    >
                        <i className="bi bi-trash"></i>
                    </button>
                </div>
            ),
            ignoreRowClick: true,
            center: 'true',
        },
    ];

    const customStyles = {
        rows: { style: { minHeight: '50px' } },
        headCells: {
            style: {
                fontWeight: 'bold',
                fontSize: '14px',
            },
        },
    };

    return (
        <>
            {(isLoading || isSubmitLoading) && <Loader />}
            <div className="content-wrapper">
                {/* Header */}
                <div className="content-header">
                    <h1 id="page-title">Idea List</h1>
                </div>

                {/* Main content */}
                <div className="main-content">
                    <div className="card">
                        <div className="card-body">
                            <div className="d-flex justify-content-between align-items-center mb-4">
                                <div className="entries-select"></div>
                                <div className="search-box position-relative">
                                    <label>Search:</label>
                                    <input
                                        type="text"
                                        placeholder="Search idea..."
                                        className="form-control form-control-sm"
                                        value={search}
                                        onChange={handleSearch}
                                    />
                                    {search && (
                                        <button
                                            className="btn btn-sm btn-light position-absolute"
                                            style={{
                                                right: '5px',
                                                top: '50%',
                                                transform: 'translateY(-50%)',
                                                padding: '0.25rem 0.5rem',
                                                borderRadius: '50%',
                                                boxShadow: '0 0 3px rgba(0,0,0,0.2)',
                                                backgroundColor: '#7c66d8',
                                                color: 'white'
                                            }}
                                            onClick={async () => {
                                                setSearch('');
                                                setCurrentPage(1);
                                                await getList(1, '');
                                            }}
                                        >
                                            <i className="bi bi-x-lg fw-bold"></i>
                                        </button>
                                    )}
                                </div>
                            </div>

                            <div className="table">
                                <DataTable
                                    columns={columns}
                                    data={data}
                                    pagination
                                    paginationServer
                                    paginationTotalRows={totalRows}
                                    onChangeRowsPerPage={handlePerRowsChange}
                                    onChangePage={handlePageChange}
                                    highlightOnHover
                                    striped
                                    responsive
                                    customStyles={customStyles}
                                    progressPending={isContentLoad}
                                    progressComponent={<Common.DataTableLoader />}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Status Change Modal */}
            <CommonModal
                ref={modelRefs.statusModalRef}
                staticModal={true}
                icon={statusChangeInfo.status === 1 ? 'bi bi-check-circle-fill' : 'bi bi-x-circle-fill'}
                title={`${statusChangeInfo.status === 1 ? 'Activate' : 'Deactivate'} Idea`}
            >

                <Common.ActiveInActive
                    statusChangeInfo={statusChangeInfo}
                    onConfirm={confirmStatusChange}
                    onCancel={() => {
                        requestAnimationFrame(() => {
                            if (modelRefs.statusModalRef.current) {
                                modelRefs.statusModalRef.current?.close();
                            }
                        });
                    }}
                />
            </CommonModal>

            {/* Delete Modal */}
            <CommonModal
                ref={modelRefs.deleteModalRef}
                staticModal={true}
                title="Delete Idea"
            >
                <Common.DeleteConfirmation deleteInfo={deleteInfo} onConfirm={confirmDelete} onCancel={() => {
                    requestAnimationFrame(() => {
                        if (modelRefs.deleteModalRef.current) {
                            modelRefs.deleteModalRef.current?.close();
                        }
                    });
                }} />
            </CommonModal>
        </>
    );
};

export default IdeaList;
