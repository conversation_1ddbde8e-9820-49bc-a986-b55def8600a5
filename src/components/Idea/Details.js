import React, { useState, useEffect, useCallback, useRef } from 'react';
import { IdeaService, CommentService } from '@/services';
import { useTitle } from '@/hooks/useTitle';
import { showToast, getS3FilePath, ucwords, formatDateWithTime, formatCount } from '@/utils/helper';
import Link from 'next/link';
import { Loader, NotFound, Common, CommonModal } from '@/components';
import messages from '@/utils/messages';
import { useRouter } from 'next/navigation';

const IdeaDetails = ({ ideaId }) => {
    useTitle("Idea Details");
    const router = useRouter();
    const [isContentLoad, setIsContentLoad] = useState(false);
    const [ideaData, setIdeaData] = useState(null);
    const [ideaDetailsResStatus, setIdeaDetailsResStatus] = useState(null);
    const [comments, setComments] = useState([]);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const [isSubmitLoading, setIsSubmitLoading] = useState(false);
    const observer = useRef();
    const perPage = 10;
    const [deleteInfo, setDeleteInfo] = useState({});
    const deleteModalRef = useRef(null);

    const getIdeaDetails = useCallback(async (ideaId) => {
        try {
            setIsContentLoad(true);
            const response = await IdeaService.getIdeaDetails(ideaId);
            if (Object.keys(response?.data || {}).length > 0) {
                setIdeaData(response.data);
                setIdeaDetailsResStatus(response?.status);
            }
        } catch (error) {
            setIdeaDetailsResStatus(404);
            showToast(error?.message || 'Error getting details', 'error');
        } finally {
            setIsContentLoad(false);
        }
    }, [ideaId]);

    const formatUser = (user) => ({
        ...user,
        profileImage: user?.profileImage || '',
        fullName: user?.fullName || 'Anonymous User',
        userName: user?.userName || 'anonymous'
    });

    const getUserDetails = (comment) => {
        const userDetails = comment.user || {};
        const userName = userDetails.userName || 'anonymous';
        const fullName = userDetails.fullName || 'Anonymous User';
        const profileImage = userDetails.profileImage || '';

        return {
            userName,
            fullName,
            profileImage
        }
    }

    const getComments = useCallback(async (pageNum) => {
        try {
            setIsLoadingMore(true);
            const response = await CommentService.getList(ideaId, pageNum, perPage);
            if (response?.data?.comments) {
                const formattedComments = response.data.comments.map(comment => ({
                    ...comment,
                    user: formatUser(comment.user),
                    replies: (comment.replies || []).map(reply => ({
                        ...reply,
                        user: formatUser(reply.user)
                    }))
                }));
                setComments(pageNum === 1 ? formattedComments : prev => [...prev, ...formattedComments]);
                setHasMore(response.data.pagination.total > pageNum * perPage);
            }
        } catch (error) {
            showToast(error?.message || 'Error loading comments', 'error');
        } finally {
            setIsLoadingMore(false);
        }
    }, [ideaId, perPage]);

    const lastCommentElementRef = useCallback(node => {
        if (isLoadingMore || !node) return;

        if (observer.current) observer.current.disconnect();

        observer.current = new IntersectionObserver(entries => {
            if (entries[0].isIntersecting && hasMore && !isLoadingMore) {
                requestAnimationFrame(() => {
                    setPage(prevPage => prevPage + 1);
                });
            }
        }, {
            root: null,
            rootMargin: '100px',
            threshold: 0.1
        });
        observer.current.observe(node);
        return () => {
            if (observer.current) {
                observer.current.disconnect();
            }
        };
    }, [isLoadingMore, hasMore]);

    useEffect(() => {
        const getData = async () => {
            if (ideaId) {
                try {
                    await Promise.all([
                        getIdeaDetails(ideaId),
                        getComments(1)
                    ]);
                } catch (error) {
                    showToast(error?.message || 'Error loading idea details or comments', 'error');
                }
            }
        };
        getData();
    }, [getIdeaDetails, ideaId, getComments]);

    useEffect(() => {
        if (page > 1) {
            getComments(page);
        }
    }, [page, getComments]);

    const goToUserProfile = useCallback((userId) => {
        if (userId) {
            router.push(`/user/details/${userId}`);
        } else {
            showToast('User not found', 'error');
        }
    }, [router]);

    const handleDeleteClickEvent = useCallback(async (e, id, comment) => {
        e.preventDefault();
        e.stopPropagation();
        setDeleteInfo({ id, title: comment });
        requestAnimationFrame(() => {
            if (deleteModalRef.current) {
                deleteModalRef.current?.open();
            }
        });
    }, [deleteModalRef]);

    const confirmDelete = useCallback(async () => {
        try {
            setIsSubmitLoading(true);
            const response = await CommentService.deleteComment(deleteInfo.id);

            if (response?.status === 200) {
                showToast(response?.message || 'Comment deleted successfully', "success");

                const isMainComment = comments.some(comment => comment.id === deleteInfo.id);

                if (isMainComment) {
                    setComments(prev => prev.filter(comment => comment.id !== deleteInfo.id));
                    if (page > 1 && comments.length <= 1) {
                        setPage(1);
                        getComments(1);
                    } else if (hasMore) {
                        const nextPage = Math.ceil(comments.length / perPage) + 1;
                        const { data } = await CommentService.getList(ideaId, nextPage, 1);
                        if (data?.comments?.[0]) {
                            const newComment = data.comments[0];
                            const formattedComment = {
                                ...newComment,
                                user: formatUser(newComment.user),
                                replies: (newComment.replies || []).map(reply => ({
                                    ...reply,
                                    user: formatUser(reply.user)
                                }))
                            };
                            setComments(prev => [...prev, formattedComment]);
                        }
                    }
                } else {
                    setComments(prev => prev.map(comment =>
                        comment.replies?.some(reply => reply.id === deleteInfo.id)
                            ? {
                                ...comment,
                                replies: comment.replies.filter(reply => reply.id !== deleteInfo.id),
                                totalSubComments: (comment.totalSubComments || 0) - 1
                            }
                            : comment
                    ));
                }
                requestAnimationFrame(() => {
                    if (deleteModalRef.current) {
                        deleteModalRef.current?.close();
                    }
                });
            }
        } catch (error) {
            showToast(error?.message || 'Error deleting comment', "error");
        } finally {
            setIsSubmitLoading(false);
            requestAnimationFrame(() => {
                if (deleteModalRef.current) {
                    deleteModalRef.current?.close();
                }
            });
        }
    }, [deleteInfo, deleteModalRef, comments, page, hasMore, ideaId, perPage, ideaData, getComments]);

    const ideaDetailsHTML = () => {
        return (
            <div className="card-body">
                <div className="post-header d-flex align-items-center mb-3">
                    <img src={ideaData?.user?.profileImage ? getS3FilePath(ideaData?.user?.profileImage) : '/images/user.png'}
                        alt="User Profile"
                        className="user-avatar me-3" />
                    <div>
                        <h5 className="mb-0"
                            onClick={() => goToUserProfile(ideaData?.user?.userId || '')}
                            style={{ cursor: 'pointer' }}>{ideaData?.user?.fullName ? ucwords(ideaData?.user?.fullName) : ''}</h5>
                        <p className="text-muted mb-0"
                            onClick={() => goToUserProfile(ideaData?.user?.userId || '')}
                            style={{ cursor: 'pointer' }}>{ideaData?.user?.userName ? '@' + ideaData?.user?.userName : ''}</p>
                    </div>
                    <div className="ms-auto">
                        <span className="created-at">
                            {ideaData?.createdAt ? formatDateWithTime(ideaData?.createdAt) : ''}
                        </span>
                    </div>
                </div>
                <div className="post-content mb-3">
                    <p>{ideaData?.description ? ideaData?.description : ''}</p>
                    {ideaData?.gif && <img src={ideaData.gif} alt="Trading Chart" className="img-fluid rounded" />}
                    {ideaData?.image && <img src={getS3FilePath(ideaData.image) || '/images/no-image.jpg'} alt="Trading Chart" className="img-fluid rounded" />}
                </div>
                <div className="post-stats d-flex align-items-center">
                    <div className="stat-item me-4">
                        <i className="fas fa-heart text-danger"></i>
                        <span className="ms-1 fw-bold">{ideaData?.totalLikes && ideaData?.totalLikes > 0 ? formatCount(ideaData?.totalLikes) : 0}</span>
                    </div>
                    <div className="stat-item me-4">
                        <i className="fas fa-comment text-primary"></i>
                        <span className="ms-1 fw-bold">{ideaData?.totalComments && ideaData?.totalComments > 0 ? formatCount(ideaData?.totalComments) : 0}</span>
                    </div>
                    <div className="stat-item">
                        <i className="fas fa-eye text-info"></i>
                        <span className="ms-1 fw-bold">{ideaData?.totalViews && ideaData?.totalViews > 0 ? formatCount(ideaData?.totalViews) : 0}</span>
                    </div>
                </div>
            </div>
        );
    };

    const replyCommentHTML = (comment) => {
        const { userName, fullName, profileImage } = getUserDetails(comment);
        return (
            <div className="sub-comment-item mb-3" key={comment.id}>
                <div className="d-flex">
                    <img
                        src={profileImage ? getS3FilePath(profileImage) : '/images/user.png'}
                        alt="User Profile"
                        className="user-avatar me-2"
                    />
                    <div>
                        <div className="d-flex align-items-center">
                            <h6 className="mb-0"
                                onClick={() => goToUserProfile(comment?.user?.userId || '')}
                                style={{ cursor: 'pointer' }}>{fullName ? ucwords(fullName) : 'Anonymous User'}</h6>
                            <span className="text-muted"
                                onClick={() => goToUserProfile(comment?.user?.userId || '')}
                                style={{ cursor: 'pointer' }}>@{userName || 'anonymous'}</span>
                            <span className="created-at">
                                {formatDateWithTime(comment.createdAt)}
                            </span>
                        </div>
                        <p className="mb-1">{comment.comment}</p>
                        <div className="comment-actions">
                            <button className="btn btn-link btn-sm p-0">
                                <i className="fas fa-heart"></i>
                                <span className="ms-0">{formatCount(comment.totalCommentLikes || 0)}</span>
                            </button>
                            <button className="btn btn-link btn-sm p-0 delete-btn" onClick={(e) => handleDeleteClickEvent(e, comment.id, comment.comment)} style={{ cursor: 'pointer' }}>
                                <i className="fas fa-trash-alt" style={{ color: "#6c5ce7" }}></i>
                                <span className="ms-1" style={{ color: "#6c5ce7" }}>Delete</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    const mainCommentHTML = (comment) => {
        const { userName, fullName, profileImage } = getUserDetails(comment);
        return (
            <div className="comment-item mb-4" key={comment.id}>
                <div className="d-flex">
                    <img
                        src={profileImage ? getS3FilePath(profileImage) : '/images/user.png'}
                        alt="User Profile"
                        className="user-avatar me-3"
                    />
                    <div className="flex-grow-1">
                        <div className="d-flex align-items-center mb-2">
                            <h6 className="mb-0"
                                onClick={() => goToUserProfile(comment?.user?.userId || '')}
                                style={{ cursor: 'pointer' }}>{fullName ? ucwords(fullName) : 'Anonymous User'}</h6>
                            <span className="text-muted"
                                onClick={() => goToUserProfile(comment?.user?.userId || '')}
                                style={{ cursor: 'pointer' }}>@{userName || 'anonymous'}</span>
                            <span className="created-at">
                                {formatDateWithTime(comment.createdAt)}
                            </span>
                            <button className="btn btn-link btn-sm p-0 delete-btn ms-2" onClick={(e) => handleDeleteClickEvent(e, comment.id, comment.comment)} style={{ cursor: 'pointer' }}>
                                <i className="fas fa-trash-alt text-danger"></i>
                                <span className="ms-1 text-danger">Delete</span>
                            </button>
                        </div>
                        <p className="mb-2">{comment.comment}</p>
                        <div className="comment-actions d-flex align-items-center">
                            <button className="btn btn-link btn-sm p-0 me-3">
                                <i className="fas fa-heart"></i>
                                <span className="ms-1">{formatCount(comment.totalCommentLikes || 0)}</span>
                            </button>
                            {comment.totalSubComments > 0 && (
                                <button
                                    className="btn btn-link btn-sm p-0"
                                    type="button"
                                    data-bs-toggle="collapse"
                                    data-bs-target={`#subComments${comment.id}`}
                                >
                                    <i className="fas fa-comment"></i>
                                    <span className="ms-1">{formatCount(comment.totalSubComments)} Replies</span>
                                </button>
                            )}
                        </div>
                        {comment.replies && comment.replies.length > 0 && (
                            <div className="collapse mt-3" id={`subComments${comment.id}`}>
                                <div className="sub-comments">
                                    {comment.replies.map(reply => renderComment(reply, true))}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        );
    }

    const renderComment = (comment, isReply = false) => {
        if (isReply) {
            return replyCommentHTML(comment);
        }
        return mainCommentHTML(comment);
    };

    return (
        <>
            {(isContentLoad || isSubmitLoading) && <Loader />}
            <div className="content-wrapper">
                {/* Header */}
                <div className="content-header d-flex justify-content-between align-items-center">
                    <h1 id="page-title"><i className="fas fa-lightbulb me-2"></i>Idea Details</h1>
                    <div className="d-flex gap-3">
                        <Link href="/idea/list" className="btn btn-back">
                            <i className="fas fa-arrow-left me-2"></i>Back to Ideas List
                        </Link>
                    </div>
                </div>

                {/* Main content */}
                <div className="main-content">
                    {ideaData && ideaDetailsResStatus && ideaDetailsResStatus === 200 ? (
                        <div className="row">
                            {/* Post Section */}
                            <div className="col-md-6">
                                <div className="card mb-4">
                                    {ideaDetailsHTML()}
                                </div>
                            </div>

                            {/* Comments Section */}
                            <div className="col-md-6">
                                <div className="card">
                                    <div className="card-header">
                                        <h5 className="mb-0">Comments</h5>
                                    </div>
                                    <div className="card-body p-0">
                                        <div className="comments-container" style={{ maxHeight: '600px', overflowY: 'auto' }}>
                                            {comments.map((comment, index) => (
                                                <div key={index} ref={index === comments.length - 1 ? lastCommentElementRef : null}>
                                                    {renderComment(comment, false)}
                                                </div>
                                            ))}
                                            {isLoadingMore && (
                                                <div className="text-center py-4">
                                                    <div className="spinner-border" role="status" style={{
                                                        color: '#6c5ce7',
                                                        width: '2.5rem',
                                                        height: '2.5rem',
                                                        borderWidth: '0.25rem'
                                                    }}>
                                                        <span className="visually-hidden">Loading...</span>
                                                    </div>
                                                    <p className="mt-2 mb-0" style={{ color: '#6c5ce7', fontWeight: '500' }}>
                                                        {comments.length > 0 ? 'Loading more comments...' : 'Loading comments...'}
                                                    </p>
                                                </div>
                                            )}

                                            {!isLoadingMore && comments.length === 0 && (
                                                <div className="text-center py-4">
                                                    <div className="mb-3">
                                                        <i className="fas fa-comments fa-3x" style={{ color: '#6c5ce7', opacity: 0.5 }}></i>
                                                    </div>
                                                    <p className="text-muted">No comments yet</p>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        !isContentLoad && ideaDetailsResStatus && ideaDetailsResStatus !== 200 && (
                            <div className="col-12">
                                <div className="card shadow-sm border-0">
                                    <NotFound message={messages.IDEA_DETAILS_NOT_FOUND || 'Idea details not found'} />
                                </div>
                            </div>
                        )
                    )}
                </div>
            </div>

            {/* Delete Modal */}
            {ideaData && ideaDetailsResStatus && ideaDetailsResStatus === 200 && (
                <CommonModal
                    ref={deleteModalRef}
                    staticModal={true}
                    title="Delete Comment"
                >
                    <Common.DeleteConfirmation deleteInfo={deleteInfo} onConfirm={confirmDelete} onCancel={() => {
                        requestAnimationFrame(() => {
                            if (deleteModalRef.current) {
                                deleteModalRef.current?.close();
                            }
                        });
                    }} />
                </CommonModal>
            )}
        </>
    )
};

export default IdeaDetails;