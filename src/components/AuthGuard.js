/* eslint-disable react-hooks/exhaustive-deps */
'use client';
import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { usePathname, useRouter } from 'next/navigation';
import { useSelector } from 'react-redux';
import { Navbar, PageLoader, GlobalLazyMedia, Sidebar } from '@/components';
import 'aos/dist/aos.css'
import AOS from 'aos'
import { clearUser } from '@/store/slices/userSlice';
import { useDispatch } from 'react-redux';

const AuthGuard = ({ children }) => {
  const { status } = useSession();
  const pathname = usePathname();
  const AuthPaths = ['/auth/login', '/auth/error'];
  const isAuthPath = AuthPaths.some(path => pathname.includes(path));
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const dispatch = useDispatch();
  const userData = useSelector((state) => state.user);

  const authUser = (
    status === "authenticated" &&
    userData?.accessToken &&
    userData?.roleId === 1 &&
    userData?.otpPending === false
  ) || false;


  useEffect(() => {
    const handleLoadingState = () => {
      if (status === "loading" || document.readyState !== 'complete') {
        setIsLoading(true);
      } else {
        setIsLoading(false);
      }
    };
    handleLoadingState();

    if (status === "authenticated" && !userData?.accessToken) {
      const checkUserData = setTimeout(() => {
        setIsLoading(false);
      }, 500);
      return () => clearTimeout(checkUserData);
    }

    if ((status === "unauthenticated" && !isAuthPath) || (status === "authenticated" && !authUser && !isAuthPath)) {
      router.push('/auth/login');
    } else if (authUser && isAuthPath) {
      router.push('/dashboard');
    }

    if (document.readyState !== 'complete') {
      const onLoad = () => setIsLoading(false);
      window.addEventListener('load', onLoad);
      return () => window.removeEventListener('load', onLoad);
    }

  }, [status, pathname, router, authUser, userData]);

  useEffect(() => {
    if (isAuthPath && !authUser) {
      const body = document.body;
      body.classList.remove("login-page");
      body.classList.remove("dashboard-page");
      body.classList.add("login-page");
    } else if (!isAuthPath && authUser) {
      const body = document.body;
      body.classList.remove("login-page");
      body.classList.remove("dashboard-page");
      body.classList.add("dashboard-page");
    }

    if (isAuthPath || (authUser && pathname.includes('/dashboard'))) {
      AOS.init();
    }
  }, [isAuthPath, authUser, pathname]);

  useEffect(() => {
    if (status === "unauthenticated") {
      dispatch(clearUser());
    }
  }, [status, dispatch]);

  useEffect(() => {
    const handleInitialLoad = () => {
      if (document.readyState === 'complete') {
        setTimeout(() => setIsLoading(false), 300);
      }
    };
    
    handleInitialLoad();
    window.addEventListener('load', handleInitialLoad);
    
    return () => window.removeEventListener('load', handleInitialLoad);
  }, []);

  if (isLoading) {
    return <PageLoader />;
  }
  
  return (
    <>
      {(!isAuthPath || (!isAuthPath && authUser)) ? (
        <div className="wrapper">
          <GlobalLazyMedia />
          <Sidebar />
          <div id="content">
            <Navbar />
            <main className="main-content">
              {children}
            </main>
          </div>
        </div>
      ) : (
        isAuthPath && !authUser && children
      )}
    </>
  );
}

export default AuthGuard;