import React, { useState, useEffect, useCallback, useRef } from 'react';
import { StockService } from '@/services';
import { useTitle } from '@/hooks/useTitle';
import { showToast, getSearchRecords, shortenString, formatCount, getStockTypeOptions } from '@/utils/helper';
import DataTable from 'react-data-table-component';
import { Loader, CommonModal, Common } from '@/components';
import { useFormFields } from '@/hooks/useFormRefs';
import * as yup from 'yup';

const StockList = ({ type }) => {
    useTitle(type == 'crypto' ? "Crypto" : "Stocks");
    const stockTypeOptions = getStockTypeOptions(type) || [];
    const [isContentLoad, setIsContentLoad] = useState(false);
    const [search, setSearch] = useState('');
    const [data, setData] = useState([]);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [currentPage, setCurrentPage] = useState(1);
    const [isSubmitLoading, setIsSubmitLoading] = useState(false);
    const [totalGameStocks, setTotalGameStocks] = useState(0);
    const [statusChangeInfo, setStatusChangeInfo] = useState({
        id: null,
        status: false,
        title: ''
    });
    const statusModalRef = useRef(null);

    const getList = useCallback(async (page, searchQuery) => {
        try {
            setIsContentLoad(true);
            const response = await StockService.getList(page, perPage, searchQuery, type);
            if (response?.data) {
                setData(response.data.data);
                setTotalRows(response.data.total);
                setTotalGameStocks(response.data.totalGameStocksCount);
            }
        } catch (error) {
            showToast(error?.message || 'Error getting details', 'error');
        } finally {
            setIsContentLoad(false);
        }
    }, [perPage]);

    useEffect(() => {
        getList(currentPage, search);
    }, [getList, currentPage]);

    const handlePageChange = useCallback((page) => {
        setCurrentPage(page);
    }, []);

    const handlePerRowsChange = useCallback(async (newPerPage, page) => {
        setPerPage(newPerPage);
        setCurrentPage(page);
    }, []);

    const handleSearch = useCallback(async (e) => {
        getSearchRecords(e, setSearch, setCurrentPage, getList);
    }, [getList]);

    const handleStatusChange = useCallback(async (e, id, symbol) => {
        e.preventDefault();
        e.stopPropagation();
        const newStatus = e.target.checked ? 1 : 0;
        setStatusChangeInfo({ id, status: newStatus, title: symbol });
        requestAnimationFrame(() => {
            if (statusModalRef.current) {
                statusModalRef.current?.open();
            }
        });
    }, [statusModalRef]);

    const confirmStatusChange = useCallback(async () => {
        try {
            setIsSubmitLoading(true);
            const response = await StockService.updateStockStatus(statusChangeInfo.id, statusChangeInfo.status);
            if (response?.status === 200) {
                showToast(response?.message || 'Status updated successfully', "success");
                requestAnimationFrame(() => {
                    if (statusModalRef.current) {
                        statusModalRef.current?.close();
                    }
                });
                setTimeout(() => {
                    window.location.reload();
                }, 200);
            }
        } catch (error) {
            showToast(error?.message || 'Error updating status', "error");
        } finally {
            setIsSubmitLoading(false);
            requestAnimationFrame(() => {
                if (statusModalRef.current) {
                    statusModalRef.current?.close();
                }
            });
        }
    }, [statusChangeInfo, getList, currentPage, search]);

    const columns = [
        {
            name: 'No.',
            selector: (row, index) => ((currentPage - 1) * perPage) + index + 1,
            width: '80px',
        },
        {
            name: 'Currency',
            selector: (row) => row?.currency || '',
            sortable: true,
            wrap: true,
            omit: type !== 'crypto',
        },
        {
            name: 'Description',
            selector: (row) => row?.description || '',
            sortable: true,
            wrap: true,
            grow: type === 'crypto' ? 2 : 1
        },
        {
            name: 'Symbol',
            selector: (row) => row?.symbol || '',
            sortable: true,
            grow: 1,
        },
        {
            name: 'Icon',
            selector: (row) => null,
            cell: row => {
                return <Common.ImageRender src={row?.logo || '/images/no-image.png'} alt={row?.symbol || 'No image available'} />
            },
            omit: type !== 'crypto',
            center: "true",
        },
        {
            name: 'Type',
            selector: (row) => row?.type || '',
            sortable: true,
            omit: type !== 'crypto',
        },
        {
            name: 'Is GameStock?',
            selector: (row) => (
                <div className="form-check form-switch">
                    <input
                        className="form-check-input"
                        type="checkbox"
                        checked={row?.isGameStock === 1}
                        onChange={(e) => handleStatusChange(e, row.id, row.symbol)}
                    />
                </div>
            ),
            center: 'true',
        }
    ];

    const customStyles = {
        rows: { style: { minHeight: '50px' } },
        headCells: {
            style: {
                fontWeight: 'bold',
                fontSize: '14px',
            },
        },
    };

    const syncDataValidationSchema = yup.object().shape({
        stockType: yup.string()
            .required('Please select stock type.')
            .test('is-valid', 'Please select stock type.', value =>
                value && value.trim() !== ''
            ),
    });

    const {
        register,
        handleSubmit,
        errors,
        reset,
        watch,
        createFormData
    } = useFormFields(syncDataValidationSchema);

    const formValues = watch();

    const handleSyncStocks = useCallback(async (data) => {
        try {
            setIsSubmitLoading(true);
            const formData = createFormData(data);
            const response = await StockService.syncStocks(formData);
            if (response?.status === 200) {
                showToast(response?.message || 'Data synced successfully', "success");
                await getList(1, '');
                reset();
            }
        } catch (error) {
            showToast(error?.message || 'Error syncing data', "error");
        } finally {
            setIsSubmitLoading(false);
        }
    }, [createFormData]);

    const syncStocksFormHTML = () => {
        return (
            <form onSubmit={handleSubmit(handleSyncStocks)} className="stock-sync-form" encType="multipart/form-data" noValidate>
                <div className="stock-form-wrapper">
                    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', flex: 1, minWidth: 0 }}>
                        <div className="select-type" style={{ width: '100%' }}>
                            <select
                                className="form-select"
                                name="stockType"
                                id="stockType"
                                {...register('stockType')}
                                value={formValues.stockType || ''}
                            >
                                <option value="">Select Stock Type</option>
                                {Array.from(stockTypeOptions).map((value, index) => (
                                    <option
                                        key={index}
                                        value={value}
                                    >
                                        {value}
                                    </option>
                                ))}
                            </select>
                        </div>
                        {errors.stockType?.message && (
                            <div className="error-message m-0 text-start">{errors.stockType.message}</div>
                        )}
                    </div>
                    <button type="submit" className={`btn btn-sync${isSubmitLoading ? ' loading' : ''}`} disabled={isSubmitLoading}>
                        <i className={`fas fa-sync-alt ${isSubmitLoading ? 'fa-spin' : ''}`} style={isSubmitLoading ? { animation: 'fa-spin 1s infinite linear' } : {}}></i>
                        <span>{isSubmitLoading ? 'Syncing...' : 'Sync Data'}</span>
                    </button>
                </div>
            </form>
        )
    }

    return (
        <>
            {isSubmitLoading && <Loader />}
            <div className="content-wrapper">
                {/* Header */}
                <div className="content-header d-flex justify-content-between align-items-center">
                    <h1 id="page-title">{type == 'crypto' ? 'Crypto' : 'Stocks'} List</h1>
                    <div className="crypto-header-controls">
                        <div className="game-crypto-count">
                            <i className={`fas ${type == 'crypto' ? 'fa-coins' : 'fa-chart-line'} me-2`}></i>
                            <span style={{ display: 'inline-flex', alignItems: 'center' }}>Game {type == 'crypto' ? 'Crypto' : 'Stocks'} Count :
                                {isContentLoad ? (
                                    <div className="spinner-border spinner-border-sm ms-2" role="status">
                                        <span className="visually-hidden">Loading...</span>
                                    </div>
                                ) : (
                                    <strong className="ms-2" style={{ marginTop: '2px' }}>{formatCount(totalGameStocks)}</strong>
                                )}
                            </span>
                        </div>
                        {syncStocksFormHTML()}
                    </div>
                </div>

                {/* Main content */}
                <div className="main-content">
                    <div className="card">
                        <div className="card-body">
                            <div className="d-flex justify-content-between align-items-center mb-4">
                                <div className="entries-select"></div>
                                <div className="search-box position-relative">
                                    <label>Search:</label>
                                    <input
                                        type="text"
                                        placeholder={`Search ${type === 'crypto' ? 'crypto' : 'stock'}...`}
                                        className="form-control form-control-sm"
                                        value={search}
                                        onChange={handleSearch}
                                    />
                                    {search && (
                                        <button
                                            className="btn btn-sm btn-light position-absolute"
                                            style={{
                                                right: '5px',
                                                top: '50%',
                                                transform: 'translateY(-50%)',
                                                padding: '0.25rem 0.5rem',
                                                borderRadius: '50%',
                                                boxShadow: '0 0 3px rgba(0,0,0,0.2)',
                                                backgroundColor: '#7c66d8',
                                                color: 'white'
                                            }}
                                            onClick={async () => {
                                                setSearch('');
                                                setCurrentPage(1);
                                                await getList(1, '');
                                            }}
                                        >
                                            <i className="bi bi-x-lg fw-bold"></i>
                                        </button>
                                    )}
                                </div>
                            </div>

                            <div className="table">
                                <DataTable
                                    columns={columns}
                                    data={data}
                                    pagination
                                    paginationServer
                                    paginationTotalRows={totalRows}
                                    onChangeRowsPerPage={handlePerRowsChange}
                                    onChangePage={handlePageChange}
                                    highlightOnHover
                                    striped
                                    responsive
                                    customStyles={customStyles}
                                    progressPending={isContentLoad}
                                    progressComponent={<Common.DataTableLoader />}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Status Change Modal */}
            <CommonModal
                ref={statusModalRef}
                staticModal={true}
                icon={statusChangeInfo.status === 1 ? 'bi bi-plus-circle-fill' : 'bi bi-dash-circle-fill'}
                title={`${statusChangeInfo.status === 1 ? ' Add to' : 'Remove from'} Game Stock`}
            >

                <Common.ActiveInActive
                    statusChangeInfo={statusChangeInfo}
                    onConfirm={confirmStatusChange}
                    onCancel={() => {
                        requestAnimationFrame(() => {
                            if (statusModalRef.current) {
                                statusModalRef.current?.close();
                            }
                        });
                    }}
                    customMessageHTML={
                        <>
                            Are you sure you want to <span className="fw-semibold" style={{ color: statusChangeInfo.status ? '#28a745' : '#dc3545' }}>
                                {statusChangeInfo.status ? `add ${type == 'crypto' ? 'crypto' : 'stock'}` : `remove ${type == 'crypto' ? 'crypto' : 'stock'}`}
                            </span> <span className="fw-bold">{statusChangeInfo.title ? shortenString(statusChangeInfo.title, 20) : ''} </span>{statusChangeInfo.status ? 'to' : 'from'} game stock?
                        </>
                    }
                    customLoadingHTML={
                        <>
                            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            {statusChangeInfo.status ? 'Adding...' : 'Removing...'}
                        </>
                    }
                    customButtonHTML={
                        <>
                            <i className={`fas ${statusChangeInfo.status ? 'fa-plus' : 'fa-minus'} me-2`}></i>
                            {statusChangeInfo.status ? 'Add' : 'Remove'}
                        </>
                    }
                />
            </CommonModal>
        </>
    );
};

export default StockList;
