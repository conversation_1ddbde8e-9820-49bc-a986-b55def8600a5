import React from 'react';

const DataTableLoader = () => {
    return (
        <div className="d-flex justify-content-center align-items-center py-5 w-100">
            <div className="position-relative">
                <div
                    className="spinner-border"
                    role="status"
                    style={{
                        width: '3rem',
                        height: '3rem',
                        color: '#7c66d8'
                    }}
                >
                    <span className="visually-hidden">Loading...</span>
                </div>
                <div className="position-absolute text-center" style={{
                    top: '120%',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    color: '#7c66d8',
                    fontWeight: '500',
                    fontSize: '0.95rem',
                    marginTop: '0.5rem',
                    whiteSpace: 'nowrap'
                }}>
                    Loading data...
                </div>
            </div>
        </div>
    );
};

export default DataTableLoader;