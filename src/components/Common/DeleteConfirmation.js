import React, { useState } from 'react';
import { shortenString } from '@/utils/helper';

const DeleteConfirmation = (props) => {
  const { deleteInfo, onConfirm, onCancel } = props;
  const [isLoading, setIsLoading] = useState(false);

  const handleConfirm = () => {
    setIsLoading(true);
    onConfirm();
  };

  return (
    <div className="delete-confirmation p-4 text-center">
      <p className="mb-2 fs-5">Are you sure you want to <span className="text-danger">delete</span> <strong>{deleteInfo.title ? shortenString(deleteInfo.title, 20) : ''}</strong>{deleteInfo.title ? '?' : 'this record?'}</p>
      <p className="text-danger mb-4">
        <i className="fas fa-info-circle me-2"></i>
        This action cannot be undone.
      </p>
      <div className="d-flex justify-content-center gap-4 mt-4">
        <button
          type="button"
          className="btn btn-outline-secondary px-4 py-2 rounded-pill shadow-sm"
          onClick={onCancel}
          disabled={isLoading}
        >
          <i className="fas fa-times me-2"></i>
          Cancel
        </button>
        <button
          type="button"
          className="btn btn-danger px-4 py-2 rounded-pill shadow-sm"
          onClick={handleConfirm}
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Deleting...
            </>
          ) : (
            <>
              <i className="fas fa-trash-alt me-2"></i>
              Delete
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default DeleteConfirmation;