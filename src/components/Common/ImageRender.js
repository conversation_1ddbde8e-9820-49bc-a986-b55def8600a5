import React from 'react';

const ImageRender = (props) => {
    const { src = '/images/no-image.png', alt = 'No image available', height = '70px', width = '70px' } = props;

    const parseSize = (size) => {
        const numericValue = parseInt(size);
        return isNaN(numericValue) ? 20 : numericValue;
    };
    const heightValue = parseSize(height);
    const widthValue = parseSize(width);
    const spinnerSize = Math.min(heightValue, widthValue) / 3;
    
    return (
        <div style={{
            backgroundColor: 'transparent',
            height: height,
            width: width,
            position: 'relative',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
        }}>
            <div
                className="spinner-border"
                role="status"
                style={{
                    position: 'absolute',
                    margin: 'auto',
                    inset: 0,
                    zIndex: 1,
                    width: `${spinnerSize}px`,
                    height: `${spinnerSize}px`,
                    borderWidth: `${spinnerSize / 8}px`
                }}
            >
                <span className="visually-hidden">Loading...</span>
            </div>
            <img
                src={src || '/images/no-image.png'}
                className="profile-avatar"
                style={{
                    height: height,
                    width: width,
                    objectFit: 'cover',
                    borderRadius: '50%',
                    border: '2px solid #e0e6ed',
                    opacity: 0,
                    transition: 'opacity 0.3s',
                    position: 'relative',
                    zIndex: 2
                }}
                alt={alt || 'No image available'}
                onLoad={(e) => {
                    e.target.style.opacity = 1;
                    e.target.previousSibling.style.display = 'none';
                }}
                onError={(e) => {
                    e.target.style.opacity = 1;
                    e.target.previousSibling.style.display = 'none';
                }}
            />
        </div>
    );
};

export default ImageRender;