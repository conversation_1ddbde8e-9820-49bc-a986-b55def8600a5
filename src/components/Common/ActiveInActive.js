'use client';

import React, { useState } from 'react';
import { shortenString } from '@/utils/helper';

const ActiveInActive = (props) => {
    const { statusChangeInfo, onConfirm, onCancel, customMessageHTML = null, customLoadingHTML = null, customButtonHTML = null } = props;
    const [isLoading, setIsLoading] = useState(false);
    const iconColor = statusChangeInfo.status ? '#28a745' : '#dc3545';

    const DefaultLoadingHTML = () => {
        return (
            <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                {statusChangeInfo.status ? 'Activating...' : 'Deactivating...'}
            </>
        );
    };

    const DefaultButtonHTML = () => {
        return (
            <>
                <i className={`fas ${statusChangeInfo.status ? 'fa-check' : 'fa-times'} me-2`}></i>
                {statusChangeInfo.status ? 'Activate' : 'Deactivate'}
            </>
        );
    };

    const handleConfirm = () => {
        setIsLoading(true);
        onConfirm();
    };

    return (
        <div className="p-4 text-center">
            <p className="mb-4 fs-5">
                {!customMessageHTML ? (
                    <>
                        Are you sure you want to <span className="fw-semibold" style={{ color: iconColor }}>
                            {statusChangeInfo.status ? 'activate' : 'deactivate'}
                        </span> <span className="fw-bold">{statusChangeInfo?.title ? shortenString(statusChangeInfo.title, 20) : ''}</span>{!statusChangeInfo?.title ? 'this record' : ''}?
                    </>
                ) : customMessageHTML}
            </p>
            <div className="d-flex justify-content-center gap-4 mt-4">
                <button
                    type="button"
                    className="btn btn-outline-secondary px-4 py-2 rounded-pill shadow-sm"
                    onClick={onCancel}
                    disabled={isLoading}
                >
                    <i className="fas fa-times me-2"></i>
                    Cancel
                </button>
                <button
                    type="button"
                    className={`btn ${statusChangeInfo.status ? 'btn-success' : 'btn-danger'} px-4 py-2 rounded-pill shadow-sm`}
                    onClick={handleConfirm}
                    disabled={isLoading}
                >
                    {isLoading ? (
                        customLoadingHTML ? customLoadingHTML : <DefaultLoadingHTML />
                    ) : (
                        customButtonHTML ? customButtonHTML : <DefaultButtonHTML />
                    )}
                </button>
            </div>
        </div>
    );
};

export default ActiveInActive;