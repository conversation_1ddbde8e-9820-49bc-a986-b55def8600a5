import React from 'react';
import { getS3FilePath, ucwords } from '@/utils/helper';
import { useRouter } from 'next/navigation';

const FollowersFollowingList = (props) => {
    const { data, title } = props;
    const router = useRouter();
    const userDetails = title === 'Followers' ? 'followerDetails' : 'followingDetails';

    return (
        <div className="user-list profile-card">
            <h3 className="user-list-title">
                <span>{title}</span>
                <span className="list-count">{data?.length || 0}</span>
            </h3>
            <div className="user-list-container">
                {Array.isArray(data) && data.length > 0 ? (
                    data.map((result, index) => {
                        const user = result?.[userDetails];
                        return (
                            <div
                                key={user?.userId || index}
                                className="user-item"
                                onClick={() => user?.userId && router.push(`/user/details/${user.userId}`)}
                                style={{ cursor: user?.userId ? 'pointer' : 'default' }}
                            >
                                <img
                                    src={user?.profileImage ? getS3FilePath(user.profileImage) : "https://randomuser.me/api/portraits/men/1.jpg"}
                                    alt={user?.fullName?.trim() ? ucwords(user?.fullName) : 'Anonymous User'}
                                    className="user-avatar"
                                />
                                <div className="user-name">{user?.fullName?.trim() ? ucwords(user?.fullName) : 'Anonymous User'}</div>
                            </div>
                        );
                    })
                ) : (
                    <div className="text-center py-4 text-muted">No {title} found</div>
                )}
            </div>
        </div>
    );
};

export default FollowersFollowingList;