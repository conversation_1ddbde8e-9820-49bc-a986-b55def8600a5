import React, { forwardRef, useImperativeHandle, useState, useCallback, useRef } from 'react';
import { useFormFields } from '@/hooks/useFormRefs';
import { FileUploader } from '@/hooks/useFileRefs';
import { handleFileUpload } from '@/utils/helper';
import * as yup from 'yup';


const Form = forwardRef(({ onSubmit, cancelForm }, ref) => {

    const bulkUploadValidationSchema = yup.object().shape({
        web_users_file: yup.mixed()
            .test('fileRequired', 'File is required.', function (value) {
                return value && (typeof value === 'object' || typeof value === 'string');
            })
            .test(
                'fileType',
                'Only CSV, Excel (.xls, .xlsx), or Excel Macro-Enabled (.xlsm) files are allowed.',
                function (value) {
                    if (!value) return false;
                    if (typeof value === 'string') return true;
                    const file = value instanceof FileList ? value[0] : value;
                    if (!file || !file.name) return false;
                    const allowedExtensions = ['.csv', '.xls', '.xlsx', '.xlsm'];
                    const fileName = file.name.toLowerCase();
                    return allowedExtensions.some(ext => fileName.endsWith(ext));
                }
            ),
    });

    const {
        handleSubmit,
        errors,
        reset,
        setValue,
        trigger,
        createFormData,
    } = useFormFields(bulkUploadValidationSchema);

    const [isSubmitEnable, setIsSubmitEnable] = useState(false);
    const fileUploaderRef = useRef(null);

    useImperativeHandle(ref, () => ({
        resetForm: () => {
            reset();
        },
        disableSubmit: () => {
            setIsSubmitEnable(false);
        },
        enableSubmit: () => {
            setIsSubmitEnable(true);
        }
    }));

    const handleFormSubmit = useCallback((data) => {
        const formData = createFormData(data);
        onSubmit(formData);
    }, [createFormData, onSubmit]);

    const handleCancel = useCallback((e) => {
        e.preventDefault();
        ref.current?.resetForm();
        if (cancelForm) {
            cancelForm();
        }
    }, [cancelForm, ref]);


    const formHTML = () => {
        return (
            <form onSubmit={handleSubmit(handleFormSubmit)} encType="multipart/form-data" noValidate>
                <div className="mb-3">
                    <div className="row">
                        <div className="col-12">
                            <FileUploader
                                isRequired={false}
                                maxSize={100}
                                uploadType="bulk_upload"
                                error={errors.web_users_file?.message}
                                ref={fileUploaderRef}
                                onChange={(file) => handleFileUpload('web_users_file', file, setValue, trigger)}
                            />
                        </div>
                    </div>
                </div>
                <div className="d-flex justify-content-end gap-2">
                    <button type="button" className="btn btn-cancel" disabled={isSubmitEnable} onClick={handleCancel}>
                        <i className="fas fa-times me-1"></i> Cancel
                    </button>
                    <button type="submit" className="btn btn-submit" disabled={isSubmitEnable}>
                        {isSubmitEnable ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                                Uploading...
                            </>
                        ) : (
                            <>
                                <i className="fas fa-upload me-1"></i> Upload
                            </>
                        )}
                    </button>
                </div>
            </form>
        );
    };

    return (
        <>
            {formHTML()}
        </>
    );
});

Form.displayName = 'Form';

export default Form; 