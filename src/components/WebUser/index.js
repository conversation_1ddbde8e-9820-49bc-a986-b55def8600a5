import React, { useState, useEffect, useCallback, useRef } from 'react';
import { WebUserService } from '@/services';
import { useTitle } from '@/hooks/useTitle';
import { showToast, getSearchRecords, formatDateTimeCustom, shortenString } from '@/utils/helper';
import DataTable from 'react-data-table-component';
import { Loader, CommonModal, Common, WebUser } from '@/components';
import { useRouter } from 'next/navigation';

const WebUserList = () => {
    useTitle("Web Users");
    const router = useRouter();
    const [isContentLoad, setIsContentLoad] = useState(false);
    const [search, setSearch] = useState('');
    const [data, setData] = useState([]);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [currentPage, setCurrentPage] = useState(1);
    const [isLoading, setIsLoading] = useState(false);
    const [selectedUser, setSelectedUser] = useState(null);
    const [statusChangeInfo, setStatusChangeInfo] = useState({
        id: null,
        status: false,
        title: ''
    });

    const modelRefs = {
        formModalRef: useRef(null),
        formRef: useRef(null),
        statusModalRef: useRef(null),
        bulkUploadFormModalRef: useRef(null),
        bulkUploadFormRef: useRef(null)
    };

    const getList = useCallback(async (page, searchQuery) => {
        try {
            setIsContentLoad(true);
            const response = await WebUserService.getList(page, perPage, searchQuery);
            if (response?.data) {
                setData(response.data.data);
                setTotalRows(response.data.total);
            }
        } catch (error) {
            showToast(error?.message || 'Error getting details', 'error');
        } finally {
            setIsContentLoad(false);
        }
    }, [perPage]);

    useEffect(() => {
        getList(currentPage, search);
    }, [getList, currentPage]);

    const handlePageChange = useCallback((page) => {
        setCurrentPage(page);
    }, []);

    const handlePerRowsChange = useCallback(async (newPerPage, page) => {
        setPerPage(newPerPage);
        setCurrentPage(page);
    }, []);

    const handleSearch = useCallback(async (e) => {
        getSearchRecords(e, setSearch, setCurrentPage, getList);
    }, [getList]);

    const handleActionClickEvent = useCallback(async (e, id) => {
        try {
            e.stopPropagation();
            setIsLoading(true);
            const button = e.target.closest('button');
            if (!button) return;
            const title = button.dataset.bsTitle || '';
            if (title && title.toLowerCase().includes('view')) {
                router.push(`/webuser/details/${id}`);
            } else if (title && title.toLowerCase().includes('edit')) {
                router.push(`/webuser/edit/${id}`);
            }
        } catch (error) {
            setIsLoading(false);
            console.error('Navigation error:', error);
        }
    }, [router]);

    const handleStatusChange = useCallback(async (e, id, email) => {
        e.preventDefault();
        e.stopPropagation();
        const newStatus = e.target.checked ? 1 : 0;
        setStatusChangeInfo({ id, status: newStatus, title: email });
        requestAnimationFrame(() => {
            if (modelRefs.statusModalRef.current) {
                modelRefs.statusModalRef.current?.open();
            }
        });
    }, [modelRefs]);

    const confirmStatusChange = useCallback(async () => {
        try {
            setIsLoading(true);
            const response = await WebUserService.updateWebUserStatus(statusChangeInfo.id, statusChangeInfo.status);
            if (response?.status === 200) {
                showToast(response?.message || 'Status updated successfully', "success");
                await getList(currentPage, search);
                requestAnimationFrame(() => {
                    if (modelRefs.statusModalRef.current) {
                        modelRefs.statusModalRef.current?.close();
                    }
                });
            }
        } catch (error) {
            showToast(error?.message || 'Error updating status', "error");
        } finally {
            setIsLoading(false);
            requestAnimationFrame(() => {
                if (modelRefs.statusModalRef.current) {
                    modelRefs.statusModalRef.current?.close();
                }
            });
        }
    }, [statusChangeInfo, getList, currentPage, search]);

    const handleFormAction = useCallback((e, user = null) => {
        e.stopPropagation();
        if (user && user?.id) {
            setSelectedUser(user);
            modelRefs?.formModalRef.current?.open();
            requestAnimationFrame(() => {
                if (modelRefs?.formRef?.current) {
                    modelRefs.formRef.current.setFormValues(user);
                }
            });
        } else {
            setSelectedUser(null);
            modelRefs?.formModalRef.current?.open();
            requestAnimationFrame(() => {
                if (modelRefs?.formRef?.current) {
                    modelRefs.formRef.current.resetForm();
                }
            });
        }
    }, [modelRefs]);

    const handleFormSubmit = useCallback(async (formData) => {
        try {
            setIsLoading(true);
            requestAnimationFrame(() => {
                if (modelRefs?.formRef.current) {
                    modelRefs.formRef.current.enableSubmit();
                }
            });
            if (selectedUser) {
                formData.append('id', selectedUser.id);
            }
            const response = await WebUserService.updateWebUserDetails(formData);
            if (response?.message && response?.status === 200) {
                requestAnimationFrame(() => {
                    if (modelRefs?.formModalRef.current) {
                        modelRefs?.formModalRef.current?.close();
                    }
                });
                await getList(currentPage, search);
            }
            showToast(response?.message, 'success');
        } catch (error) {
            showToast(error?.message || 'Error saving user details', 'error');
        } finally {
            setIsLoading(false);
            requestAnimationFrame(() => {
                if (modelRefs?.formRef.current) {
                    modelRefs.formRef.current.disableSubmit();
                }
            });
        }
    }, [modelRefs, selectedUser, currentPage, search]);

    const handleUploadWebUsersSubmit = useCallback(async (formData) => {
        try {
            setIsLoading(true);
            requestAnimationFrame(() => {
                if (modelRefs?.bulkUploadFormRef.current) {
                    modelRefs.bulkUploadFormRef.current.enableSubmit();
                }
            });
            const response = await WebUserService.bulkUploadWebUsers(formData);
            if (response?.message && response?.status === 200) {
                requestAnimationFrame(() => {
                    if (modelRefs?.bulkUploadFormModalRef.current) {
                        modelRefs?.bulkUploadFormModalRef.current?.close();
                    }
                });
                await getList(currentPage, search);
            }
            showToast(response?.message, 'success');
        } catch (error) {
            showToast(error?.message || 'Error uploading web users', 'error');
        } finally {
            setIsLoading(false);
            requestAnimationFrame(() => {
                if (modelRefs?.bulkUploadFormRef.current) {
                    modelRefs.bulkUploadFormRef.current.disableSubmit();
                }
            });
        }
    }, [modelRefs]);

    const columns = [
        {
            name: 'No.',
            selector: (row, index) => ((currentPage - 1) * perPage) + index + 1,
            width: '80px',
        },
        {
            name: 'Email',
            selector: (row) => row?.email || '',
            sortable: true,
            wrap: true,
            grow: 4
        },
        {
            name: 'Full Name',
            selector: (row) => row?.fullName || '',
            sortable: true,
            wrap: true,
            grow: 2
        },
        {
            name: 'Ref. By',
            selector: (row) => (row?.id == row?.referralBy) ? row?.email : '',
            sortable: true,
            wrap: true,
            grow: 3
        },
        {
            name: 'Ref. Count',
            selector: (row) => row?.referralCount || 0,
            sortable: true,
            wrap: true,
            grow: 2,
            center: 'true'
        },
        {
            name: 'Total Points',
            selector: (row) => row?.totalPoints || 0,
            grow: 2,
            center: 'true'
        },
        {
            name: 'Created At',
            selector: (row) => formatDateTimeCustom(row?.created_at) || 'N/A',
            sortable: true,
            grow: 3
        },
        {
            name: 'Block/Unblock',
            selector: (row) => (
                <div className="form-check form-switch">
                    <input
                        className="form-check-input"
                        type="checkbox"
                        checked={row?.isBlock === 1}
                        onChange={(e) => handleStatusChange(e, row.id, row.email)}
                    />
                </div>
            ),
            center: 'true',
            grow: 2,
        },
        {
            name: 'Action',
            cell: (row) => (
                <div className="d-flex gap-1">
                    <button
                        type="button"
                        className="btn btn-sm btn-info"
                        onClick={(e) => handleActionClickEvent(e, row.id)}
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        data-bs-title="View Web User"
                    >
                        <i className="bi bi-eye"></i>
                    </button>
                    <button
                        type="button"
                        className="btn btn-sm btn-primary"
                        onClick={(e) => handleFormAction(e, row)}
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        data-bs-title="Edit Web User"
                    >
                        <i className="bi bi-pencil"></i>
                    </button>
                </div>
            ),
            ignoreRowClick: true,
            center: 'true',
        },
    ];

    const customStyles = {
        rows: { style: { minHeight: '50px' } },
        headCells: {
            style: {
                fontWeight: 'bold',
                fontSize: '14px',
            },
        },
    };

    return (
        <>
            {isLoading && <Loader />}
            <div className="content-wrapper">
                {/* Header */}
                <div className="content-header d-flex justify-content-between align-items-center">
                    <h1 id="page-title">Web Users List</h1>
                    <div className="d-flex gap-3">
                        <button type="button" className="btn btn-add" onClick={() => {
                            modelRefs?.bulkUploadFormModalRef.current?.open();
                        }}>
                            <i className="fas fa-file-upload me-1"></i> Upload Web Users
                        </button>
                    </div>
                </div>

                {/* Main content */}
                <div className="main-content">
                    <div className="card">
                        <div className="card-body">
                            <div className="d-flex justify-content-between align-items-center mb-4">
                                <div className="entries-select"></div>
                                <div className="search-box position-relative">
                                    <label>Search:</label>
                                    <input
                                        type="text"
                                        placeholder="Search user..."
                                        className="form-control form-control-sm"
                                        value={search}
                                        onChange={handleSearch}
                                    />
                                    {search && (
                                        <button
                                            className="btn btn-sm btn-light position-absolute"
                                            style={{
                                                right: '5px',
                                                top: '50%',
                                                transform: 'translateY(-50%)',
                                                padding: '0.25rem 0.5rem',
                                                borderRadius: '50%',
                                                boxShadow: '0 0 3px rgba(0,0,0,0.2)',
                                                backgroundColor: '#7c66d8',
                                                color: 'white'
                                            }}
                                            onClick={async () => {
                                                setSearch('');
                                                setCurrentPage(1);
                                                await getList(1, '');
                                            }}
                                        >
                                            <i className="bi bi-x-lg fw-bold"></i>
                                        </button>
                                    )}
                                </div>
                            </div>

                            <div className="table">
                                <DataTable
                                    columns={columns}
                                    data={data}
                                    pagination
                                    paginationServer
                                    paginationTotalRows={totalRows}
                                    onChangeRowsPerPage={handlePerRowsChange}
                                    onChangePage={handlePageChange}
                                    highlightOnHover
                                    striped
                                    responsive
                                    customStyles={customStyles}
                                    progressPending={isContentLoad}
                                    progressComponent={<Common.DataTableLoader />}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {/* Form Modal */}
            <CommonModal ref={modelRefs?.formModalRef} title={`${selectedUser && selectedUser?.id ? 'Edit' : 'Add New'} Web User`}>
                <WebUser.Form
                    ref={modelRefs?.formRef}
                    onSubmit={handleFormSubmit}
                    cancelForm={() => {
                        requestAnimationFrame(() => {
                            if (modelRefs?.formModalRef.current) {
                                modelRefs?.formModalRef.current?.close();
                            }
                        });
                    }}
                />
            </CommonModal>

            {/* Bulk Upload Form Modal */}
            <CommonModal ref={modelRefs?.bulkUploadFormModalRef} title="Upload Web Users" size="lg" icon="fa-file-excel" staticModal={true}>
                <WebUser.BulkUpload
                    ref={modelRefs?.bulkUploadFormRef}
                    onSubmit={handleUploadWebUsersSubmit}
                    cancelForm={() => {
                        requestAnimationFrame(() => {
                            if (modelRefs?.bulkUploadFormModalRef.current) {
                                modelRefs?.bulkUploadFormModalRef.current?.close();
                            }
                        });
                    }}
                />
            </CommonModal>

            {/* Status Change Modal */}
            <CommonModal
                ref={modelRefs.statusModalRef}
                staticModal={true}
                icon={statusChangeInfo.status === 1 ? 'bi bi-check-circle-fill' : 'bi bi-x-circle-fill'}
                title={`${statusChangeInfo.status === 1 ? 'Block' : 'Unblock'} Web User `}
            >

                <Common.ActiveInActive
                    statusChangeInfo={statusChangeInfo}
                    onConfirm={confirmStatusChange}
                    onCancel={() => {
                        requestAnimationFrame(() => {
                            if (modelRefs.statusModalRef.current) {
                                modelRefs.statusModalRef.current?.close();
                            }
                        });
                    }}
                    customMessageHTML={
                        <>
                            Are you sure you want to <span className="fw-semibold" style={{ color: statusChangeInfo.status ? '#28a745' : '#dc3545' }}>
                                {statusChangeInfo.status ? `block` : `unblock`}
                            </span> <span className="fw-bold">{statusChangeInfo.title ? shortenString(statusChangeInfo.title, 30) : ''} </span>web user?
                        </>
                    }
                    customLoadingHTML={
                        <>
                            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            {statusChangeInfo.status ? 'Blocking...' : 'Unblocking...'}
                        </>
                    }
                    customButtonHTML={
                        <>
                            <i className={`fas ${statusChangeInfo.status ? 'fa-check' : 'fa-times'} me-2`}></i>
                            {statusChangeInfo.status ? 'Block' : 'Unblock'}
                        </>
                    }
                />
            </CommonModal>
        </>
    );
};

export default WebUserList;
