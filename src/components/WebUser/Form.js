import React, { forwardRef, useImperative<PERSON><PERSON>le, useState, useCallback } from 'react';
import { useFormFields, Input } from '@/hooks/useFormRefs';
import { preventSpaces } from '@/utils/helper';
import * as yup from 'yup';

const webUserValidationSchema = yup.object().shape({
    fullName: yup.string().trim().required('Full name is required').min(2, 'Full name must be at least 3 characters long').max(50, 'Full name must be less than 50 characters long'),
    email: yup.string()
        .trim()
        .required("Email is required")
        .min(8, "Email must be at least 8 characters")
        .max(80, "Email must not exceed 50 characters")
        .test(
            "email-validation",
            "Please enter a valid email address.",
            (value) => /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/.test(value)
        )
        .test(
            "no-spaces",
            "Spaces are not applicable for email",
            (value) => !/\s/.test(value)
        ),
    userName: yup.string().trim().required('Username is required').min(2, 'Username must be at least 3 characters long').max(20, 'Username must be less than 20 characters long'),
});

const Form = forwardRef(({ onSubmit, cancelForm }, ref) => {
    const {
        register,
        handleSubmit,
        errors,
        reset,
        watch,
        createFormData,
        setValue
    } = useFormFields(webUserValidationSchema);

    const formValues = watch();
    const [id, setId] = useState(null);
    const [isSubmitEnable, setIsSubmitEnable] = useState(false);

    useImperativeHandle(ref, () => ({
        resetForm: () => {
            setId(null);
            reset();
        },
        setFormValues: async (data) => {
            if (data && data?.id) {
                setId(data.id);
            }
            const includedFields = ['fullName', 'email', 'userName', 'isActive'];
            Object.entries(data).forEach(([key, value]) => {
                if (includedFields.includes(key) && value) {
                    setValue(key, value);
                }
            });
        },
        disableSubmit: () => {
            setIsSubmitEnable(false);
        },
        enableSubmit: () => {
            setIsSubmitEnable(true);
        }
    }));

    const handleFormSubmit = useCallback((data) => {
        const formData = createFormData(data);
        if (id) {
            const isActive = data?.isActive ? 1 : 0;
            formData.append('isActive', isActive);
        }
        onSubmit(formData);
    }, [createFormData, onSubmit, id]);

    const handleCancel = useCallback((e) => {
        e.preventDefault();
        ref.current?.resetForm();
        if (cancelForm) {
            cancelForm();
        }
    }, [cancelForm, ref]);

    const formHTML = () => {
        return (
            <form onSubmit={handleSubmit(handleFormSubmit)} encType="multipart/form-data" noValidate>
                <div className="mb-3">
                    <Input
                        label="Full Name"
                        name="fullName"
                        type="text"
                        {...register('fullName')}
                        placeholder="Enter full name"
                        value={formValues?.fullName || ''}
                        icon="fas fa-user"
                        error={errors?.fullName?.message}
                    />
                </div>
                <div className="mb-3">
                    <Input
                        label="User Name"
                        name="userName"
                        type="text"
                        {...register('userName')}
                        placeholder="Enter userName"
                        value={formValues?.userName || ''}
                        icon="fas fa-user"
                        error={errors?.userName?.message}
                    />
                </div>
                <div className="mb-3">
                    <Input
                        label="Email ID"
                        name="email"
                        type="email"
                        {...register('email')}
                        placeholder="Enter email address"
                        value={formValues?.email || ''}
                        icon="fas fa-envelope"
                        onKeyDown={preventSpaces}
                        error={errors?.email?.message}
                    />
                </div>

                <div className="d-flex justify-content-end gap-2">
                    <button type="button" className="btn btn-cancel" disabled={isSubmitEnable} onClick={handleCancel}>
                        <i className="fas fa-times me-1"></i> Cancel
                    </button>
                    <button type="submit" className="btn btn-submit" disabled={isSubmitEnable}>
                        {isSubmitEnable ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                                Saving...
                            </>
                        ) : (
                            <>
                                <i className="fas fa-save me-1"></i> Save
                            </>
                        )}
                    </button>
                </div>
            </form>
        );
    };

    return (
        <>
            {formHTML()}
        </>
    );
});

Form.displayName = 'Form';

export default Form;