import React, { useState, useEffect, useCallback } from 'react';
import { WebUserService } from '@/services';
import { useTitle } from '@/hooks/useTitle';
import { showToast, ucwords, formatDateTimeCustom } from '@/utils/helper';
import { Loader, NotFound } from '@/components';
import messages from '@/utils/messages';
import Link from 'next/link';

const WebUserDetails = ({ id }) => {
    useTitle("Web User Details");
    const [isContentLoad, setIsContentLoad] = useState(false);
    const [userData, setUserData] = useState(null);
    const [userDetailsResStatus, setUserDetailsResStatus] = useState(null);

    const getWebUserDetails = useCallback(async (id) => {
        try {
            setIsContentLoad(true);
            const response = await WebUserService.getWebUserDetails(id);
            if (Object.keys(response?.data || {}).length > 0) {
                setUserData(response.data);
                setUserDetailsResStatus(response.status);
            }
        } catch (error) {
            setUserDetailsResStatus(404);
            showToast(error?.message || 'Error getting details', 'error');
        } finally {
            setIsContentLoad(false);
        }
    }, [id]);

    useEffect(() => {
        if (id) {
            getWebUserDetails(id);
        }
    }, [getWebUserDetails, id]);

    const refferalUsersHTML = () => {
        return (
            <div className="col-lg-6">
                <div className="card h-100">
                    <div className="card-header bg-success text-white d-flex justify-content-between align-items-center">
                        <h5 className="mb-0"><i className="fas fa-users me-2"></i>Referral Users</h5>
                        <span className="badge bg-white text-success">{Array.isArray(userData?.referralUsers) && userData?.referralUsers?.length + ' ' + (userData?.referralUsers?.length > 1 ? 'Users' : 'User') || 0 + ' User'}</span>
                    </div>
                    <div className="card-body p-0">
                        {Array.isArray(userData?.referralUsers) && userData?.referralUsers?.length > 0 ? (
                            <div className="table-responsive" style={{ maxHeight: "400px", overflowY: "auto" }}>
                                <table className="table custom-table mb-0">
                                    <thead>
                                        <tr>
                                            <th>No.</th>
                                            <th>Username</th>
                                            <th>Full Name</th>
                                            <th style={{ textAlign: 'center' }}>Email</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {userData?.referralUsers?.map((user, index) => (
                                            <tr key={index}>
                                                <td>{index + 1}</td>
                                                <td><span className="nowrap-username">{user?.userName}</span></td>
                                                <td>{user?.fullName || 'N/A'}</td>
                                                <td className="nowrap-email">{user?.email}</td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        ) : (
                            <div className="text-center py-4 d-flex flex-column justify-content-center align-items-center" style={{ height: "100%" }}>
                                <div className="mb-3">
                                    <i className="fas fa-users fa-3x" style={{ color: '#28a745', opacity: 0.5 }}></i>
                                </div>
                                <p className="text-muted">No referral users found</p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        );
    };

    const rewardHistoryHTML = () => {
        return (
            <div className="col-lg-6">
                <div className="card h-100">
                    <div className="card-header bg-info text-white d-flex justify-content-between align-items-center">
                        <h5 className="mb-0"><i className="fas fa-gift me-2"></i>Reward History</h5>
                        <span className="badge bg-white text-info">{userData?.totalPoints ? userData.totalPoints + ' ' + (userData.totalPoints > 1 ? 'Points' : 'Point') : '0 Point'}</span>
                    </div>
                    <div className="card-body p-0">
                        {Array.isArray(userData?.rewardHistory) && userData?.rewardHistory?.length > 0 ? (
                            <div className="table-responsive" style={{ maxHeight: "400px", overflowY: "auto" }}>
                                <table className="table custom-table mb-0">
                                    <thead>
                                        <tr>
                                            <th>No.</th>
                                            <th>Points</th>
                                            <th>Message</th>
                                            <th>Created At</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {userData?.rewardHistory?.map((reward, index) => (
                                            <tr key={index}>
                                                <td>{index + 1}</td>
                                                <td><span className="points-badge">{reward?.points}</span></td>
                                                <td>{reward?.message}</td>
                                                <td>
                                                    <div className="created-at">
                                                        <span className="date">{formatDateTimeCustom(reward?.created_at)}</span>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        ) : (
                            <div className="text-center py-4 d-flex flex-column justify-content-center align-items-center" style={{ height: "100%" }}>
                                <div className="mb-3">
                                    <i className="fas fa-gift fa-3x" style={{ color: '#17a2b8', opacity: 0.5 }}></i>
                                </div>
                                <p className="text-muted">No reward history found</p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        );
    };


    return (
        <>
            {isContentLoad && <Loader />}
            <div className="content-wrapper">
                <div className="content-header d-flex justify-content-between align-items-center">
                    <h1>Web User Details</h1>
                    <Link href="/webuser/list" className="btn btn-back">
                        <i className="fas fa-arrow-left me-2"></i>Back to Web Users List
                    </Link>
                </div>

                {userData && Object.keys(userData).length > 0 && userDetailsResStatus && userDetailsResStatus === 200 ? (
                    <>
                        <div className="row g-4 mb-4">
                            <div className="col-12">
                                <div className="card h-100 profile-details-card-modern">
                                    <div className="card-body p-0">
                                        <div className="d-flex flex-column flex-md-row align-items-stretch justify-content-center text-center text-md-start">
                                            <div className="profile-detail-modern flex-fill d-flex flex-column align-items-center justify-content-center py-4 px-3">
                                                <div className="profile-detail-icon mb-2"><i className="fas fa-user-circle"></i></div>
                                                <div className="profile-detail-label-modern">Full Name</div>
                                                <div className="profile-detail-value-modern">{userData?.fullName?.trim() ? ucwords(userData.fullName) : 'Anonymous User'}</div>
                                            </div>
                                            <div className="profile-detail-modern-separator d-none d-md-block"></div>
                                            <div className="profile-detail-modern flex-fill d-flex flex-column align-items-center justify-content-center py-4 px-3">
                                                <div className="profile-detail-icon mb-2"><i className="fas fa-user-tag"></i></div>
                                                <div className="profile-detail-label-modern">Username</div>
                                                <div className="profile-detail-value-modern">{userData?.userName?.trim() || 'anonymous'}</div>
                                            </div>
                                            <div className="profile-detail-modern-separator d-none d-md-block"></div>
                                            <div className="profile-detail-modern flex-fill d-flex flex-column align-items-center justify-content-center py-4 px-3">
                                                <div className="profile-detail-icon mb-2"><i className="fas fa-envelope"></i></div>
                                                <div className="profile-detail-label-modern">Email</div>
                                                <div className="profile-detail-value-modern">{userData?.email || 'Not Provided'}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="main-content">
                            <div className="row g-4">
                                {refferalUsersHTML()}
                                {rewardHistoryHTML()}
                            </div>
                        </div>
                    </>
                ) : (
                    !isContentLoad && userDetailsResStatus && userDetailsResStatus !== 200 && (
                        <div className="col-12">
                            <div className="card shadow-sm border-0">
                                <NotFound message={messages.USER_DETAILS_NOT_FOUND || 'User details not found'} />
                            </div>
                        </div>
                    )
                )}
            </div>
        </>
    );
};

export default WebUserDetails;


