"use client"
import { forwardRef, useState, useEffect, useRef, useCallback, useImperativeHandle } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import Select from 'react-select';
import { AsyncPaginate } from "react-select-async-paginate";


// Custom hook for form with multiple fields
export const useFormFields = (validationSchema) => {
  const form = useForm({
    resolver: yupResolver(validationSchema),
    mode: 'onChange'
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset,
    trigger
  } = form;

  const createFormData = useCallback((data) => {
    const formData = new FormData();
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value) {
        formData.append(key, value);
      }
    });
    return formData;
  }, []);

  return {
    register,
    handleSubmit,
    errors,
    watch,
    setValue,
    reset,
    trigger,
    createFormData
  };
};

// Input Component with ForwardRef
export const Input = forwardRef((props, ref) => {
  const {
    name,
    label,
    type = 'text',
    placeholder,
    onChange,
    onKeyDown,
    onPaste,
    onKeyUp,
    className,
    isRequired = true,
    value,
    error,
    icon,
    characterCount = false,
    maxLength,
    ...rest
  } = props ?? {};

  const [count, setCount] = useState(0);

  useEffect(() => {
    if (characterCount && value) {
      setCount(value.length);
    }
  }, [value, characterCount]);

  const handleEvents = useCallback({
    change: (e) => {
      if (characterCount) {
        setCount(e.target.value.length);
      }
      if (onChange) {
        onChange(e);
      }
    },
    keyDown: (e) => {
      if (onKeyDown) {
        onKeyDown(e);
      }
    },
    paste: (e) => {
      if (onPaste) {
        onPaste(e);
      }
    },
    keyUp: (e) => {
      if (onKeyUp) {
        onKeyUp(e);
      }
    }
  }, [characterCount, onChange, onKeyDown, onPaste, onKeyUp]);

  let inputHtml = (
    <div className="form-group">
      <label htmlFor={name} className="form-label">{label} {isRequired && <span className="required-asterisk">*</span>}</label>
      <div className="input-group">
        {icon && <span className={`input-group-text ${error ? 'error' : ''}`}><i className={icon}></i></span>}
        <input
          ref={ref}
          type={type}
          id={name}
          name={name}
          className={`form-control ${error ? 'error' : ''}`}
          placeholder={placeholder ? placeholder : `Type ${label.toLowerCase()} here...`}
          onChange={handleEvents.change}
          onKeyDown={handleEvents.keyDown}
          onPaste={handleEvents.paste}
          onKeyUp={handleEvents.keyUp}
          value={value}
          maxLength={maxLength}
          {...rest}
        />
        {error && <div className="invalid-feedback" style={{ display: 'block' }}>{error}</div>}
        {characterCount && (
          <div className="character-count">{count} characters</div>
        )}
      </div>
    </div>
  );

  return inputHtml;
});

Input.displayName = 'Input';

// TextArea Component with ForwardRef
export const TextArea = forwardRef((props, ref) => {
  const {
    name,
    label,
    placeholder,
    onChange,
    value,
    error,
    characterCount = false,
    maxLength,
    rows = 3,
    cols = 50,
    isRequired = true,
    icon,
    ...rest
  } = props ?? {};

  const [count, setCount] = useState(0);

  useEffect(() => {
    if (characterCount && value) {
      setCount(value.length);
    }
  }, [value, characterCount]);

  const handleChange = useCallback((e) => {
    if (characterCount) {
      setCount(e.target.value.length);
    }
    if (onChange) {
      onChange(e);
    }
  }, [characterCount, onChange]);

  return (
    <div className="form-group">
      <label htmlFor={name} className="form-label">{label} {isRequired && <span className="required-asterisk">*</span>}</label>
      <div className="input-group">
        {icon && <span className={`input-group-text ${error ? 'error' : ''}`}><i className={icon}></i></span>}
        <textarea
          ref={ref}
          id={name}
          name={name}
          className={`form-control ${error ? 'error' : ''}`}
          placeholder={placeholder ? placeholder : `Type ${label.toLowerCase()} here...`}
          onChange={handleChange}
          value={value}
          maxLength={maxLength}
          style={{ height: 'auto' }}
          cols={cols}
          rows={rows}
          {...rest}
        />
        {error && <div className="invalid-feedback" style={{ display: 'block' }}>{error}</div>}
        {characterCount && (
          <div className="character-count">{count} characters</div>
        )}
      </div>
    </div>
  );
});

TextArea.displayName = 'TextArea';

// Common styles and props for all pickers
const getCommonSx = (error) => ({
  '& .MuiOutlinedInput-root': {
    height: '45px',
    fontSize: '0.95rem',
    borderRadius: '8px',
    backgroundColor: '#fff',
    '& fieldset': {
      borderColor: error ? '#dc3545' : 'rgba(78, 115, 223, 0.2)',
    },
    '&:hover fieldset': {
      borderColor: 'rgba(78, 115, 223, 0.4)',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#4e73df',
      boxShadow: '0 0 0 0.2rem rgba(78, 115, 223, 0.15)',
    },
  },
  '& .MuiInputBase-input': {
    padding: '0.6rem 1rem',
    color: '#2e3338',
  },
  '& .MuiPickersSectionList-root': {
    padding: '10px 0px',
  },
  '& .MuiInputAdornment-root': {
    visibility: 'visible',
    marginRight: '8px',
  },
  '& .MuiSvgIcon-root': {
    color: '#4e73df',
    fontSize: '1.2rem',
  },
});

const getCommonSlotProps = (name, error) => ({
  textField: {
    name,
    fullWidth: true,
    error: !!error,
    readOnly: true,
    onKeyDown: (e) => e.preventDefault(),
    sx: getCommonSx(error)
  },
});

// Component wrapper
const PickerWrapper = (props) => {
  const {
    label,
    name,
    icon,
    error,
    isRequired = true,
    children
  } = props ?? {};

  return (
    <div className="form-group">
      <label htmlFor={name} className="form-label">
        {label} {isRequired && <span className="required-asterisk">*</span>}
      </label>
      <div className="input-group">
        {icon && (
          <span className="input-group-text">
            <i className={`fas fa-${icon || 'calendar-alt'}`}></i>
          </span>
        )}
        {children}
        {error && (
          <div className="invalid-feedback" style={{ display: 'block' }}>
            {error}
          </div>
        )}
      </div>
    </div>
  );
};

// 1. DatePickerInput Component
export const DatePickerInput = forwardRef((props, ref) => {
  const {
    label,
    value,
    onChange,
    error,
    name,
    isRequired = true,
    ...rest
  } = props ?? {};

  const { maxDate, minDate, icon } = rest;
  const currentDateTime = new Date();

  const handleChange = useCallback(
    (date) => {
      if (onChange) {
        if (!date) {
          onChange(null);
          return;
        }

        const selectedDate = new Date(date);

        if (selectedDate > currentDateTime) {
          onChange(currentDateTime);
        } else {
          onChange(date);
        }
      }
    },
    [onChange, currentDateTime]
  );

  return (
    <PickerWrapper label={label} name={name} icon={icon} error={error} isRequired={isRequired}>
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <DatePicker
          inputRef={ref}
          value={value || null}
          onChange={handleChange}
          maxDate={maxDate || currentDateTime}
          minDate={minDate || undefined}
          format="dd MMM yyyy"
          slotProps={getCommonSlotProps(name, error)}
          {...rest}
        />
      </LocalizationProvider>
    </PickerWrapper>
  );
}
);

DatePickerInput.displayName = 'DatePickerInput';

// 2. TimePickerInput Component
export const TimePickerInput = forwardRef((props, ref) => {
  const {
    label,
    value,
    onChange,
    error,
    name,
    isRequired = true,
    ...rest
  } = props ?? {};

  const { maxTime, minTime, icon } = rest;
  const currentDateTime = new Date();

  const handleChange = useCallback(
    (date) => {
      if (onChange) {
        if (!date) {
          onChange(null);
          return;
        }

        const selectedDate = new Date(date);
        const currentTime = new Date();
        const selectedTime = new Date(
          currentTime.getFullYear(),
          currentTime.getMonth(),
          currentTime.getDate(),
          selectedDate.getHours(),
          selectedDate.getMinutes()
        );

        if (selectedTime > currentDateTime) {
          onChange(currentDateTime);
        } else {
          onChange(date);
        }
      }
    },
    [onChange, currentDateTime]
  );

  return (
    <PickerWrapper label={label} name={name} icon={icon} error={error} isRequired={isRequired}>
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <TimePicker
          inputRef={ref}
          value={value || null}
          onChange={handleChange}
          maxTime={maxTime || currentDateTime}
          minTime={minTime || undefined}
          format="hh:mm aa"
          ampm
          slotProps={getCommonSlotProps(name, error)}
          {...rest}
        />
      </LocalizationProvider>
    </PickerWrapper>
  );
}
);

TimePickerInput.displayName = 'TimePickerInput';

// 3. DateTimePickerInput Component
export const DateTimePickerInput = forwardRef((props, ref) => {
  const {
    label,
    value,
    onChange,
    error,
    name,
    isRequired = true,
    ...rest
  } = props ?? {};

  const { maxDateTime, minDateTime, icon } = rest;
  const currentDateTime = new Date();

  const handleChange = useCallback(
    (date) => {
      if (onChange) {
        if (!date) {
          onChange(null);
          return;
        }

        const selectedDate = new Date(date);

        if (selectedDate > currentDateTime) {
          onChange(currentDateTime);
        } else {
          onChange(date);
        }
      }
    },
    [onChange, currentDateTime]
  );

  return (
    <PickerWrapper label={label} name={name} icon={icon} error={error} isRequired={isRequired}>
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <DateTimePicker
          inputRef={ref}
          value={value || null}
          onChange={handleChange}
          maxDateTime={maxDateTime || currentDateTime}
          minDateTime={minDateTime || undefined}
          format="dd MMM yyyy hh:mm aa"
          ampm
          slotProps={getCommonSlotProps(name, error)}
          {...rest}
        />
      </LocalizationProvider>
    </PickerWrapper>
  );
}
);

DateTimePickerInput.displayName = 'DateTimePickerInput';

// React Select Component with ForwardRef
export const ReactSelect = forwardRef((props, ref) => {
  const {
    options = [],
    placeholder = "Select an option...",
    onChange,
    onBlur,
    isMulti = false,
    isSearchable = true,
    className = "",
    icon = null,
    name,
    value,
    error,
    id,
    disabled = false,
    label,
    isRequired = true
  } = props ?? {};

  const [touched, setTouched] = useState(false);
  const [menuIsOpen, setMenuIsOpen] = useState(false);
  const selectRef = useRef(null);

  const handleChange = (selected) => {
    if (onChange) {
      onChange(selected);
    }
    if (!touched) {
      setTouched(true);
      if (onBlur) onBlur({ target: { name } });
    }
  };

  const handleBlur = () => {
    if (!touched && onBlur) {
      setTouched(true);
      onBlur({ target: { name } });
    }
  };

  useImperativeHandle(ref, () => ({
    name,
    value: isMulti
      ? value?.map(opt => opt.value) || []
      : value?.value || '',

    setValue: (newValue) => {
      if (isMulti && Array.isArray(newValue)) {
        const newSelectedOptions = newValue
          .map(val => options.find(opt => opt.value === val))
          .filter(Boolean);
        handleChange(newSelectedOptions);
      } else {
        const option = options.find(opt => opt.value === newValue);
        if (option) {
          handleChange(option);
        }
      }
    },

    getValue: () => isMulti
      ? value?.map(opt => opt.value) || []
      : value?.value || '',

    clear: () => {
      handleChange(isMulti ? [] : null);
    },
    focus: () => {
      selectRef.current?.focus();
    }
  }));

  const customStyles = {
    container: (base) => ({
      ...base,
      width: '100%',
    }),
    control: (base, state) => ({
      ...base,
      minHeight: '45px',
      fontSize: '0.95rem',
      backgroundColor: '#fff',
      borderColor: error ? '#dc3545' : state.isFocused ? '#80bdff' : 'rgba(78, 115, 223, 0.2)',
      borderRadius: '8px',
      boxShadow: error && state.isFocused
        ? '0 0 0 0.2rem rgba(220,53,69,.25)'
        : state.isFocused
          ? '0 0 0 0.2rem rgba(0,123,255,.25)'
          : 'none',
      '&:hover': {
        borderColor: error ? '#dc3545' : state.isFocused ? '#4e73df' : 'rgba(78, 115, 223, 0.4)'
      }
    }),
    input: (base) => ({
      ...base,
      color: '#2e3338',
      margin: 0,
      padding: 0,
    }),
    valueContainer: (base) => ({
      ...base,
      padding: '0.6rem 1rem',
    }),
    singleValue: (base) => ({
      ...base,
      color: '#2e3338',
    }),
    placeholder: (base) => ({
      ...base,
      color: '#6c757d',
    }),
    menu: (base) => ({
      ...base,
      backgroundColor: '#fff',
      borderRadius: '8px',
      boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)',
      zIndex: 9999,
    }),
    option: (base, state) => ({
      ...base,
      backgroundColor: state.isSelected ? '#4e73df' : state.isFocused ? 'rgba(78, 115, 223, 0.1)' : '#fff',
      color: state.isSelected ? '#fff' : '#2e3338',
      '&:hover': {
        backgroundColor: state.isSelected ? '#4e73df' : 'rgba(78, 115, 223, 0.1)',
      },
      padding: '0.6rem 1rem',
      fontSize: '0.95rem',
    }),
    multiValue: (base) => ({
      ...base,
      backgroundColor: 'rgba(78, 115, 223, 0.1)',
      borderRadius: '4px',
    }),
    multiValueLabel: (base) => ({
      ...base,
      color: '#4e73df',
      padding: '0.25rem 0.5rem',
      fontSize: '0.9rem',
    }),
    multiValueRemove: (base) => ({
      ...base,
      color: '#4e73df',
      '&:hover': {
        backgroundColor: 'rgba(78, 115, 223, 0.2)',
        color: '#dc3545',
      },
    }),
  };

  return (
    <div className="form-group">
      <label htmlFor={name} className="form-label">{label} {isRequired && <span className="required-asterisk">*</span>}</label>
      <div className={`input-group ${className ? className : ''}`}>
        {icon && <span className="input-group-text"><i className={`fas fa-${icon || 'caret-down'}`}></i></span>}
        <Select
          ref={selectRef}
          id={id || name}
          name={name}
          options={options}
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          isMulti={isMulti}
          isSearchable={isSearchable}
          isClearable={true}
          isDisabled={disabled}
          placeholder={placeholder}
          className={`react-select ${error ? 'is-invalid' : ''}`}
          classNamePrefix="react-select"
          onMenuToggle={() => setMenuIsOpen(!menuIsOpen)}
          styles={customStyles}
        />
        {error && (
          <div className="invalid-feedback" style={{ display: 'block' }}>
            {error}
          </div>
        )}
      </div>
    </div>
  );
});

ReactSelect.displayName = 'ReactSelect';

// Input Radio Switch Component with ForwardRef
export const InputRadioSwitch = forwardRef((props, ref) => {
  const {
    label,
    onChange,
    value = false,
    disabled = false,
    className = "mb-4",
    name
  } = props ?? {};

  const handleChange = (e) => {
    if (onChange) {
      onChange(e.target.checked);
    }
  };

  return (
    <div className={`${className || ''} form-check form-switch ps-0`}>
      <div className="d-flex align-items-center">
        <label className="form-check-label me-2 mt-1" style={{ fontSize: '1.1rem' }} htmlFor={`${name || label}_${Date.now()}`}>{label}</label>
        <div className="form-check form-switch mb-0">
          <input
            className="form-check-input"
            type="checkbox"
            id={`${name || label}_${Date.now()}`}
            checked={value}
            disabled={disabled}
            onChange={handleChange}
            ref={ref}
          />
        </div>
      </div>
    </div>
  );
});

InputRadioSwitch.displayName = 'InputRadioSwitch';

// Input Checkbox Component with ForwardRef
export const InputCheckBox = forwardRef((props, ref) => {
  const {
    label,
    onChange,
    value = false,
    disabled = false,
    className = "mb-3",
    name,
    error
  } = props ?? {};

  const handleChange = (e) => {
    if (onChange) {
      onChange(e.target.checked);
    }
  };

  return (
    <div className={`${className || ''} form-check`}>
      <div className="d-flex align-items-center">
        <input
          className={`form-check-input me-2 ${error ? 'is-invalid' : ''}`}
          type="checkbox"
          id={`${name || label}_${Date.now()}`}
          checked={value}
          disabled={disabled}
          onChange={handleChange}
          ref={ref}
        />
        <label
          className="form-check-label"
          style={{ fontSize: '1rem' }}
          htmlFor={`${name || label}_${Date.now()}`}
        >
          {label}
        </label>
      </div>
      {error && (
        <div className="invalid-feedback" style={{ display: 'block' }}>
          {error}
        </div>
      )}
    </div>
  );
});

InputCheckBox.displayName = 'InputCheckBox';

// Searchable Select Component with ForwardRef
export const SearchableSelect = forwardRef((props, ref) => {
  const {
    onChange,
    loadOptions,
    value,
    placeholder = "Search...",
    isClearable = true,
    onMenuOpen,
    onMenuClose,
    error,
    name,
    label,
    isRequired = true
  } = props ?? {};

  const customStyles =
  {
    control: (base, state) => ({
      ...base,
      borderRadius: 8,
      borderColor: error ? '#dc3545' : state.isFocused ? '#6f42c1' : '#ccc',
      minHeight: 40,
      boxShadow: error && state.isFocused
        ? '0 0 0 0.2rem rgba(220,53,69,.25)'
        : state.isFocused
          ? '0 0 0 0.2rem rgba(111, 66, 193, 0.25)'
          : 'none',
      '&:hover': {
        borderColor: error ? '#dc3545' : state.isFocused ? '#6f42c1' : 'rgba(78, 115, 223, 0.4)'
      }
    }),
    option: (base, state) => ({
      ...base,
      backgroundColor: state.isSelected
        ? 'rgba(111, 66, 193, 0.9)'
        : state.isFocused
          ? 'rgba(111, 66, 193, 0.1)'
          : '#fff',
      color: state.isSelected ? '#fff' : '#333',
      padding: '10px 15px',
      cursor: 'pointer',
      transition: 'background-color 0.2s ease, color 0.2s ease, transform 0.1s ease',
      '&:active': {
        backgroundColor: 'rgba(111, 66, 193, 0.7)'
      },
      '&:hover': {
        backgroundColor: state.isSelected
          ? 'rgba(111, 66, 193, 1)'
          : 'rgba(111, 66, 193, 0.15)',
        transform: 'translateX(2px)'
      }
    }),
    menu: (base) => ({
      ...base,
      borderRadius: '8px',
      boxShadow: '0 4px 20px rgba(111, 66, 193, 0.2)',
      border: '1px solid rgba(111, 66, 193, 0.2)',
      zIndex: 10,
      animation: 'fadeIn 0.2s ease-in-out',
      '@keyframes fadeIn': {
        '0%': { opacity: 0, transform: 'translateY(-10px)' },
        '100%': { opacity: 1, transform: 'translateY(0)' }
      }
    }),
    menuList: (base) => ({
      ...base,
      padding: '5px',
      '::-webkit-scrollbar': {
        width: '8px',
        height: '0px',
      },
      '::-webkit-scrollbar-track': {
        background: '#f1f1f1',
        borderRadius: '10px'
      },
      '::-webkit-scrollbar-thumb': {
        background: '#6f42c1',
        borderRadius: '10px',
        '&:hover': {
          background: '#5e35b1'
        }
      }
    }),
    multiValue: (base) => ({
      ...base,
      backgroundColor: 'rgba(111, 66, 193, 0.1)',
      borderRadius: '4px',
      transition: 'all 0.2s ease',
      '&:hover': {
        backgroundColor: 'rgba(111, 66, 193, 0.2)',
        transform: 'scale(1.02)'
      }
    }),
    multiValueLabel: (base) => ({
      ...base,
      color: '#6f42c1',
      fontWeight: 500
    }),
    multiValueRemove: (base) => ({
      ...base,
      color: '#6f42c1',
      '&:hover': {
        backgroundColor: 'rgba(111, 66, 193, 0.3)',
        color: '#fff',
        borderRadius: '0 4px 4px 0'
      }
    }),
    input: (base) => ({
      ...base,
      color: '#495057'
    }),
    placeholder: (base) => ({
      ...base,
      color: '#aaa'
    }),
    singleValue: (base) => ({
      ...base,
      color: '#495057',
      transition: 'opacity 0.2s ease, transform 0.2s ease'
    }),
    indicatorSeparator: (base) => ({
      ...base,
      backgroundColor: 'rgba(111, 66, 193, 0.2)'
    }),
    dropdownIndicator: (base, state) => ({
      ...base,
      color: state.isFocused ? '#6f42c1' : '#aaa',
      '&:hover': {
        color: '#6f42c1',
        transform: 'scale(1.2)'
      },
      transition: 'all 0.2s ease'
    }),
    clearIndicator: (base) => ({
      ...base,
      color: '#aaa',
      '&:hover': {
        color: '#6f42c1',
        transform: 'scale(1.2)'
      },
      transition: 'all 0.2s ease'
    })
  };

  return (
    <div className="form-group">
      <label htmlFor={name} className="form-label">{label} {isRequired && <span className="required-asterisk">*</span>}</label>
      <AsyncPaginate
        ref={ref}
        value={value}
        onChange={onChange}
        loadOptions={loadOptions}
        placeholder={placeholder}
        debounceTimeout={300}
        additional={{ page: 1 }}
        isClearable={isClearable}
        loadingMessage={() => "Loading results..."}
        noOptionsMessage={() => "No results found"}
        onMenuOpen={onMenuOpen}
        onMenuClose={onMenuClose}
        styles={customStyles}
      />
      {error && <div className="invalid-feedback" style={{ display: 'block' }}>{error}</div>}
    </div>
  );
}
);

SearchableSelect.displayName = 'SearchableSelect';