import React, { forwardRef, useState, useRef, useImperative<PERSON>andle, useCallback } from 'react';
import { ucwords, truncateFileName, showToast } from '@/utils/helper';

const formatBytes = (bytes, decimals = 2) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

const getFileIconClass = (filename) => {
    if (!filename) return 'bi-file-earmark';

    const extension = filename.split('.').pop()?.toLowerCase();
    if (!extension) return 'bi-file-earmark';

    switch (extension) {
        case 'pdf': return 'bi-file-earmark-pdf';
        case 'doc':
        case 'docx': return 'bi-file-earmark-word';
        case 'txt': return 'bi-file-earmark-text';

        case 'csv': return 'bi-file-earmark-spreadsheet';

        case 'xls':
        case 'xlsm':
        case 'xlsx': return 'bi-file-earmark-excel';

        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
        case 'svg':
        case 'webp': return 'bi-file-earmark-image';

        case 'mp4':
        case 'mov':
        case 'avi':
        case 'mkv':
        case 'wmv':
        case 'webm': return 'bi-file-earmark-play';

        default: return 'bi-file-earmark';
    }
}

const getFileType = (file) => {
    if (!file || typeof file !== 'object') return null;

    if (file.type && typeof file.type === 'string') {
        const type = file.type.toLowerCase();
        if (type.startsWith('image/')) {
            return 'image';
        } else if (type === 'application/pdf') {
            return 'document';
        } else if (type === 'video/mp4' || type === 'video/quicktime' || type === 'video/x-msvideo' || type === 'video/x-matroska' || type === 'video/webm') {
            return 'video';
        } else if (type === 'text/csv') {
            return 'csv';
        } else if (
            type === 'application/vnd.ms-excel' ||
            type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            type === 'application/vnd.ms-excel.sheet.macroenabled.12'
        ) {
            return 'excel';
        }
    }

    if (!file.name || typeof file.name !== 'string') return null;

    const extension = file.name.split('.').pop()?.toLowerCase();
    if (!extension) return null;

    if (['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'].includes(extension)) {
        return 'image';
    }

    if (['pdf', 'doc', 'docx', 'txt'].includes(extension)) {
        return 'document';
    }

    if (['mp4', 'mov', 'avi', 'mkv', 'wmv', 'webm'].includes(extension)) {
        return 'video';
    }

    if (['csv'].includes(extension)) {
        return 'csv';
    }

    if (['xls', 'xlsx', 'xlsm'].includes(extension)) {
        return 'excel';
    }

    return null;
}

const getAcceptedAndSupportedFormats = (uploadType) => {
    let acceptedFormats, supportedFormats;
    let acceptedFormatsObject = {};
    switch (uploadType) {
        case "multi_media":
            acceptedFormats = ".pdf,image/*";
            supportedFormats = "PDF, PNG, JPG and JPEG";
            acceptedFormatsObject = {
                "application/pdf": "PDF",
                "image/png": "PNG",
                "image/jpeg": "JPG",
                "image/jpg": "JPG"
            };
            break;
        case "image":
        case "images":
        case "profile_image":
            acceptedFormats = "image/*";
            supportedFormats = "PNG, JPG and JPEG";
            acceptedFormatsObject = {
                "image/png": "PNG",
                "image/jpeg": "JPG",
                "image/jpg": "JPG"
            };
            break;
        case "document":
        case "documents":
            acceptedFormats = ".pdf";
            supportedFormats = "PDF";
            acceptedFormatsObject = {
                "application/pdf": "PDF"
            };
            break;
        case "bulk_upload":
            acceptedFormats = ".csv, .xls, .xlsx, .xlsm";
            supportedFormats = "CSV, Excel and Excel Macro Enabled";
            acceptedFormatsObject = {
                "text/csv": "CSV",
                "application/vnd.ms-excel": "Excel",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "Excel",
                "application/vnd.ms-excel.sheet.macroenabled.12": "Excel",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.macroenabled.12": "Excel"
            };
            break;
        default:
            acceptedFormats = "image/*";
            supportedFormats = "PNG, JPG and JPEG";
            acceptedFormatsObject = {
                "image/png": "PNG",
                "image/jpeg": "JPG",
                "image/jpg": "JPG"
            };
            break;
    }
    return { acceptedFormats, supportedFormats, acceptedFormatsObject };
}

const useDragAndDrop = (onDrop, isMultiple) => {
    const [isDragging, setIsDragging] = useState(false);
    const dragCounter = useRef(0);
    const validationInProgress = useRef(false);

    const handleDrag = useCallback((e) => {
        e.preventDefault();
        e.stopPropagation();
    }, []);

    const handleDragIn = useCallback((e) => {
        e.preventDefault();
        e.stopPropagation();
        dragCounter.current += 1;
        if (dragCounter.current === 1) {
            setIsDragging(true);
        }
    }, []);

    const handleDragOut = useCallback((e) => {
        e.preventDefault();
        e.stopPropagation();
        dragCounter.current -= 1;
        if (dragCounter.current === 0) {
            setIsDragging(false);
        }
    }, []);

    const handleDrop = useCallback(async (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
        dragCounter.current = 0;

        if (validationInProgress.current) return;
        validationInProgress.current = true;

        try {
            const droppedFiles = e.dataTransfer.files;
            if (droppedFiles && droppedFiles.length > 0) {
                if (isMultiple) {
                    await onDrop(droppedFiles);
                } else {
                    const file = droppedFiles[0];
                    await onDrop([file]);
                }
            }
        } finally {
            validationInProgress.current = false;
        }
    }, [onDrop, isMultiple]);

    return {
        isDragging,
        dragHandlers: {
            onDragEnter: handleDragIn,
            onDragLeave: handleDragOut,
            onDragOver: handleDrag,
            onDrop: handleDrop
        }
    };
};

const useFileValidation = () => {
    const validationInProgress = useRef(false);

    const validateFiles = useCallback((files, allowedFormats, MAX_SIZE, fileRef, mediaType, isMultiple = false) => {
        if (validationInProgress.current) return { isValid: false, validFiles: [] };
        validationInProgress.current = true;

        try {
            if (!files || files.length === 0) return { isValid: true, validFiles: [] };

            const validFiles = [];
            const invalidFiles = [];
            let hasInvalidFormat = false;
            let hasInvalidSize = false;

            files.forEach(file => {
                let isValid = true;
                if (!allowedFormats[file.type]) {
                    isValid = false;
                    hasInvalidFormat = true;
                    invalidFiles.push(file.name);
                }

                const fileSizeMB = Math.round(file.size / 1024 / 1024 * 10) / 10;
                if (fileSizeMB > MAX_SIZE) {
                    isValid = false;
                    hasInvalidSize = true;
                    invalidFiles.push(file.name);
                }

                if (isValid) {
                    validFiles.push(file);
                }
            });

            if (invalidFiles.length > 0) {
                if (hasInvalidFormat) {
                    const formatNames = [...new Set(Object.values(allowedFormats))];
                    const formatsText = formatNames.length > 1
                        ? `${formatNames.slice(0, -1).join(', ')} and ${formatNames[formatNames.length - 1]}`
                        : formatNames[0];
                    const formatMessage = `Only ${formatsText} ${formatNames.length > 1 ? 'formats are' : 'format is'} allowed.`;

                    if (isMultiple) {
                        showToast(`Some files have an invalid ${mediaType.toLowerCase()} format and will not be included. ${formatMessage}`, 'error');
                    } else {
                        showToast(`Invalid ${mediaType} format. ${formatMessage}`, 'error');
                        if (fileRef) fileRef.current.value = '';
                        return { isValid: false, validFiles: [] };
                    }
                }

                if (hasInvalidSize) {
                    const sizeMessage = `${isMultiple ? 'Some files exceed' : `${mediaType} size must be`} ${MAX_SIZE} MB${isMultiple ? ' and will not be included.' : ' or less.'}`;
                    showToast(sizeMessage, 'error');

                    if (!isMultiple) {
                        if (fileRef) fileRef.current.value = '';
                        return { isValid: false, validFiles: [] };
                    }
                }
            }

            return {
                isValid: isMultiple ? validFiles.length > 0 : invalidFiles.length === 0 && validFiles.length > 0,
                validFiles
            };
        } finally {
            validationInProgress.current = false;
        }
    }, []);

    const validateImage = useCallback((files, MAX_SIZE = 15, fileRef, isMultiple = false) => {
        const { acceptedFormatsObject } = getAcceptedAndSupportedFormats('image');
        return validateFiles(
            Array.isArray(files) ? files : [files],
            acceptedFormatsObject,
            MAX_SIZE,
            fileRef,
            'Image',
            isMultiple
        );
    }, [validateFiles]);

    const validateDocument = useCallback((files, MAX_SIZE = 15, fileRef, isMultiple = false) => {
        const { acceptedFormatsObject } = getAcceptedAndSupportedFormats('document');
        return validateFiles(
            Array.isArray(files) ? files : [files],
            acceptedFormatsObject,
            MAX_SIZE,
            fileRef,
            'Document',
            isMultiple
        );
    }, [validateFiles]);

    const validateCsvOrExcel = useCallback((file, MAX_SIZE = 50, fileRef) => {
        const { acceptedFormatsObject } = getAcceptedAndSupportedFormats('bulk_upload');
        return validateFiles(
            [file],
            acceptedFormatsObject,
            MAX_SIZE,
            fileRef,
            'File',
            false
        );
    }, [validateFiles]);

    return { validateImage, validateDocument, validateCsvOrExcel, validateFiles };
};

export const FileUploader = forwardRef((props, ref) => {
    const {
        maxSize = 15,
        className,
        onChange,
        uploadType = uploadType ?? 'image',
        label,
        isRequired = false,
        maxFiles = 10,
        error = null,
        mode = 'single',
        onRemove
    } = props;

    const isMultipleMode = mode === 'multiple' && (uploadType === 'image' || uploadType === 'document');
    const fileInputRef = useRef(null);
    const removedFilesRef = useRef([]);
    const [files, setFiles] = useState([]);
    const [previews, setPreviews] = useState([]);
    const [fileNames, setFileNames] = useState([]);
    const [fileSizes, setFileSizes] = useState([]);
    const [fileTypes, setFileTypes] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const { validateImage, validateDocument, validateCsvOrExcel, validateFiles } = useFileValidation();
    const title = label ? ucwords(label) : '';
    const MAX_SIZE = maxSize && typeof maxSize === 'number' ? parseInt(maxSize) : 15;
    const MAX_FILES = maxFiles && typeof maxFiles === 'number' ? parseInt(maxFiles) : 10;

    const validate = useCallback((files) => {
        const isMultipleMode = mode === 'multiple' && (uploadType === 'image' || uploadType === 'document');
        const filesToValidate = Array.isArray(files) ? files : [files];

        if (uploadType === "multi_media") {
            const file = filesToValidate[0];
            const { acceptedFormatsObject } = getAcceptedAndSupportedFormats('multi_media');
            const result = validateFiles([file], acceptedFormatsObject, MAX_SIZE, fileInputRef, 'File', false);
            return { isValid: result.isValid, validFiles: result.validFiles[0] };
        } else if (uploadType === "image" || uploadType === "profile_image") {
            const result = validateImage(filesToValidate, MAX_SIZE, fileInputRef, isMultipleMode);
            return isMultipleMode ? result : { isValid: result.isValid, validFiles: result.validFiles[0] };
        } else if (uploadType === "document") {
            const result = validateDocument(filesToValidate, MAX_SIZE, fileInputRef, isMultipleMode);
            return isMultipleMode ? result : { isValid: result.isValid, validFiles: result.validFiles[0] };
        } else if (uploadType === "bulk_upload") {
            const result = validateCsvOrExcel(filesToValidate[0], MAX_SIZE, fileInputRef);
            return { isValid: result.isValid, validFiles: result.validFiles[0] };
        }
    }, [uploadType, MAX_SIZE, mode, validateImage, validateDocument, validateFiles, validateCsvOrExcel]);

    const handleFileChange = useCallback(async (selectedFiles) => {
        if (!selectedFiles || selectedFiles.length === 0) {
            ref.current?.reset();
            return true;
        }

        setIsLoading(true);

        try {
            const isMultipleMode = mode === 'multiple' && (uploadType === 'image' || uploadType === 'document');

            if (isMultipleMode) {
                const remainingSlots = MAX_FILES - files.length;

                if (remainingSlots <= 0) {
                    showToast(`Maximum ${MAX_FILES} files are allowed.`, 'warning');
                    if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                    }
                    return false;
                } else if (files.length + selectedFiles.length > MAX_FILES) {
                    showToast(`Only ${remainingSlots} more file(s) can be added. Maximum ${MAX_FILES} files are allowed.`, 'warning');
                }
            }

            let filesToProcess = Array.from(selectedFiles);
            if (isMultipleMode) {
                const remainingSlots = MAX_FILES - files.length;
                if (filesToProcess.length > remainingSlots) {
                    filesToProcess = filesToProcess.slice(0, remainingSlots);
                }
            } else {
                filesToProcess = [filesToProcess[0]];
            }

            const validationResult = validate(filesToProcess);
            if (!validationResult || !validationResult.isValid) {
                if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                }
                return false;
            }

            const validFiles = isMultipleMode
                ? (Array.isArray(validationResult.validFiles) ? validationResult.validFiles : [])
                : (validationResult.validFiles ? [validationResult.validFiles] : []);

            if (validFiles.length === 0) {
                if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                }
                return false;
            }

            const processFile = async (file) => {
                if (!file) return null;

                const currentFileType = getFileType(file);
                if (!currentFileType) return null;

                let name = file.name.replace(/\.[^/.]+$/, '');
                name = name.replace(/[^\w\s]/gi, '_').replace(/\s/g, '_');
                name = name + '.' + file.name.split('.').pop();
                const truncatedName = truncateFileName(name, uploadType === "bulk_upload" ? 100 : 50);

                let preview = null;
                if (currentFileType === 'image' && (uploadType.includes('image') || uploadType.includes('media'))) {
                    preview = await new Promise((resolve) => {
                        const reader = new FileReader();
                        reader.onload = (e) => resolve(e.target.result);
                        reader.onerror = () => resolve(null);
                        reader.readAsDataURL(file);
                    });
                } else if (currentFileType === 'document' || uploadType === "bulk_upload") {
                    preview = uploadType === "bulk_upload" ? "csv-or-excel-preview" : "document-preview";
                }

                return {
                    file,
                    preview,
                    name: truncatedName,
                    size: file.size,
                    type: currentFileType
                };
            };

            if (isMultipleMode) {
                const existingFileNames = files.map(f => f?.name || '');
                const uniqueFiles = validFiles.filter(file =>
                    file && !existingFileNames.includes(file.name)
                );

                if (uniqueFiles.length < validFiles.length) {
                    showToast('Some duplicate files were found and not considered.', 'warning');
                }

                if (uniqueFiles.length === 0) {
                    if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                    }
                    return true;
                }

                const processedFiles = await Promise.all(uniqueFiles.map(processFile));
                const validProcessedFiles = processedFiles.filter(Boolean);

                if (validProcessedFiles.length > 0) {
                    const newFiles = validProcessedFiles.map(item => item.file);
                    const newPreviews = validProcessedFiles.map(item => item.preview);
                    const newNames = validProcessedFiles.map(item => item.name);
                    const newSizes = validProcessedFiles.map(item => item.size);
                    const newTypes = validProcessedFiles.map(item => item.type);

                    setFiles(prevFiles => [...prevFiles, ...newFiles]);
                    setPreviews(prevPreviews => [...prevPreviews, ...newPreviews]);
                    setFileNames(prevNames => [...prevNames, ...newNames]);
                    setFileSizes(prevSizes => [...prevSizes, ...newSizes]);
                    setFileTypes(prevTypes => [...prevTypes, ...newTypes]);

                    if (onChange) {
                        onChange([...files, ...newFiles]);
                    }
                }
            } else {
                const processedFile = await processFile(validFiles[0]);
                if (!processedFile) {
                    if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                    }
                    return true;
                }

                setFiles([processedFile.file]);
                setPreviews([processedFile.preview]);
                setFileNames([processedFile.name]);
                setFileSizes([processedFile.size]);
                setFileTypes([processedFile.type]);

                if (onChange) {
                    onChange(processedFile.file, processedFile.type);
                }
            }

            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }

            return true;
        } finally {
            setIsLoading(false);
        }
    }, [onChange, validate, mode, uploadType, files]);

    const { isDragging, dragHandlers } = useDragAndDrop(handleFileChange, isMultipleMode);

    const isFileIsString = useCallback((filePreview) => {
        return filePreview && typeof filePreview == "string" && filePreview.includes('amazonaws.com');
    }, []);

    const handlePreventDefault = useCallback((e) => {
        e.preventDefault();
    }, []);

    const handleInputClick = useCallback((e) => {
        handlePreventDefault(e);
        fileInputRef.current?.click();
    }, [handlePreventDefault]);

    const handleFileReset = useCallback((e, index) => {
        handlePreventDefault(e);

        if (typeof index === 'number' && mode === 'multiple') {
            const fileToRemove = previews[index];

            if (isFileIsString(fileToRemove)) {
                removedFilesRef.current.push(fileToRemove);
                onRemove?.(removedFilesRef.current);
            }

            const newFiles = [...files];
            const newPreviews = [...previews];
            const newFileNames = [...fileNames];
            const newFileSizes = [...fileSizes];
            const newFileTypes = [...fileTypes];

            newFiles.splice(index, 1);
            newPreviews.splice(index, 1);
            newFileNames.splice(index, 1);
            newFileSizes.splice(index, 1);
            newFileTypes.splice(index, 1);

            setFiles(newFiles);
            setPreviews(newPreviews);
            setFileNames(newFileNames);
            setFileSizes(newFileSizes);
            setFileTypes(newFileTypes);

            onChange?.(newFiles);
        } else {
            removedFilesRef.current = [];
            ref.current?.reset();
            onChange?.(null);
        }
    }, [ref, handlePreventDefault, onChange, onRemove, mode, files, previews, fileNames, fileSizes, fileTypes, isFileIsString]);

    const handleRemoveAll = useCallback((e) => {
        handlePreventDefault(e);
        if (previews && previews.length > 0) {
            const dbFiles = previews
                .filter(file => file && isFileIsString(file));
            if (onRemove && dbFiles.length > 0) {
                onRemove(dbFiles);
            }
        }
        if (onChange) {
            onChange(null);
        }
        ref.current?.reset();
    }, [ref, handlePreventDefault, onChange]);

    const handleInputChange = useCallback((e) => {
        handleFileChange(e.target.files);
    }, [handleFileChange]);

    const handleImageLoad = useCallback((e) => {
        const img = e.target;
        img.classList.add('loaded');
        const parent = img.parentElement;
        const loaderId = parent?.dataset?.loaderId;
        if (loaderId) {
            const loader = parent.querySelector('.preview-loader');
            if (loader) {
                loader.style.opacity = '0';
                setTimeout(() => {
                    loader.style.display = 'none';
                }, 300);
            }
        }
    }, []);

    useImperativeHandle(ref, () => ({
        fileRef: fileInputRef,
        files,
        previews,
        fileNames,
        fileSizes,
        fileTypes,
        reset: () => {
            if (fileInputRef.current) {
                fileInputRef.current.value = "";
            }
            setFiles([]);
            setPreviews([]);
            setFileNames([]);
            setFileSizes([]);
            setFileTypes([]);
        },
        setFilePreview: async (filePaths) => {
            if (Array.isArray(filePaths)) {
                const newFiles = [];
                const newPreviews = [];
                const newNames = [];
                const newSizes = [];
                const newTypes = [];
                filePaths.forEach(async (path) => {
                    if (path && typeof path === 'string') {
                        const isImage = path.match(/\.(jpg|jpeg|png)$/i);
                        const filename = path.split('/').pop();
                        newFiles.push(null);
                        newPreviews.push(path);
                        newNames.push(truncateFileName(filename));
                        newSizes.push(0);
                        newTypes.push(isImage ? 'image' : 'document');
                    }
                });
                setFiles(newFiles);
                setPreviews(newPreviews);
                setFileNames(newNames);
                setFileSizes(newSizes);
                setFileTypes(newTypes);
            } else {
                const isImage = filePaths.match(/\.(jpg|jpeg|png)$/i);
                const filename = filePaths.split('/').pop();
                setFiles([null]);
                setPreviews([filePaths]);
                setFileNames([truncateFileName(filename)]);
                setFileSizes([0]);
                setFileTypes([isImage ? 'image' : 'document']);
            }
        },
        handleChange: handleFileChange
    }), [files, previews, fileNames, fileSizes, fileTypes, handleFileChange]);

    const goToViewFile = useCallback((e, fileName) => {
        handlePreventDefault(e);
        window.open(fileName, '_blank');
    }, [handlePreventDefault]);

    const { acceptedFormats, supportedFormats } = getAcceptedAndSupportedFormats(uploadType);

    const fileRequirementsHTML = () => {
        if (uploadType === "profile_image") {
            return (
                <div className="profile-file-requirements">
                    <p>
                        <i className="bi bi-file-earmark"></i>
                        <span>Supported formats: <span className="value">{supportedFormats}</span></span>
                    </p>
                    <p>
                        <i className="bi bi-hdd"></i>
                        <span>Allowed maximum file size: <span className="value">{MAX_SIZE || 15}MB</span></span>
                    </p>
                </div>
            );
        }
        return (
            <div className="file-requirements">
                <p>
                    <span className="requirement-label">
                        <i className="bi bi-file-earmark"></i>
                        Supported formats:
                    </span>
                    <span className="requirement-value">{supportedFormats}</span>
                </p>
                <p>
                    <span className="requirement-label">
                        <i className="bi bi-hdd"></i>
                        Allowed maximum file size:
                    </span>
                    <span className="requirement-value">{MAX_SIZE || 15}MB</span>
                </p>
                {mode === 'multiple' && (
                    <p>
                        <span className="requirement-label">
                            <i className="bi bi-files"></i>
                            Maximum files allowed:
                        </span>
                        <span className="requirement-value">10 files</span>
                    </p>
                )}
            </div>
        );
    }

    const previewHTML = () => {
        if (previews.length > 0 && uploadType !== "profile_image") {
            const canAddMore = mode === 'multiple' && files.length < MAX_FILES;

            return (
                <div className={`preview-content ${mode === 'multiple' ? 'preview-grid' : ''}`}>
                    {previews.map((preview, index) => (
                        <div key={index} className={`preview-item ${mode === 'multiple' ? 'preview-item-grid' : ''}`}>
                            {fileTypes[index] === 'image' ? (
                                <div className="preview-image">
                                    {preview && (
                                        <>
                                            <div
                                                className="preview-loader"
                                                style={{ display: 'flex' }}
                                                ref={(loaderEl) => {
                                                    if (loaderEl) {
                                                        loaderEl.parentElement.dataset.loaderId = `loader-${index}`;
                                                    }
                                                }}
                                            >
                                                <div className="spinner-border" role="status">
                                                    <span className="visually-hidden">Loading...</span>
                                                </div>
                                            </div>
                                            <img
                                                src={preview}
                                                alt={`Preview ${index + 1}`}
                                                onLoad={handleImageLoad}
                                                onError={handleImageLoad}
                                            />
                                            {mode === 'multiple' && (
                                                <>
                                                    <div className="image-overlay">
                                                        {isFileIsString(preview) && (
                                                            <button 
                                                                className="image-overlay-btn"
                                                                onClick={(e) => goToViewFile(e, preview)}
                                                                title={`View ${fileNames[index]}`}
                                                            >
                                                                <i className="bi bi-eye"></i>
                                                            </button>
                                                        )}
                                                    </div>
                                                </>
                                            )}
                                        </>
                                    )}
                                </div>
                            ) : (
                                <div className="preview-document">
                                    <div className="document-icon">
                                        <i className={`bi ${getFileIconClass(fileNames[index])}`}></i>
                                    </div>
                                    {mode === 'multiple' && (
                                        <>
                                            <div className="image-overlay">
                                                {preview && isFileIsString(preview) && (
                                                    <button 
                                                        className="image-overlay-btn"
                                                        onClick={(e) => goToViewFile(e, preview)}
                                                        title={`View ${fileNames[index]}`}
                                                    >
                                                        <i className="bi bi-eye"></i>
                                                    </button>
                                                )}
                                            </div>
                                        </>
                                    )}
                                </div>
                            )}

                            <div className="preview-info">
                                <div className="preview-icon">
                                    <i className={`bi ${fileTypes[index] === 'image' ? 'bi-image' : getFileIconClass(fileNames[index])}`}></i>
                                </div>
                                <div className="preview-details">
                                    <span className="preview-filename" title={fileNames[index]}>{fileNames[index]}</span>
                                    {/* {fileSizes[index] > 0 && <span className="preview-filesize">{formatBytes(fileSizes[index])}</span>} */}
                                </div>
                                <div className="d-flex align-items-center" style={{ gap: '0' }}>
                                    {preview && isFileIsString(preview) && (
                                        <button
                                            type="button"
                                            className="btn btn-sm btn-outline-primary me-2"
                                            onClick={(e) => goToViewFile(e, preview)}
                                            disabled={isLoading || !preview}
                                            title={`View ${fileTypes[index]}`}>
                                            <i className="bi bi-eye"></i>
                                        </button>
                                    )}
                                    <button
                                        type="button"
                                        className="btn btn-sm btn-outline-danger btn-hover-danger"
                                        onClick={(e) => handleFileReset(e, index)}
                                        disabled={isLoading || !preview}
                                        title={`Remove ${fileTypes[index]}`}>
                                        <i className="bi bi-x"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    ))}
                    {mode === 'multiple' && (
                        <div className="preview-grid-add-more">
                            <div className="d-flex flex-column align-items-center">
                                {canAddMore && (
                                    <button type="button" className="btn btn-primary btn-sm" onClick={handleInputClick}>
                                        <i className="bi bi-plus me-1"></i> Add More Files ({MAX_FILES - files.length} remaining)
                                    </button>
                                )}
                                {previews && previews.length > 1 && (
                                    <div className="mt-2">
                                        <button type="button" className="btn btn-outline-danger btn-sm" onClick={handleRemoveAll}>
                                            <i className="bi bi-trash me-1"></i> Remove All ({previews.length} files)
                                        </button>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>
            );
        } else if (previews.length > 0 && uploadType === "profile_image") {
            return (
                <div className="position-relative w-100 h-100">
                    <div id="preview-loader" className="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center rounded-circle">
                        <div className="spinner-border text-primary" role="status">
                            <span className="visually-hidden">Loading...</span>
                        </div>
                    </div>
                    <img
                        className="profile-preview-image img-fluid w-100 h-100 object-fit-cover"
                        style={{ display: previews[0] ? '' : 'none' }}
                        src={previews[0]}
                        alt="Preview"
                        onLoad={(e) => {
                            const loader = document.getElementById('preview-loader');
                            if (loader) {
                                loader.style.opacity = '0';
                                setTimeout(() => {
                                    loader.style.display = 'none';
                                }, 300);
                            }
                            e.target.classList.add('loaded');
                        }}
                    />
                </div>
            );
        }
        return null;
    }

    const fileUploaderHTML = () => {
        if (uploadType && uploadType !== "profile_image") {
            return (
                <>
                    <label className="form-label fw-medium mb-2">
                        {title} {isRequired && <span className="required-asterisk">*</span>}
                    </label>
                    <div className={`verification-upload-area ${className || ""} ${isDragging ? 'drag-active' : (!isDragging ? 'hoverArea' : '')}`} {...dragHandlers}>
                        <input type="file"
                            className="d-none verification-file-input"
                            ref={fileInputRef}
                            accept={acceptedFormats}
                            onChange={handleInputChange}
                            multiple={mode === 'multiple' && (uploadType === 'image' || uploadType === 'document')}
                        />

                        {isLoading && mode === 'multiple' && (
                            <div className="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style={{ background: 'rgba(255, 255, 255, 0.8)', zIndex: 1000 }}>
                                <div className="spinner-border" style={{ width: '3rem', height: '3rem' }} role="status">
                                    <span className="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        )}

                        <div className="upload-state" style={{ display: previews.length > 0 ? 'none' : 'block' }}>
                            <i className="bi bi-cloud-arrow-up"></i>
                            <h6>Drag & Drop or Browse</h6>
                            {fileRequirementsHTML()}
                            <button type="button" className="btn btn-primary" onClick={handleInputClick}>
                                <i className="bi bi-folder me-1" style={{ fontSize: '1rem', color: '#ffffff' }}></i> Choose {mode === 'multiple' ? 'Files' : 'File'}
                            </button>
                        </div>

                        <div className="preview-state" style={{ display: previews.length > 0 ? 'block' : 'none' }}>
                            {previewHTML()}
                        </div>
                    </div>
                    {error && <div className="invalid-feedback" style={{ display: 'block' }}>{error}</div>}
                </>
            )
        } else if (uploadType && uploadType === "profile_image") {
            return (
                <div className={`image-upload-preview ${className || ""}`} {...dragHandlers}>
                    <div className="mb-3">
                        <label className="preview-label">
                            <i className="bi bi-image"></i>
                            <span>{title || 'Profile Image'}</span>
                            {isRequired && <span className="required-asterisk">*</span>}
                        </label>
                    </div>
                    
                    <div className="profile-upload-area">
                        <div className="profile-preview-container">
                            <div className="profile-placeholder" style={{ display: previews[0] ? 'none' : 'flex' }}>
                                <i className="bi bi-person"></i>
                            </div>
                            {previews[0] && (
                                <>
                                    <div className="profile-image-loader" style={{ display: 'flex' }}>
                                        <div className="spinner-border" role="status">
                                            <span className="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                    <img
                                        className="profile-preview-image"
                                        src={previews[0]}
                                        alt="Profile Preview"
                                        onLoad={(e) => {
                                            const loader = e.target.previousSibling;
                                            if (loader) {
                                                loader.style.opacity = '0';
                                                setTimeout(() => {
                                                    loader.style.display = 'none';
                                                }, 300);
                                            }
                                            e.target.classList.add('loaded');
                                        }}
                                    />
                                </>
                            )}
                        </div>

                        {!previews[0] && (
                            <div className="drag-drop-browse">
                                <span>Drag & Drop Image or</span>
                                <button 
                                    type="button" 
                                    className="btn btn-primary btn-browse"
                                    onClick={handleInputClick}
                                >
                                    <i className="bi bi-folder"></i>
                                    <span>Browse</span>
                                </button>
                            </div>
                        )}

                        {previews[0] && (
                            <div className="profile-action-buttons">
                                <button 
                                    type="button" 
                                    className="btn btn-primary btn-icon"
                                    onClick={handleInputClick}
                                    title="Change Image"
                                >
                                    <i className="bi bi-pencil"></i>
                                </button>
                                <button 
                                    type="button" 
                                    className="btn btn-outline-danger btn-icon" 
                                    onClick={handleFileReset}
                                    title="Remove Image"
                                >
                                    <i className="bi bi-trash"></i>
                                </button>
                            </div>
                        )}

                        {fileRequirementsHTML()}
                    </div>

                    <input 
                        type="file"
                        className="d-none"
                        ref={fileInputRef}
                        accept={acceptedFormats}
                        onChange={handleInputChange}
                    />
                </div>
            );
        }
    }

    return fileUploaderHTML();
});

FileUploader.displayName = "FileUploader";