a {
  text-decoration: none !important;
  color: inherit;
}

@keyframes loader_text {
  0% {
    opacity: 0;
    transform: rotateY(-90deg);
  }
  50% {
    opacity: 1;
    transform: rotateY(0deg);
  }
  100% {
    opacity: 0;
    transform: rotateY(90deg);
  }
}
.preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 100px;
  font-weight: bold;
  color: #333;
  z-index: 1000;
}

.loading span {
  display: inline-block;
  margin: 0 -0.05em;
}

/* Loader Animation */
.loader_text span {
  position: relative;
  color: rgba(0, 0, 0, 0.2);
}

.loader_text span::after {
  position: absolute;
  top: 0;
  left: 0;
  content: attr(data-text);
  color: #7d5be3;
  opacity: 0;
  transform: rotateY(-90deg);
  animation: loader_text 4s infinite;
}

.loader_text span:nth-child(2)::after {
  animation-delay: 0.2s;
}
.loader_text span:nth-child(3)::after {
  animation-delay: 0.4s;
}
.loader_text span:nth-child(4)::after {
  animation-delay: 0.6s;
}
.loader_text span:nth-child(5)::after {
  animation-delay: 0.8s;
}
.loader_text span:nth-child(6)::after {
  animation-delay: 1s;
}
.loader_text span:nth-child(7)::after {
  animation-delay: 1.2s;
}
.loader_text span:nth-child(8)::after {
  animation-delay: 1.4s;
}
.loader_text span:nth-child(9)::after {
  animation-delay: 1.6s;
}
.loader_text span:nth-child(10)::after {
  animation-delay: 1.8s;
}

/* my added css */
.loader-overlay {
  position: fixed;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  z-index: 9999999999;
  background: url("/loader.gif") 50% 50% no-repeat rgb(249, 249, 249);
  opacity: 0.5 !important;
}

.error-message {
  color: #e74c3c; /* Slightly brighter red for better visibility */
  font-size: 14px; /* Increased font size for better readability */
  margin-top: 2px; /* Increased margin for better spacing */
  font-weight: 600; /* Bolder font weight for emphasis */
}

.input-group .form-control.error,
.input-group .input-group-text.error,
.input-group .form-select.error,
.input-group .input-group-text .form-select.error,
.input-group .form-control.error:focus,
.input-group .input-group-text.error:focus,
.input-group .form-select.error:focus,
.input-group .input-group-text .form-select.error:focus {
  border-color: #dc3545 !important;
  transition: all 0.3s ease !important;
}

.input-group .input-group-text.error i {
  color: #dc3545 !important;
}

.input-group .form-control.error:hover,
.input-group .input-group-text.error:hover,
.input-group .form-select.error:hover,
.input-group .input-group-text .form-select.error:hover {
  border-color: #dc3545 !important;
}

.input-group .form-control.error:focus,
.input-group .input-group-text.error:focus,
.input-group .form-select.error:focus,
.input-group .input-group-text .form-select.error:focus {
  border-color: #dc3545 !important;
  outline: none;
}

.swal2-container {
  z-index: 1234567890 !important;
}

#_rht_toaster > div > div:nth-child(1) {
  max-width: 500px !important;
}

/* Required Field Indicator */
.required-field::after {
  content: "*";
  color: var(--danger-color);
  margin-left: 4px;
}

/* DataTable Styles Enhancement */
.rdt_Table {
  font-size: 0.95rem;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 6px 30px rgba(125, 91, 227, 0.1);
  background: #fff;
  border: 1px solid rgba(125, 91, 227, 0.08);
}

.rdt_TableHeader {
  background: linear-gradient(
    135deg,
    rgb(92, 68, 184) 0%,
    rgb(61, 43, 148) 100%
  );
  padding: 1.25rem;
  font-weight: 600;
  font-size: 1.1rem;
  color: #ffffff;
  position: relative;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  border-radius: 16px 16px 0 0;
  display: flex;
  align-items: center;
}

.rdt_TableHeader::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
}

.rdt_TableHeadRow {
  background: linear-gradient(
    135deg,
    rgb(92, 68, 184) 0%,
    rgb(61, 43, 148) 100%
  );
  border-bottom: none;
  font-weight: 600;
  color: #ffffff;
  min-height: 48px;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 1px;
  padding: 0.5rem 0;
}

.rdt_TableHeadRow .rdt_TableCol {
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  padding: 12px 16px;
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
}

.rdt_TableHeadRow .rdt_TableCol:hover {
  background: rgba(255, 255, 255, 0.12);
  color: #ffffff;
  backdrop-filter: blur(4px);
  transform: translateY(-1px);
}

.rdt_TableHeadRow .rdt_TableCol:active {
  transform: translateY(0);
  background: rgba(255, 255, 255, 0.18);
}

/* Sort icon colors */
.rdt_TableCol_Sortable {
  display: flex !important;
  align-items: center;
  gap: 6px;
}

.rdt_TableCol_Sortable svg {
  fill: rgba(255, 255, 255, 0.8);
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  width: 16px;
  height: 16px;
  opacity: 0.8;
}

.rdt_TableCol_Sortable:hover svg {
  fill: #ffffff;
  opacity: 1;
  transform: translateY(-1px);
}

.rdt_TableCol_Sortable span {
  margin-left: 4px;
}

.rdt_TableRow {
  border-bottom: 1px solid rgba(125, 91, 227, 0.08);
  min-height: 56px;
  font-size: 0.95rem;
  position: relative;
  overflow: hidden;
  background: #ffffff;
}

.rdt_TableRow:nth-child(odd) {
  background: #ffffff;
  border-bottom: 1px solid rgba(125, 91, 227, 0.08);
}

.rdt_TableRow:nth-child(even) {
  background: rgba(125, 91, 227, 0.15);
  border-bottom: 1px solid rgba(125, 91, 227, 0.12);
}

/* Add back the vertical line */
.rdt_TableRow::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  height: 100%;
  background: linear-gradient(to bottom, #7d5be3, #6347c9);
  opacity: 0;
}

.rdt_TableRow:hover {
  background: rgba(125, 91, 227, 0.25);
}

.rdt_TableRow:hover::before {
  opacity: 1;
  width: 4px;
}

.rdt_TableCell {
  padding: 1.15rem;
  font-size: 0.95rem;
  color: #464255;
  font-weight: 500;
  line-height: 1.5;
}

.rdt_TableRow:hover .rdt_TableCell {
  color: #6347c9;
  font-weight: 600;
}

/* Remove all animations and transitions except for the vertical line */
.rdt_TableRow,
.rdt_TableRow:hover,
.rdt_TableCell,
.rdt_TableRow:hover .rdt_TableCell {
  transition: none !important;
  transform: none !important;
  animation: none !important;
  box-shadow: none !important;
}

/* Remove scale and transform effects */
.rdt_TableRow:hover,
.rdt_TableRow:active {
  transform: none !important;
  box-shadow: none !important;
}

/* DataTable Buttons */
.rdt_Table .btn {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  border-radius: 10px;
  min-width: 38px;
  height: 38px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin: 0 4px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.rdt_Table .btn::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.rdt_Table .btn:hover::after {
  opacity: 1;
}

.rdt_Table .btn i {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.rdt_Table .btn:hover i {
  transform: scale(1.1);
}

/* DataTable Action Buttons */
.rdt_Table .btn-info {
  background: rgba(125, 91, 227, 0.08);
  color: #6347c9;
  border: 1px solid rgba(125, 91, 227, 0.15);
  backdrop-filter: blur(4px);
}

.rdt_Table .btn-primary {
  background: rgba(125, 91, 227, 0.08);
  color: #6347c9;
  border: 1px solid rgba(125, 91, 227, 0.15);
  backdrop-filter: blur(4px);
}

.rdt_Table .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(125, 91, 227, 0.15);
}

.rdt_Table .btn-info:hover,
.rdt_Table .btn-primary:hover {
  background: linear-gradient(135deg, #7d5be3, #6347c9);
  color: #fff;
  border-color: transparent;
}

/* DataTable Pagination */
.rdt_Pagination {
  background: #ffffff;
  padding: 1rem 1.25rem;
  border-top: 1px solid rgba(125, 91, 227, 0.1);
  display: flex;
  align-items: center;
  border-radius: 0 0 16px 16px;
  gap: 8px;
}

/* Rows per page dropdown */
.rdt_Pagination select {
  padding: 0.5rem 1.75rem 0.5rem 0.75rem;
  font-size: 0.9rem;
  border-radius: 8px;
  border: 1px solid rgba(125, 91, 227, 0.2);
  color: rgb(92, 68, 184);
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  background: #fff;
  cursor: pointer;
  font-weight: 500;
  position: relative;
}

/* Remove default dropdown arrow in IE */
.rdt_Pagination select::-ms-expand {
  display: none;
}

/* Firefox specific styling */
.rdt_Pagination select {
  text-indent: 1px;
  text-overflow: "";
}

/* Pagination container */
.rdt_Pagination {
  background: #ffffff;
  padding: 1rem 1.25rem;
  border-top: 1px solid rgba(125, 91, 227, 0.1);
  display: flex;
  align-items: center;
  border-radius: 0 0 16px 16px;
  gap: 8px;
}

/* Rows per page container */
.rdt_Pagination_rowsPerPage {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 16px;
  white-space: nowrap;
  position: relative;
}

.rdt_Pagination_rowsPerPage span {
  color: #464255;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Select container to prevent width changes */
.rdt_Pagination_rowsPerPage select {
  width: 70px;
  text-align: center;
  padding-left: 0.5rem;
  padding-right: 1.5rem;
}

.jJMfvT svg {
  top: -4px !important;
  right: 5px !important;
  bottom: 0 !important;
  margin: auto !important;
  color: rgb(92, 68, 184) !important;
  position: absolute !important;
  fill: currentColor !important;
  width: 24px !important;
  height: 24px !important;
  display: inline-block !important;
  user-select: none !important;
  pointer-events: none !important;
}

/* Remove custom arrow styles */
.rdt_Pagination_rowsPerPage::after {
  display: none;
}

/* Range text styling */
.rdt_Pagination_rowCount {
  color: #464255;
  font-size: 0.9rem;
  font-weight: 500;
  margin: 0 16px;
  white-space: nowrap;
}

/* Navigation buttons */
.rdt_Pagination button {
  padding: 0.5rem;
  min-width: 36px;
  height: 36px;
  margin: 0 2px;
  background: #fff;
  border: 1px solid rgba(125, 91, 227, 0.2);
  border-radius: 8px;
  color: rgb(92, 68, 184);
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.rdt_Pagination button:hover:not([disabled]) {
  background: rgba(125, 91, 227, 0.04);
  border-color: rgb(92, 68, 184);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(125, 91, 227, 0.1);
}

.rdt_Pagination button:active:not([disabled]) {
  transform: translateY(0);
  background: rgba(125, 91, 227, 0.08);
}

.rdt_Pagination button[disabled] {
  background: #f8f9fc;
  border-color: #ecedf3;
  color: #c5c7d0;
  cursor: not-allowed;
}

/* Navigation icons */
.rdt_Pagination button svg {
  width: 16px;
  height: 16px;
  stroke: currentColor;
  stroke-width: 2;
}

/* Current page button */
.rdt_Pagination button.current-page {
  background: linear-gradient(
    135deg,
    rgb(92, 68, 184) 0%,
    rgb(61, 43, 148) 100%
  );
  color: white;
  border-color: transparent;
  font-weight: 600;
}

.rdt_Pagination button.current-page:hover {
  transform: none;
  box-shadow: 0 2px 8px rgba(125, 91, 227, 0.2);
}

/* Pagination text */
.rdt_Pagination span {
  color: #464255;
  font-size: 0.9rem;
  margin: 0 8px;
}

/* First/Last page buttons */
.rdt_Pagination button:first-child,
.rdt_Pagination button:last-child {
  font-size: 1.1rem;
  padding: 0;
}

/* Previous/Next icons */
.rdt_Pagination button svg {
  width: 18px;
  height: 18px;
  stroke: currentColor;
  stroke-width: 2;
  transition: transform 0.25s ease;
}

.rdt_Pagination button:hover:not([disabled]) svg {
  transform: scale(1.1);
}

/* Rows per page label */
.rdt_Pagination_rowsPerPage {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 16px;
}

.rdt_Pagination_rowsPerPage span {
  color: #464255;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Total rows count */
.rdt_Pagination_rowCount {
  margin-left: 16px;
  color: #464255;
  font-size: 0.9rem;
  font-weight: 500;
}

/* DataTable Search */
.search-box {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.search-box label {
  color: #6347c9;
  font-weight: 600;
  margin-bottom: 0;
  font-size: 0.95rem;
}

.search-box .form-control {
  padding: 0.75rem 1.25rem;
  font-size: 0.95rem;
  border: 1px solid rgba(125, 91, 227, 0.15);
  border-radius: 12px;
  width: 300px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: #fff;
  color: #555;
}

.search-box .form-control:focus {
  border-color: #7d5be3;
  box-shadow: 0 0 0 3px rgba(125, 91, 227, 0.1);
  width: 320px;
  outline: none;
}

.search-box .form-control::placeholder {
  color: #b7b9cc;
  font-weight: 400;
}

/* Loading Spinner */
.rdt_Table .spinner-border.text-primary {
  color: #7d5be3 !important;
  width: 3.5rem !important;
  height: 3.5rem !important;
  border-width: 0.25rem;
  opacity: 0.7;
}

/* Table Header Text */
#page-title {
  color: #6347c9;
  font-weight: 700;
  font-size: 1.75rem;
  margin-bottom: 1.75rem;
  letter-spacing: -0.5px;
}

/* Card Styling */
.card {
  border-radius: 20px;
  border: none;
  background: #ffffff;
  box-shadow: 0 8px 30px rgba(125, 91, 227, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 12px 40px rgba(125, 91, 227, 0.15);
}

.card-body {
  padding: 2rem;
}

/* Table Empty State */
.rdt_Table .no-data {
  padding: 3rem;
  text-align: center;
  color: #6c757d;
  font-size: 1rem;
  background: rgba(125, 91, 227, 0.05);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .card-body {
    padding: 1.25rem;
  }

  .search-box .form-control {
    width: 100%;
    max-width: 250px;
  }

  .search-box .form-control:focus {
    width: 100%;
    max-width: 280px;
  }

  .rdt_TableCell {
    padding: 1rem 0.75rem;
  }
}

/* Add subtle animation for row entrance */
@keyframes tableRowFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.rdt_TableBody {
  position: relative;
  background: linear-gradient(to right, #ffffff, rgba(125, 91, 227, 0.01));
}

/* Add subtle separator between rows */
.rdt_TableRow:not(:last-child) {
  border-bottom: 1px solid rgba(125, 91, 227, 0.08);
}

/* Enhance active state */
.rdt_TableRow:active {
  transform: translateY(1px) scale(0.995);
  transition: transform 0.1s ease;
}

/* Status column styling if exists */
.rdt_TableCell[data-column-id="status"] {
  font-weight: 600;
  text-transform: capitalize;
  position: relative;
  padding-left: 1.5rem;
}

.rdt_TableCell[data-column-id="status"]::before {
  background: rgba(125, 91, 227, 0.3);
}

/* Add hover effect to the entire row content */
.rdt_TableRow:hover .rdt_TableCell {
  transform: translateX(4px);
}

/* Improve table header appearance */
.rdt_TableHeader::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    to right,
    rgba(125, 91, 227, 0.3),
    rgba(125, 91, 227, 0.2),
    rgba(125, 91, 227, 0.3)
  );
}

/* Add subtle transition when table updates */
.rdt_Table {
  transition: opacity 0.2s ease;
}

.rdt_Table.loading {
  opacity: 0.7;
}

/* Add subtle animation to header hover */
@keyframes headerHover {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.rdt_TableHeadRow .rdt_TableCol:hover {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  background-size: 200% 200%;
  animation: headerHover 3s ease infinite;
}

/* Improve header contrast */
.rdt_TableHeadRow {
  background: linear-gradient(
    135deg,
    rgb(92, 68, 184) 0%,
    rgb(61, 43, 148) 100%
  );
  border-bottom: none;
  font-weight: 600;
  color: #ffffff;
  min-height: 48px;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 1px;
  padding: 0.5rem 0;
}

.rdt_TableHeadRow .rdt_TableCol {
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  padding: 12px 16px;
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
}

.rdt_TableHeadRow .rdt_TableCol:hover {
  background: rgba(255, 255, 255, 0.12);
  color: #ffffff;
  backdrop-filter: blur(4px);
  transform: translateY(-1px);
}

.rdt_TableHeadRow .rdt_TableCol:active {
  transform: translateY(0);
  background: rgba(255, 255, 255, 0.18);
}

/* Sort icon colors */
.rdt_TableCol_Sortable {
  display: flex !important;
  align-items: center;
  gap: 6px;
}

.rdt_TableCol_Sortable svg {
  fill: rgba(255, 255, 255, 0.8);
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  width: 16px;
  height: 16px;
  opacity: 0.8;
}

.rdt_TableCol_Sortable:hover svg {
  fill: #ffffff;
  opacity: 1;
  transform: translateY(-1px);
}

.rdt_TableCol_Sortable span {
  margin-left: 4px;
}

/* Remove the previous hover animations */
.rdt_TableHeadRow .rdt_TableCol::after {
  display: none;
}

@keyframes headerHover {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Sorted column style */
.rdt_TableCol_Sortable.sortActive {
  color: #ffffff;
  font-weight: 700;
  background: rgba(255, 255, 255, 0.15);
}

.rdt_TableCol_Sortable.sortActive svg {
  fill: #ffffff;
  opacity: 1;
}

/* Add subtle box shadow to header */
.rdt_TableHeadRow {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 2;
}

/* Common Section Styles - Updated */
.verification-upload-area {
  position: relative;
  width: 100%;
  min-height: 260px;
  border: 2px dashed rgba(125, 91, 227, 0.2);
  border-radius: 12px;
  background: #fff;
  transition: all 0.3s ease;
  overflow: hidden;
  padding: 0.75rem;
}

.verification-upload-area.drag-active {
  border-color: #7d5be3;
  background-color: rgba(125, 91, 227, 0.03);
}

.verification-upload-area.hoverArea:hover {
  border-color: #7d5be3;
}

/* Preview State Styles - Updated */
.preview-state {
  background-color: #fff;
  border-radius: 8px;
}

/* Preview Content Container - Updated */
.preview-content-container {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 0.625rem;
  background: rgba(125, 91, 227, 0.03);
  border-radius: 8px;
  margin-bottom: 0.5rem;
}

/* Preview Item Styles - Updated */
.preview-item {
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

/* Image/Document Preview Styles - Updated */
.preview-image,
.preview-document {
  position: relative;
  width: 100%;
  height: 160px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(125, 91, 227, 0.1);
  box-shadow: 0 2px 8px rgba(125, 91, 227, 0.05);
  background: #fff;
}

/* Document Icon Styles - Updated */
.document-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.document-icon i {
  font-size: 3rem;
  color: #7d5be3;
}

/* Image Preview Styles - Updated */
.preview-image {
  position: relative;
  width: 100%;
  height: 180px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(125, 91, 227, 0.1);
  box-shadow: 0 2px 8px rgba(125, 91, 227, 0.05);
  background: #fff;
}

.preview-loader {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
}

.preview-loader .spinner-border {
  width: 2.5rem !important;
  height: 2.5rem !important;
  border-width: 0.2rem !important;
  color: #7d5be3 !important;
}

.preview-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.3s ease;
  position: relative;
  z-index: 1;
}

.preview-image img.loaded {
  opacity: 1;
}

/* Preview Info Styles - Updated */
.preview-info {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 0.625rem;
  border-top: 1px solid rgba(125, 91, 227, 0.1);
  margin-top: 0.375rem;
  background: #fff;
  border-radius: 0 0 8px 8px;
  gap: 0.625rem;
}

.preview-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  width: 32px;
  height: 32px;
  background: rgba(125, 91, 227, 0.08);
  border-radius: 6px;
  color: #7d5be3;
}

.preview-icon i {
  font-size: 1.1rem;
}

.preview-details {
  flex: 1;
  min-width: 0;
}

.preview-filename {
  display: block;
  font-weight: 500;
  color: #464255;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  font-size: 0.925rem;
}

/* Action Buttons - Updated */
.preview-info .btn-outline-primary,
.preview-info .btn-outline-danger {
  padding: 0.375rem;
  width: 32px;
  height: 32px;
  min-width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  margin: 0;
  background: transparent;
  transition: all 0.2s ease;
}

.preview-info .btn-outline-primary {
  border: 1.5px solid rgba(125, 91, 227, 0.4);
  color: #7d5be3;
}

.preview-info .btn-outline-danger {
  border: 1.5px solid rgba(220, 53, 69, 0.4);
  color: #dc3545;
  background: rgba(220, 53, 69, 0.04);
}

.preview-info .btn-outline-primary:hover {
  background: #7d5be3;
  color: #fff;
  border-color: #7d5be3;
  transform: translateY(-1px);
}

.preview-info .btn-outline-danger:hover {
  background: #dc3545;
  color: #fff;
  border-color: #dc3545;
  transform: translateY(-1px);
}

.preview-info .btn i {
  font-size: 1rem;
  margin: 0;
}

/* Upload State Styles - Updated */
.upload-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  text-align: center;
  height: 100%;
  min-height: inherit;
  gap: 0.75rem;
}

.upload-state i.bi-cloud-arrow-up {
  font-size: 2.25rem;
  color: #7d5be3;
  opacity: 0.8;
  margin-bottom: 0;
}

.upload-state h6 {
  font-size: 1rem;
  color: #464255;
  margin-bottom: 0;
  font-weight: 500;
}

/* File Requirements Section - Updated */
.file-requirements {
  margin: 0.5rem 0;
  padding: 0.625rem;
  border-radius: 8px;
  background-color: rgba(125, 91, 227, 0.03);
  border: 1px solid rgba(125, 91, 227, 0.1);
}

.file-requirements p {
  font-size: 0.875rem;
  color: #464255;
  margin-bottom: 0.375rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  line-height: 1.4;
}

.file-requirements p:last-child {
  margin-bottom: 0;
}

.file-requirements p .requirement-label {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  white-space: nowrap;
  color: #7d5be3;
  font-weight: 500;
  min-width: fit-content;
}

.file-requirements p .requirement-label i {
  font-size: 1rem;
  color: #7d5be3;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
}

.file-requirements p .requirement-value {
  color: #464255;
  font-weight: normal;
  display: flex;
  align-items: center;
  min-height: 1.5rem;
  padding-top: 1px;
}

/* Upload State Button - Updated */
.upload-state .btn {
  margin-top: 0.25rem;
  padding: 0.5rem 1.25rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.upload-state .btn i {
  font-size: 1.1rem;
}

/* Responsive Adjustments */
@media (max-width: 576px) {
  .file-requirements p {
    font-size: 0.8125rem;
    gap: 0.375rem;
  }

  .file-requirements p .requirement-label i {
    font-size: 0.9375rem;
  }
}

/* Profile Image Upload Styles */
.image-upload-preview {
  background: #fff;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  border: 2px dashed rgba(125, 91, 227, 0.2);
  transition: all 0.3s ease;
}

.image-upload-preview:hover {
  border-color: rgba(125, 91, 227, 0.4);
}

.image-upload-preview.drag-active {
  border-color: #7d5be3;
  background-color: rgba(125, 91, 227, 0.03);
}

.preview-label {
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 0;
}

.preview-label i {
  font-size: 1.25rem;
  color: #7d5be3;
}

/* Profile Upload Area */
.profile-upload-area {
    width: 100%;
    max-width: 280px;
    margin: 0 auto;
    position: relative;
}

/* Profile Image Preview Container */
.profile-preview-container {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    position: relative;
    margin: 0 auto 1rem;
    background: rgba(125, 91, 227, 0.03);
    border: 2px dashed rgba(125, 91, 227, 0.2);
    overflow: hidden;
    transition: all 0.3s ease;
}

.profile-preview-container:hover {
    border-color: rgba(125, 91, 227, 0.4);
}

/* File Requirements for Profile */
.profile-file-requirements {
    margin-top: 1rem;
    width: 100%;
    max-width: 280px;
    margin-left: auto;
    margin-right: auto;
}

.profile-file-requirements p {
    font-size: 0.8125rem;
    color: #7d5be3;
    margin-bottom: 0.375rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
    font-weight: 500;
}

.profile-file-requirements p:last-child {
    margin-bottom: 0;
}

.profile-file-requirements p i {
    color: #7d5be3;
    font-size: 0.875rem;
    min-width: 16px;
}

.profile-file-requirements .value {
    color: #464255;
    font-weight: normal;
}

/* Profile Action Buttons Container */
.profile-action-buttons {
  position: absolute;
  right: -50px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.profile-action-buttons .btn {
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: #fff;
  border: 1.5px solid rgba(125, 91, 227, 0.4);
  color: #7d5be3;
  transition: all 0.2s ease;
}

.profile-action-buttons .btn.btn-browse {
  width: auto;
  padding: 0 12px;
  gap: 6px;
}

.profile-action-buttons .btn.btn-icon {
  width: 36px;
  height: 36px;
}

.profile-action-buttons .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(125, 91, 227, 0.15);
}

.profile-action-buttons .btn-primary {
  background: rgba(125, 91, 227, 0.1);
}

.profile-action-buttons .btn-primary:hover {
  background: #7d5be3;
  color: #fff;
}

.profile-action-buttons .btn-outline-danger {
  border-color: rgba(220, 53, 69, 0.4);
  color: #dc3545;
  background: rgba(220, 53, 69, 0.04);
}

.profile-action-buttons .btn-outline-danger:hover {
  background: #dc3545;
  color: #fff;
  border-color: #dc3545;
}

.profile-action-buttons .btn i {
  font-size: 1rem;
}

.profile-action-buttons .btn span {
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
}

.profile-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(125, 91, 227, 0.5);
}

.profile-placeholder i {
  font-size: 3rem !important;
  opacity: 0.5;
}

/* Profile Image Preview */
.profile-preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.profile-preview-image.loaded {
  opacity: 1;
}

/* Profile Upload Buttons */
.profile-upload-buttons {
  display: none; /* Hide the original buttons */
}

/* Profile Image Loader */
.profile-image-loader {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  z-index: 2;
}

.profile-image-loader .spinner-border {
  width: 2rem !important;
  height: 2rem !important;
  border-width: 0.2rem !important;
  color: #7d5be3 !important;
}

/* Drag & Drop Text */
.drag-drop-text {
  font-size: 0.875rem;
  color: #6c757d;
  margin: 0.5rem 0;
}

.drag-drop-text.separator {
  position: relative;
  text-align: center;
  margin: 0.75rem 0;
}

/* Drag & Drop with Browse Button */
.drag-drop-browse {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin: 1rem 0;
}

.drag-drop-browse span {
    color: #6c757d;
    font-size: 0.875rem;
}

.drag-drop-browse .btn-browse {
    min-height: 36px;
    min-width: 105px;
    padding: 0.375rem 0.875rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    border-radius: 8px;
    background: rgba(125, 91, 227, 0.1);
    border: 1.5px solid rgba(125, 91, 227, 0.4);
    color: #7d5be3;
    transition: all 0.2s ease;
    white-space: nowrap;
    line-height: 1;
}

.drag-drop-browse .btn-browse:hover {
    background: #7d5be3;
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 4px 10px rgba(125, 91, 227, 0.15);
}

.drag-drop-browse .btn-browse i {
    font-size: 1rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-top: -2px;
}

.drag-drop-browse .btn-browse span {
    color: inherit;
    font-size: 0.875rem;
    font-weight: 500;
    display: inline-block;
    line-height: 1.2;
}

@media (max-width: 576px) {
    .drag-drop-browse {
        flex-wrap: wrap;
        text-align: center;
        gap: 6px;
    }
    
    .drag-drop-browse span {
        font-size: 0.8125rem;
    }
    
    .drag-drop-browse .btn-browse {
        min-height: 34px;
        padding: 0.325rem 0.75rem;
    }
    
    .drag-drop-browse .btn-browse span {
        font-size: 0.8125rem;
    }
}

/* Responsive Adjustments */
@media (max-width: 576px) {
    .profile-preview-container {
        width: 120px;
        height: 120px;
    }
    
    .profile-placeholder i {
        font-size: 2.5rem !important;
    }
    
    .profile-action-buttons {
        right: -45px;
    }
    
    .profile-action-buttons .btn.btn-icon {
        width: 32px;
        height: 32px;
    }
    
    .profile-action-buttons .btn.btn-browse {
        padding: 0 10px;
        height: 32px;
    }
    
    .profile-action-buttons .btn span {
        font-size: 0.8125rem;
    }
    
    .profile-file-requirements {
        padding: 0.625rem;
    }
    
    .profile-file-requirements p {
        font-size: 0.75rem;
        gap: 0.375rem;
    }
    
    .profile-file-requirements p i {
        font-size: 0.8125rem;
    }
}

/* Responsive Adjustments for File Requirements */
@media (max-width: 576px) {
    .profile-upload-area {
        max-width: 240px;
    }
    
    .profile-file-requirements {
        padding: 0.625rem 0.875rem;
        max-width: 240px;
    }
    
    .profile-file-requirements p {
        font-size: 0.75rem;
        gap: 0.375rem;
        margin-bottom: 0.25rem;
    }
    
    .profile-file-requirements p i {
        font-size: 0.8125rem;
        min-width: 14px;
    }
}
