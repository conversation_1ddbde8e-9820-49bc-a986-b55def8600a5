import { StockController } from '@/sql/controllers';
import { sendResponse } from '@/utils/helper';
import messages from '@/utils/messages';

export async function GET(request) {
    try {
        const searchParams = new URLSearchParams(new URL(request.url).search);
        const action = searchParams.get('action');
        switch (action) {
            case 'getList':
                return await StockController.getList(request);
            case 'getIdeaDetails':
                return await StockController.getStockDetails(request);
            default:
                return sendResponse({ status: 400, message: messages.INVALID_ACTION });
        }
    } catch (error) {
        return sendResponse({
            status: 500,
            message: messages.UNEXPECTED_ERROR || error.message,
        });
    }
}

export async function POST(request) {
    try {
        const searchParams = new URLSearchParams(new URL(request.url).search);
        const action = searchParams.get('action');
        switch (action) {
            case 'updateStockStatus':
                return await StockController.updateStockStatus(request);
            case 'syncStocks':
                return await StockController.syncStocks(request);
            default:
                return sendResponse({ status: 400, message: messages.INVALID_ACTION });
        }
    } catch (error) {
        return sendResponse({
            status: 500,
            message: messages.UNEXPECTED_ERROR || error.message,
        });
    }
}

export async function PUT(request) {
    return POST(request);
}

export async function DELETE(request) {
    return POST(request);
}

export async function PATCH(request) {
    return POST(request);
}