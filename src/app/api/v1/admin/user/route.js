import { User<PERSON>ontroller } from '@/sql/controllers';
import { sendResponse } from '@/utils/helper';
import messages from '@/utils/messages';

export async function GET(request) {
    try {
        const searchParams = new URLSearchParams(new URL(request.url).search);
        const action = searchParams.get('action');
        switch (action) {
            case 'getList':
                return await UserController.getList(request);
            case 'getUserDetails':
                return await UserController.getUserDetails(request);
            default:
                return sendResponse({ status: 400, message: messages.INVALID_ACTION });
        }
    } catch (error) {
        return sendResponse({
            status: 500,
            message: messages.UNEXPECTED_ERROR || error.message,
        });
    }
}

export async function POST(request) {
    try {
        const searchParams = new URLSearchParams(new URL(request.url).search);
        const action = searchParams.get('action');
        switch (action) {
            case 'updateUserDetails':
                return await User<PERSON>ontroller.updateUserDetails(request);
            case 'updateUserStatus':
                return await UserController.updateUserStatus(request);
            case 'deleteUser':
                return await UserController.deleteUser(request);
            default:
                return sendResponse({ status: 400, message: messages.INVALID_ACTION });
        }
    } catch (error) {
        return sendResponse({
            status: 500,
            message: messages.UNEXPECTED_ERROR || error.message,
        });
    }
}

export async function PUT(request) {
    return POST(request);
}

export async function DELETE(request) {
    return POST(request);
}

export async function PATCH(request) {
    return POST(request);
}