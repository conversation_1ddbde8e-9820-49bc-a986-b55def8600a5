import { <PERSON>dea<PERSON><PERSON>roller } from '@/sql/controllers';
import { sendResponse } from '@/utils/helper';
import messages from '@/utils/messages';

export async function GET(request) {
    try {
        const searchParams = new URLSearchParams(new URL(request.url).search);
        const action = searchParams.get('action');
        switch (action) {
            case 'getList':
                return await IdeaController.getList(request);
            case 'getIdeaDetails':
                return await IdeaController.getIdeaDetails(request);
            default:
                return sendResponse({ status: 400, message: messages.INVALID_ACTION });
        }
    } catch (error) {
        return sendResponse({
            status: 500,
            message: messages.UNEXPECTED_ERROR || error.message,
        });
    }
}

export async function POST(request) {
    try {
        const searchParams = new URLSearchParams(new URL(request.url).search);
        const action = searchParams.get('action');
        switch (action) {
            case 'updateIdeaStatus':
                return await <PERSON>dea<PERSON><PERSON>roller.updateIdeaStatus(request);
            case 'deleteIdea':
                return await IdeaController.deleteIdea(request);
            default:
                return sendResponse({ status: 400, message: messages.INVALID_ACTION });
        }
    } catch (error) {
        return sendResponse({
            status: 500,
            message: messages.UNEXPECTED_ERROR || error.message,
        });
    }
}

export async function PUT(request) {
    return POST(request);
}

export async function DELETE(request) {
    return POST(request);
}

export async function PATCH(request) {
    return POST(request);
}