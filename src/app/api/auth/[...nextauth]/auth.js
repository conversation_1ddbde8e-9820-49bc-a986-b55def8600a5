import CredentialsProvider from 'next-auth/providers/credentials';
import { comparePassword } from '@/utils/helper';
import jwt from 'jsonwebtoken';
import messages from '@/utils/messages';
import models from '@/sql/models';

const { users } = models;

export const authOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
        otp: { label: "OTP", type: "text" }
      },
      async authorize(credentials) {
        try {
          const user = await users.findOne({
            where: {
              email: credentials.email,
              roleId: 1
            },
            attributes: ['id', 'email', 'password', 'roleId']
          });
          if (!user) throw new Error(messages.USER_NOT_FOUND);

          const isValid = await comparePassword(credentials.password, user.password);
          if (!isValid) throw new Error(messages.INVALID_PASSWORD);

          if (!credentials.otp) {
            return {
              id: user.id,
              email: user.email,
              roleId: user.roleId,
              otpPending: true
            };
          }

          if (credentials.otp !== '123456') throw new Error('Invalid OTP');

          const accessToken = jwt.sign(
            {
              id: user.id,
              email: user.email,
              roleId: user.roleId,
              otpPending: false
            },
            process.env.NEXTAUTH_SECRET,
            { expiresIn: '30d' }
          );

          return {
            id: user.id,
            email: user.email,
            roleId: user.roleId,
            accessToken,
            otpPending: false
          };
        } catch (error) {
          throw new Error(error.message || 'Login failed');
        }
      }
    })
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.email = user.email;
        token.roleId = user.roleId;
        token.accessToken = user.accessToken || null;
        token.otpPending = user.otpPending || false;
      }
      return token;
    },
    async session({ session, token }) {
      session.user = {
        ...session.user,
        id: token.id,
        roleId: token.roleId,
        accessToken: token.accessToken || null,
        otpPending: token.otpPending || false
      };
      return session;
    }
  },
  pages: {
    signIn: '/auth/login',
    error: '/auth/error'
  },
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60,
  },
  secret: process.env.NEXTAUTH_SECRET,
  debug: process.env.NEXT_PUBLIC_APP_ENV !== 'live'
};