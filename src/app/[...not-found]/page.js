'use client';
import { useRouter, usePathname } from "next/navigation";
export default function NotFoundPage() {
  const router = useRouter();
  const pathname = usePathname();
  if (pathname !== '/') {
    return (
      <div className="flex-container">
        <div className="text-center">
          <h1>
            <span className="fade-in" id="digit1">4</span>
            <span className="fade-in" id="digit2">0</span>
            <span className="fade-in" id="digit3">4</span>
          </h1>
          <h3 className="fade-in">PAGE NOT FOUND</h3>
          <button onClick={() => router.push("/")}>Return To Home</button>
        </div>

        <style jsx>{`
          * {
            font-family: Google sans, Arial;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          .flex-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            width: 100%;
            color: white;
            animation: colorSlide 15s cubic-bezier(0.075, 0.82, 0.165, 1) infinite;
            padding: 20px;
            overflow: hidden;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
          }

          .text-center {
            text-align: center;
            max-width: 90%;
          }

          h1, h3 {
            margin: 15px 0;
            cursor: default;
          }

          .fade-in {
            animation: fadeIn 2s ease infinite;
          }

          h1 {
            font-size: 8em;
            transition: font-size 200ms ease-in-out;
            border-bottom: 1px dashed white;
            padding-bottom: 10px;
          }

          h3 {
            font-size: 1.5em;
            letter-spacing: 2px;
          }

          #digit1 { animation-delay: 200ms; }
          #digit2 { animation-delay: 300ms; }
          #digit3 { animation-delay: 400ms; }

          button {
            border: 1px solid white;
            background: transparent;
            outline: none;
            padding: 12px 24px;
            font-size: 1.1rem;
            font-weight: bold;
            color: white;
            text-transform: uppercase;
            transition: all 200ms ease-in;
            margin: 30px 0;
            border-radius: 4px;
          }

          button:hover {
            background-color: white;
            color: #555;
            cursor: pointer;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
          }

          @keyframes colorSlide {
            0% { background-color: #152a68; }
            25% { background-color: royalblue; }
            50% { background-color: seagreen; }
            75% { background-color: tomato; }
            100% { background-color: #152a68; }
          }

          @keyframes fadeIn {
            from { opacity: 0; }
            100% { opacity: 1; }
          }

          @media (max-width: 768px) {
            h1 {
              font-size: 6em;
            }
          }

          @media (max-width: 480px) {
            h1 {
              font-size: 4em;
            }
            h3 {
              font-size: 1.2em;
            }
          }
        `}</style>
      </div>
    );
  }
}
