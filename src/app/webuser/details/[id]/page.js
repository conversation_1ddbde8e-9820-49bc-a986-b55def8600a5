'use client';

import { WebUser } from '@/components';
import { showToast } from '@/utils/helper';
import { useRouter } from 'next/navigation';
import { use } from 'react';
import messages from '@/utils/messages';

const WebUserDetailsPage = ({ params }) => {
    const router = useRouter();
    const resolvedParams = use(params);
    const { id } = resolvedParams;
    if (!id) {
        showToast(messages.WEB_USER_DETAILS_NOT_FOUND || 'Web User details not found', 'error');
        router.push('/not-found');
        return null;
    }
    return <WebUser.Details id={id} />;
};

export default WebUserDetailsPage;
