import Script from 'next/script';
import { Suspense } from 'react';

import 'bootstrap/dist/css/bootstrap.min.css';
import "../../public/css/reset.css";
import "../../public/css/style.css";
import "../../public/css/responsive.css";
import "./globals.css";
import "animate.css";

import { ClientProviders, PageLoader } from '@/components';

export const metadata = {
  title: 'WeTrade Admin',
  description: 'WeTrade Admin Panel',
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" suppressHydrationWarning={true}>
      <head>
        <meta charSet="UTF-8" />
        <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
        <meta name="description" content={metadata.description || 'WeTrade Admin Panel'} />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>{metadata.title || 'WeTrade Admin'}</title>
        <link rel="icon" href="/favicon.ico" />
        <link
          rel="stylesheet"
          href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css"
        />
        <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
      </head>
      <body className="dashboard-page" suppressHydrationWarning={true}>
        <Suspense fallback={<PageLoader />}>
          <ClientProviders>
            {children}
          </ClientProviders>
        </Suspense>
        <Script src="https://code.jquery.com/jquery-3.6.0.min.js" strategy="afterInteractive" />
        <Script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js" strategy="afterInteractive" />
      </body>
    </html>
  );
}