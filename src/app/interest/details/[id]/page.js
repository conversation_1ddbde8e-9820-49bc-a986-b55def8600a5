'use client';

import { Interest } from '@/components';
import { showToast } from '@/utils/helper';
import { useRouter } from 'next/navigation';
import { use } from 'react';
import messages from '@/utils/messages';
import { useSearchParams } from 'next/navigation';

const InterestDetailsPage = ({ params }) => {
    const router = useRouter();
    const resolvedParams = use(params);
    const { id } = resolvedParams;
    const searchParams = useSearchParams();
    const { name } = Object.fromEntries(searchParams.entries());
    if (!id) {
        showToast(messages.INTEREST_DETAILS_NOT_FOUND || 'Interest details not found', 'error');
        router.push('/not-found');
        return null;
    }
    return <Interest.List interestId={id || 0} interestName={name || ''} />;
};

export default InterestDetailsPage;
