'use client';

import { Idea } from '@/components';
import { showToast } from '@/utils/helper';
import { useRouter } from 'next/navigation';
import { use } from 'react';
import messages from '@/utils/messages';

const IdeaDetailsPage = ({ params }) => {
    const router = useRouter();
    const resolvedParams = use(params);
    const { id } = resolvedParams;
    if (!id) {
        showToast(messages.IDEA_DETAILS_NOT_FOUND || 'Idea details not found', 'error');
        router.push('/not-found');
        return null;
    }
    return <Idea.Details ideaId={id || 0} />;
};

export default IdeaDetailsPage;
