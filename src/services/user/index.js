import axios from 'axios';
import { getHeaders } from '@/utils/helper';
import messages from '@/utils/messages';
const API_URL = process.env.NEXT_PUBLIC_API_URL || '/api/v1/admin/';

class UserService {
    static async getList(page = 1, per_page = 10, search = '') {
        try {
            const headers = getHeaders();
            const response = await axios.get(`${API_URL}user?action=getList&page=${page}&per_page=${per_page}&search=${search}`, {
                headers: {
                    ...headers,
                    'Content-Type': 'application/json',
                },
            });
            return response?.data || {};
        } catch (error) {
            throw new Error(error.response?.data?.message || messages.SOMETHING_WENT_WRONG);
        }
    }

    static async getUserDetails(userId) {
        try {
            const headers = getHeaders();
            const response = await axios.get(`${API_URL}user?action=getUserDetails&userId=${userId}`, {
                headers: {
                    ...headers,
                    'Content-Type': 'application/json',
                },
            });
            return response?.data || {};
        } catch (error) {
            throw new Error(error.response?.data?.message || messages.SOMETHING_WENT_WRONG);
        }
    }

    static async updateUserDetails(data) {
        try {
            const headers = getHeaders();
            const response = await axios.patch(`${API_URL}user?action=updateUserDetails`, data, {
                headers: {
                    ...headers,
                    'Content-Type': 'multipart/form-data',
                },
            });
            return response?.data || {};
        } catch (error) {
            throw new Error(error.response?.data?.message || messages.SOMETHING_WENT_WRONG);
        }
    }

    static async updateUserStatus(id, status) {
        try {
            const headers = getHeaders();
            const response = await axios.post(`${API_URL}user?action=updateUserStatus`, { id, status }, {
                headers: {
                    ...headers,
                    'Content-Type': 'multipart/form-data',
                },
            });
            return response?.data || {};
        } catch (error) {
            throw new Error(error.response?.data?.message || messages.SOMETHING_WENT_WRONG);
        }
    }

    static async deleteUser(id) {
        try {
            const headers = getHeaders();
            const response = await axios.post(`${API_URL}user?action=deleteUser`, { id }, {
                headers: {
                    ...headers,
                    'Content-Type': 'multipart/form-data',
                },
            });
            return response?.data || {};
        } catch (error) {
            throw new Error(error.response?.data?.message || messages.SOMETHING_WENT_WRONG);
        }
    }
}

export default UserService;