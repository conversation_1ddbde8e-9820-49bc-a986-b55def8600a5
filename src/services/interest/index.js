import axios from 'axios';
import { getHeaders } from '@/utils/helper';
import messages from '@/utils/messages';
const API_URL = process.env.NEXT_PUBLIC_API_URL || '/api/v1/admin/';

class InterestService {
    static async getList(page = 1, per_page = 10, search = '', interestId) {
        try {
            const headers = getHeaders();
            const response = await axios.get(`${API_URL}interest?action=getList&page=${page}&per_page=${per_page}&search=${search}&interestId=${interestId}`, {
                headers: {
                    ...headers,
                    'Content-Type': 'application/json',
                },
            });
            return response?.data || {};
        } catch (error) {
            throw new Error(error.response?.data?.message || messages.SOMETHING_WENT_WRONG);
        }
    }

    static async updateInterestDetails(data) {
        try {
            const headers = getHeaders();
            const response = await axios.patch(`${API_URL}interest?action=updateInterestDetails`, data, {
                headers: {
                    ...headers,
                    'Content-Type': 'multipart/form-data',
                },
            });
            return response?.data || {};
        } catch (error) {
            throw new Error(error.response?.data?.message || messages.SOMETHING_WENT_WRONG);
        }
    }

    static async updateInterestStatus(id, status) {
        try {
            const headers = getHeaders();
            const response = await axios.post(`${API_URL}interest?action=updateInterestStatus`, {
                id,
                status,
            }, {
                headers: {
                    ...headers,
                    'Content-Type': 'multipart/form-data',
                },
            });
            return response?.data || {};
        } catch (error) {
            throw new Error(error.response?.data?.message || messages.SOMETHING_WENT_WRONG);
        }
    }
}

export default InterestService;