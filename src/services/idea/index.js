import axios from 'axios';
import { getHeaders } from '@/utils/helper';
import messages from '@/utils/messages';
const API_URL = process.env.NEXT_PUBLIC_API_URL || '/api/v1/admin/';

class IdeaService {
    static async getList(page = 1, per_page = 10, search = '') {
        try {
            const headers = getHeaders();
            const response = await axios.get(`${API_URL}idea?action=getList&page=${page}&per_page=${per_page}&search=${search}`, {
                headers: {
                    ...headers,
                    'Content-Type': 'application/json',
                },
            });
            return response?.data || {};
        } catch (error) {
            throw new Error(error.response?.data?.message || messages.SOMETHING_WENT_WRONG);
        }
    }

    static async getIdeaDetails(id) {
        try {
            const headers = getHeaders();
            const response = await axios.get(`${API_URL}idea?action=getIdeaDetails&id=${id}`, {
                headers: {
                    ...headers,
                    'Content-Type': 'application/json',
                },
            });
            return response?.data || {};
        } catch (error) {
            throw new Error(error.response?.data?.message || messages.SOMETHING_WENT_WRONG);
        }
    }

    static async updateIdeaStatus(id, status) {
        try {
            const headers = getHeaders();
            const response = await axios.post(`${API_URL}idea?action=updateIdeaStatus`, { id, status }, {
                headers: {
                    ...headers,
                    'Content-Type': 'multipart/form-data',
                },
            });
            return response?.data || {};
        } catch (error) {
            throw new Error(error.response?.data?.message || messages.SOMETHING_WENT_WRONG);
        }
    }

    static async deleteIdea(id) {
        try {
            const headers = getHeaders();
            const response = await axios.post(`${API_URL}idea?action=deleteIdea`, { id }, {
                headers: {
                    ...headers,
                    'Content-Type': 'multipart/form-data',
                },
            });
            return response?.data || {};
        } catch (error) {
            throw new Error(error.response?.data?.message || messages.SOMETHING_WENT_WRONG);
        }
    }
}

export default IdeaService;