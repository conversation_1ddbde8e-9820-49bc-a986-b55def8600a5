import axios from 'axios';
import { getHeaders } from '@/utils/helper';
import messages from '@/utils/messages';
const API_URL = process.env.NEXT_PUBLIC_API_URL || '/api/v1/admin/';

class CommentService {
    static async getList(id, page = 1, per_page = 10) {
        try {
            const headers = getHeaders();
            const response = await axios.get(`${API_URL}comment?action=getList&id=${id}&page=${page}&per_page=${per_page}`, {
                headers: {
                    ...headers,
                    'Content-Type': 'application/json',
                },
            });
            return response?.data || {};
        } catch (error) {
            throw new Error(error.response?.data?.message || messages.SOMETHING_WENT_WRONG);
        }
    }

    static async deleteComment(id) {
        try {
            const headers = getHeaders();
            const response = await axios.post(`${API_URL}comment?action=deleteComment`, { id }, {
                headers: {
                    ...headers,
                    'Content-Type': 'multipart/form-data',
                },
            });
            return response?.data || {};
        } catch (error) {
            throw new Error(error.response?.data?.message || messages.SOMETHING_WENT_WRONG);
        }
    }
}

export default CommentService;