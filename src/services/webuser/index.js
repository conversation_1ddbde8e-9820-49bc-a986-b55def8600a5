import axios from 'axios';
import { getHeaders } from '@/utils/helper';
import messages from '@/utils/messages';
const API_URL = process.env.NEXT_PUBLIC_API_URL || '/api/v1/admin/';

class WebUserService {
    static async getList(page = 1, per_page = 10, search = '') {
        try {
            const headers = getHeaders();
            const response = await axios.get(`${API_URL}webuser?action=getList&page=${page}&per_page=${per_page}&search=${search}`, {
                headers: {
                    ...headers,
                    'Content-Type': 'application/json',
                },
            });
            return response?.data || {};
        } catch (error) {
            throw new Error(error.response?.data?.message || messages.SOMETHING_WENT_WRONG);
        }
    }

    static async getWebUserDetails(userId) {
        try {
            const headers = getHeaders();
            const response = await axios.get(`${API_URL}webuser?action=getWebUserDetails&userId=${userId}`, {
                headers: {
                    ...headers,
                    'Content-Type': 'application/json',
                },
            });
            return response?.data || {};
        } catch (error) {
            throw new Error(error.response?.data?.message || messages.SOMETHING_WENT_WRONG);
        }
    }

    static async updateWebUserDetails(data) {
        try {
            const headers = getHeaders();
            const response = await axios.patch(`${API_URL}webuser?action=updateWebUserDetails`, data, {
                headers: {
                    ...headers,
                    'Content-Type': 'multipart/form-data',
                },
            });
            return response?.data || {};
        } catch (error) {
            throw new Error(error.response?.data?.message || messages.SOMETHING_WENT_WRONG);
        }
    }

    static async updateWebUserStatus(id, status) {
        try {
            const headers = getHeaders();
            const response = await axios.post(`${API_URL}webuser?action=updateWebUserStatus`, { id, status }, {
                headers: {
                    ...headers,
                    'Content-Type': 'multipart/form-data',
                },
            });
            return response?.data || {};
        } catch (error) {
            throw new Error(error.response?.data?.message || messages.SOMETHING_WENT_WRONG);
        }
    }

    static async bulkUploadWebUsers(data) {
        try {
            const headers = getHeaders();
            const response = await axios.post(`${API_URL}webuser?action=bulkUploadWebUsers`, data, {
                headers: {
                    ...headers,
                    'Content-Type': 'multipart/form-data',
                },
            });
            return response?.data || {};
        } catch (error) {
            throw new Error(error.response?.data?.message || messages.SOMETHING_WENT_WRONG);
        }
    }
}

export default WebUserService;