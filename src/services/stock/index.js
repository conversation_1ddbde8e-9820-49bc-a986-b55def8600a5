import axios from 'axios';
import { getHeaders } from '@/utils/helper';
import messages from '@/utils/messages';
const API_URL = process.env.NEXT_PUBLIC_API_URL || '/api/v1/admin/';

class StockService {
    static async getList(page = 1, per_page = 10, search = '', type = '') {
        try {
            const headers = getHeaders();
            const response = await axios.get(`${API_URL}stock?action=getList&page=${page}&per_page=${per_page}&search=${search}&type=${type}`, {
                headers: {
                    ...headers,
                    'Content-Type': 'application/json',
                },
            });
            return response?.data || {};
        } catch (error) {
            throw new Error(error.response?.data?.message || messages.SOMETHING_WENT_WRONG);
        }
    }

    static async getStockDetails(stockId) {
        try {
            const headers = getHeaders();
            const response = await axios.get(`${API_URL}stock?action=getStockDetails&stockId=${stockId}`, {
                headers: {
                    ...headers,
                    'Content-Type': 'application/json',
                },
            });
            return response?.data || {};
        } catch (error) {
            throw new Error(error.response?.data?.message || messages.SOMETHING_WENT_WRONG);
        }
    }

    static async updateStockStatus(id, status) {
        try {
            const headers = getHeaders();
            const response = await axios.post(`${API_URL}stock?action=updateStockStatus`, { id, status }, {
                headers: {
                    ...headers,
                    'Content-Type': 'multipart/form-data',
                },
            });
            return response?.data || {};
        } catch (error) {
            throw new Error(error.response?.data?.message || messages.SOMETHING_WENT_WRONG);
        }
    }

    static async syncStocks(data) {
        try {
            const headers = getHeaders();
            const response = await axios.post(`${API_URL}stock?action=syncStocks`, data, {
                headers: {
                    ...headers,
                    'Content-Type': 'multipart/form-data',
                },
            });
            return response?.data || {};
        } catch (error) {
            throw new Error(error.response?.data?.message || messages.SOMETHING_WENT_WRONG);
        }
    }
}

export default StockService;