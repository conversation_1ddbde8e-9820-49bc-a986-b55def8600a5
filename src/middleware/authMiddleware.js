import { useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { useDispatch, useSelector } from 'react-redux';
import { setUser, clearUser } from '@/store/slices/userSlice';

export function AuthMiddleware({ children }) {
  const { data: session, status } = useSession();
  const dispatch = useDispatch();
  const currentUser = useSelector((state) => state.user);
  const hasUpdated = useRef(false);

  useEffect(() => {
    if (status === 'authenticated' && session?.user) {
      if (!hasUpdated.current) {
        dispatch(setUser({
          id: currentUser.id || session.user.id,
          email: currentUser.email || session.user.email,
          roleId: currentUser.roleId || session.user.roleId,
          otpPending: currentUser.otpPending || session.user.otpPending,
          accessToken: session.user.accessToken,
        }));
        hasUpdated.current = true;
      }
    } else if (status === 'unauthenticated' && (currentUser.id !== null || currentUser.email !== null)) {
      if (!hasUpdated.current) {
        dispatch(clearUser());
        hasUpdated.current = true;
      }
    }
  }, [session, status, dispatch, currentUser]);

  return children;
}