/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  transition: all 0.3s ease-in-out;
}

body {
  font-family: "Poppins", sans-serif;
  background-color: #f0f0f0;
  color: #333;
}

/* Login Page Styles */
.login-page {
  background: linear-gradient(135deg, #4568dc 0%, #b06ab3 100%);
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.login-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("https://wetrade.co/public/images/banner_bg1.png");
  background-size: cover;
  background-position: center;
  opacity: 0.1;
  z-index: 0;
}

.login-container {
  width: 100%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
  animation: fadeUp 0.6s ease-out;
}

.login-card {
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
  width: 100%;
  overflow: hidden;
  transform: translateY(0);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.login-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25);
}

.login-header {
  background: linear-gradient(135deg, #1d1f2a 0%, #2c3e50 100%);
  color: white;
  padding: 25px 20px;
  text-align: center;
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15px;
}

.logo {
  width: auto;
  height: 60px;
  margin-bottom: 10px;
  filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.3));
  transition: transform 0.3s ease;
}

.logo:hover {
  transform: scale(1.1);
}

.login-header h1 {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  letter-spacing: 1px;
}

.login-header p {
  margin-top: 5px;
  font-size: 16px;
  opacity: 0.9;
}

.login-body {
  padding: 35px 30px;
}

.login-btn {
  background: linear-gradient(135deg, #09c 0%, #0074a6 100%);
  border: none;
  padding: 14px;
  font-weight: 500;
  margin-top: 15px;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 153, 204, 0.4);
}

.login-btn:hover {
  background: linear-gradient(135deg, #0082b3 0%, #005a80 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 153, 204, 0.5);
}

.login-footer {
  text-align: center;
  margin-top: 25px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}


.form-label {
  font-weight: 500;
  margin-bottom: 8px;
  color: #444;
}

.required-asterisk {
  color: #dc3545;
  font-weight: bold;
  margin-left: 3px;
}

.form-control:focus {
  border-color: #6bca16;
  box-shadow: none;
  transform: none;
  outline: none;
}

/* Fix Form Control Styles */
input.form-control,
select.form-select,
textarea.form-control {
  border-width: 2px !important;
  border-color: #e0e6ed !important;
  border-radius: 8px !important;
  padding: 12px 15px;
  font-size: 14px;
  color: #444;
  transition: all 0.3s ease;
  box-shadow: none;
}

input.form-control::placeholder,
select.form-select::placeholder,
textarea.form-control::placeholder {
  color: #aab7c4;
  opacity: 0.8;
}

/* Dashboard Styles */
.dashboard-page {
  background-color: #f8f9fd;
  min-height: 100vh;
}

.wrapper {
  display: flex;
  width: 100%;
  align-items: stretch;
}

/* Sidebar */
#sidebar {
  min-width: 240px;
  max-width: 240px;
  background: linear-gradient(180deg, #1d1f2a 0%, #2c3e50 100%);
  color: #fff;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  height: 100vh;
  position: fixed;
  z-index: 999;
  box-shadow: 5px 0 15px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

#sidebar.active {
  margin-left: -240px;
  box-shadow: none;
}

#sidebar .sidebar-header {
  padding: 25px 20px;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

#sidebar .sidebar-header h2 {
  display: none;
}

#sidebar ul.components {
  padding: 20px 0;
  flex-grow: 1;
}

#sidebar ul li {
  position: relative;
}

#sidebar ul li::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 20px;
  right: 20px;
  height: 1px;
  background-color: rgba(255, 255, 255, 0.05);
}

#sidebar ul li a {
  padding: 15px 20px;
  font-size: 14px;
  display: flex;
  align-items: center;
  color: #b9bdc5;
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

#sidebar ul li a i {
  margin-right: 12px;
  width: 20px;
  text-align: center;
  font-size: 18px;
  transition: all 0.3s ease;
}

#sidebar ul li a:hover {
  color: #fff;
  background: rgba(255, 255, 255, 0.05);
}

#sidebar ul li a:hover i {
  transform: none;
  color: #6bca16;
}

#sidebar ul li.active > a {
  color: #fff;
  background: rgba(107, 202, 22, 0.2);
  border-left: 4px solid #6bca16;
  font-weight: 500;
}

#sidebar ul li.active > a i {
  color: #6bca16;
}

#sidebar .sidebar-footer {
  padding: 15px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

#sidebar .sidebar-footer a.logout-link {
  color: #b9bdc5;
  text-decoration: none;
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-radius: 6px;
  transition: all 0.3s ease;
  background-color: rgba(0, 0, 0, 0.2);
}

#sidebar .sidebar-footer a.logout-link i {
  margin-right: 8px;
  font-size: 16px;
}

#sidebar .sidebar-footer a.logout-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}

#sidebar .sidebar-footer a.logout-link:hover i {
  transform: none;
  color: #6bca16;
}

/* Content */
#content {
  width: 100%;
  padding: 0;
  min-height: 100vh;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  margin-left: 240px;
}

.navbar {
  padding: 15px 20px;
  background: transparent;
  border: none;
  border-radius: 0;
  box-shadow: none;
}

#sidebarCollapse {
  background: transparent;
  color: #333;
  border: none;
  font-size: 22px;
  transition: all 0.2s ease;
  display: none; /* Hide for desktop by default */
}

#sidebarCollapse:hover {
  color: #6bca16;
  transform: scale(1.1);
}

.user-profile {
  position: relative;
  cursor: pointer;
}

.user-profile img {
  width: 45px;
  height: 45px;
  object-fit: cover;
  border: 2px solid #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.user-profile:hover img {
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.content-wrapper {
  padding: 0 25px 25px;
  animation: fadeIn 0.4s ease-out;
}

.content-header {
  background-image: url("/images/breadcrumb-bg.jpg");
  background-size: cover;
  background-position: bottom left;
  position: relative;
  padding: 30px 25px;
  margin-bottom: 25px;
  border-radius: 12px;
  color: white;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.25);
  transition: all 0.3s ease;
  overflow: hidden;
  z-index: 1;
}

.content-header:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(92, 68, 184, 0.7) 0%,
    rgba(61, 43, 148, 0.75) 100%
  );
  z-index: -1;
}

.content-header:hover {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.25);
  transform: none;
}

.content-header h1 {
  margin: 0;
  font-size: 26px;
  font-weight: 600;
  letter-spacing: 0.5px;
  position: relative;
  z-index: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Content Header with Icons Styling */
.content-header .d-flex.align-items-center {
  display: flex;
  align-items: center;
  gap: 15px;
  position: relative;
  z-index: 2;
}

.content-header .d-flex.align-items-center i {
  color: white;
  font-size: 28px;
  transition: all 0.3s ease;
  filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.4));
}

.content-header .d-flex.align-items-center:hover i {
  transform: none;
  filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.4));
}

.content-header #page-title {
  margin: 0;
  font-size: 26px;
  font-weight: 600;
  letter-spacing: 0.5px;
  position: relative;
  z-index: 1;
  color: white;
  transition: all 0.3s ease;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.content-header .d-flex.align-items-center:hover #page-title {
  transform: none;
}

/* Custom header backgrounds for different page types */
.content-header.users-header:before {
  background: linear-gradient(
    135deg,
    rgba(78, 115, 223, 0.85) 0%,
    rgba(34, 74, 190, 0.9) 100%
  );
}

.content-header.games-header:before {
  background: linear-gradient(
    135deg,
    rgba(246, 194, 62, 0.85) 0%,
    rgba(221, 162, 10, 0.9) 100%
  );
}

.content-header.interests-header:before {
  background: linear-gradient(
    135deg,
    rgba(28, 200, 138, 0.85) 0%,
    rgba(22, 154, 111, 0.9) 100%
  );
}

.content-header.stocks-header:before {
  background: linear-gradient(
    135deg,
    rgba(231, 74, 59, 0.85) 0%,
    rgba(192, 57, 43, 0.9) 100%
  );
}

.content-header.ideas-header:before {
  background: linear-gradient(
    135deg,
    rgba(54, 185, 204, 0.85) 0%,
    rgba(37, 131, 145, 0.9) 100%
  );
}

.content-header.crypto-header:before {
  background: linear-gradient(
    135deg,
    rgba(142, 68, 173, 0.85) 0%,
    rgba(108, 52, 131, 0.9) 100%
  );
}

/* Add a subtle pattern overlay */
.content-header:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'%3E%3Cpath fill='%23ffffff' fill-opacity='0.1' d='M1 3h1v1H1V3zm2-2h1v1H3V1z'%3E%3C/path%3E%3C/svg%3E");
  opacity: 0.3;
  z-index: -1;
}

/* Enhanced breadcrumb navigation if needed */
.breadcrumb {
  background: transparent;
  padding: 0;
  margin: 8px 0 0 0;
  display: flex;
  align-items: center;
}

.breadcrumb-item {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.breadcrumb-item.active {
  color: #ffffff;
}

.breadcrumb-item a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.2s ease;
}

.breadcrumb-item a:hover {
  color: #ffffff;
  text-decoration: none;
}

.breadcrumb-item + .breadcrumb-item::before {
  color: rgba(255, 255, 255, 0.5);
  content: ">";
  padding: 0 8px;
}

.main-content .card {
  border: none;
  border-radius: 12px;
  background-color: #fff;
  overflow: hidden;
  transition: none;
}

.main-content .card:hover {
  box-shadow: none;
  transform: none;
}

.card-body {
  padding: 25px;
}

.entries-select {
  display: flex;
  align-items: center;
}

.entries-select span {
  margin: 0 10px;
  font-size: 14px;
  color: #666;
}

.entries-select select {
  width: 70px;
  border-radius: 8px;
  border: 1px solid #ddd;
  padding: 6px 10px;
  cursor: pointer;
}

.search-box {
  display: flex;
  align-items: center;
}

.search-box label {
  margin-right: 10px;
  font-size: 14px;
  color: #666;
}

.search-box input {
  width: 240px;
  border-radius: 8px;
  padding: 8px 15px;
  border: 1px solid #ddd;
  transition: all 0.3s ease;
}

.search-box input:focus {
  border-color: #6bca16;
  box-shadow: 0 0 0 0.2rem rgba(107, 202, 22, 0.2);
  width: 280px;
}

/* Table Styles */
.table-responsive {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
  margin-top: 20px;
  background: white;
  position: relative;
}

.table {
  margin-bottom: 0;
}

.table thead th {
  background-color: #5c44b8;
  color: white;
  border: none;
  font-weight: 600;
  white-space: nowrap;
  vertical-align: middle;
  padding: 16px;
  transition: background-color 0.3s ease;
  position: relative;
  cursor: pointer;
}

.table thead th:hover {
  background-color: #4d39a3;
}

.table thead th i {
  margin-left: 8px;
  font-size: 12px;
  transition: transform 0.3s ease;
  opacity: 0.7;
}

.table thead th:hover i {
  opacity: 1;
}

.table th.asc i {
  transform: rotate(180deg);
}

.table tbody tr {
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.table tbody tr:hover {
  background-color: rgba(107, 202, 22, 0.05);
  transform: none;
  box-shadow: none;
  z-index: auto;
  position: static;
}

.table tbody td {
  vertical-align: middle;
  padding: 15px 16px;
  color: #555;
  border-top: none;
}

.table tbody td img.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.table tbody tr:hover td img.user-avatar {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Status Toggle Switch */
.form-check-input[type="checkbox"] {
  background-color: #e0e0e0;
  border-color: #d0d0d0;
  height: 22px;
  width: 40px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
}

.form-check-input:checked[type="checkbox"] {
  background-color: #6bca16;
  border-color: #6bca16;
}

.form-check-input[type="checkbox"]:focus {
  box-shadow: 0 0 0 0.2rem rgba(107, 202, 22, 0.25);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
}

.btn-view,
.btn-edit,
.btn-delete {
  border: none;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
  position: relative;
  overflow: hidden;
}

.btn-view::after,
.btn-edit::after,
.btn-delete::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: scale(0) translate(-50%, -50%);
  transform-origin: left top;
  transition: all 0.4s ease-out;
}

.btn-view:hover::after,
.btn-edit:hover::after,
.btn-delete:hover::after {
  transform: scale(3) translate(-50%, -50%);
}

.btn-view:active::after,
.btn-edit:active::after,
.btn-delete:active::after {
  background: rgba(255, 255, 255, 0.5);
  transform: scale(1.5) translate(-50%, -50%);
  transition: all 0.1s ease-out;
}

.btn-view {
  background-color: #e6f4ff;
  color: #09c;
}

.btn-edit {
  background-color: #e6f4ff;
  color: #09c;
}

.btn-delete {
  background-color: #ffebee;
  color: #f44336;
}

.btn-view:hover,
.btn-edit:hover {
  background-color: #09c;
  color: white;
  transform: none;
  box-shadow: none;
}

.btn-view:active,
.btn-edit:active {
  transform: none;
  box-shadow: none;
}

.btn-delete:hover {
  background-color: #f44336;
  color: white;
  transform: none;
  box-shadow: none;
}

.btn-delete:active {
  transform: none;
  box-shadow: none;
}

.btn-view:hover i,
.btn-edit:hover i,
.btn-delete:hover i {
  transform: none;
}

/* Updated Add Button Styles */
.btn-add {
  background: linear-gradient(135deg, #4CAF50 0%, #43A047 100%);
  color: #fff;
  border: none;
  padding: 0 28px;
  height: 48px;
  min-width: 140px;
  font-weight: 600;
  font-size: 16px;
  border-radius: 16px;
  box-shadow: 0 2px 10px rgba(76, 175, 80, 0.10);
  transition: background 0.2s, box-shadow 0.2s, color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-right: 0;
}

.btn-add:hover,
.btn-add:focus {
  background: linear-gradient(135deg, #388E3C 0%, #2e7031 100%);
  color: #fff;
  box-shadow: 0 4px 16px rgba(76, 175, 80, 0.18);
}

.btn-add:active {
  background: linear-gradient(135deg, #388E3C 0%, #2e7031 100%);
  color: #fff;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.12);
}

/* Add ripple effect */
.btn-add::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 10px;
  height: 10px;
  background: rgba(255, 255, 255, 0.4);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1) translate(-50%, -50%);
  transform-origin: 0 0;
}

.btn-add:active::after {
  animation: btn-ripple 0.6s ease-out;
}

@keyframes btn-ripple {
  0% {
    opacity: 0.7;
    transform: scale(0) translate(-50%, -50%);
  }
  100% {
    opacity: 0;
    transform: scale(20) translate(-50%, -50%);
  }
}

/* Pagination */
.pagination {
  margin-bottom: 0;
  gap: 5px;
}

.page-item .page-link {
  border-radius: 8px;
  border: 1px solid #ddd;
  padding: 8px 16px;
  color: #555;
  transition: all 0.3s ease;
}

.page-item .page-link:hover {
  background-color: #f5f5f5;
  color: #6bca16;
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.05);
}

.page-item.active .page-link {
  background-color: #6bca16;
  border-color: #6bca16;
  color: white;
  font-weight: 500;
  transform: none;
  box-shadow: none;
}

.page-item.disabled .page-link {
  color: #aaa;
  background-color: #f8f8f8;
  cursor: not-allowed;
}

/* Upload CSV Page */
.upload-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 350px;
}

.upload-box {
  border: 2px dashed #ddd;
  border-radius: 12px;
  padding: 50px;
  text-align: center;
  width: 100%;
  max-width: 550px;
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.8);
  position: relative;
}

.upload-box:hover {
  border-color: #6bca16;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
}

.upload-box h2 {
  margin-bottom: 30px;
  font-size: 22px;
  font-weight: 500;
  color: #333;
}

.file-upload-wrapper {
  position: relative;
  width: 100%;
  margin-bottom: 20px;
}

.file-upload-input {
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  z-index: 2;
}

.file-upload-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 18px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 8px;
  cursor: pointer;
  width: 100%;
  transition: all 0.3s ease;
}

.file-upload-label:hover {
  background-color: #eaeaea;
  border-color: #ccc;
}

.file-upload-label .file-name {
  color: #888;
}

/* Enhanced Upload CSV Styles */
.upload-container {
  padding: 20px 0;
}

.upload-area {
  background: linear-gradient(135deg, #f8fbff 0%, #f0f4ff 100%);
  border-radius: 16px;
  padding: 50px 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
  margin: 0 auto;
  max-width: 700px;
}

.upload-area::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, #6bca16, #5c44b8);
}

.upload-icon {
  width: 80px;
  height: 80px;
  background-color: rgba(107, 202, 22, 0.1);
  border-radius: 50%;
  margin: 0 auto 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6bca16;
  font-size: 32px;
  transition: all 0.3s ease;
}

.upload-area:hover .upload-icon {
  transform: scale(1.1);
  background-color: rgba(107, 202, 22, 0.15);
}

.upload-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.upload-subtitle {
  color: #666;
  margin-bottom: 30px;
  font-size: 15px;
}

.upload-dropzone {
  border: 2px dashed #cdd6f3;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  background-color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
  position: relative;
}

.upload-dropzone:hover {
  border-color: #6bca16;
  background-color: rgba(255, 255, 255, 0.9);
}

.upload-dropzone.dragging {
  border-color: #6bca16;
  background-color: rgba(107, 202, 22, 0.05);
}

.upload-dropzone input[type="file"] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
  z-index: 10;
}

.dropzone-content {
  pointer-events: none;
}

.dropzone-icon {
  font-size: 40px;
  color: #6bca16;
  margin-bottom: 15px;
}

.dropzone-text {
  font-weight: 500;
  margin-bottom: 10px;
}

.dropzone-hint {
  color: #888;
  font-size: 14px;
}

.selected-file {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f8f9fd;
  border-radius: 8px;
  padding: 10px 15px;
  margin-top: 15px;
  border: 1px solid #e6e9f4;
  transition: all 0.3s ease;
  display: none;
}

.selected-file.active {
  display: flex;
}

.file-info {
  display: flex;
  align-items: center;
}

.file-icon {
  width: 40px;
  height: 40px;
  background-color: rgba(92, 68, 184, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #5c44b8;
  margin-right: 12px;
}

.file-details {
  text-align: left;
}

.file-details .file-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.file-details .file-size {
  color: #888;
  font-size: 12px;
}

.remove-file {
  background: transparent;
  border: none;
  color: #f44336;
  cursor: pointer;
  transition: all 0.2s ease;
}

.remove-file:hover {
  color: #d32f2f;
  transform: scale(1.1);
}

.upload-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.btn-upload {
  background: linear-gradient(135deg, #6bca16 0%, #5ab013 100%);
  color: white;
  border: none;
  padding: 12px 35px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(107, 202, 22, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-upload:hover {
  background: linear-gradient(135deg, #5ab013 0%, #4a9010 100%);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(107, 202, 22, 0.4);
  color: white;
}

.btn-upload:disabled {
  background: linear-gradient(135deg, #a8a8a8 0%, #8a8a8a 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.upload-help {
  margin-top: 25px;
  color: #666;
  font-size: 14px;
}

.upload-help a {
  color: #5c44b8;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
}

.upload-help a:hover {
  color: #6bca16;
  text-decoration: underline;
}

/* Responsive Styles */
@media (max-width: 992px) {
  #sidebar {
    margin-left: -240px;
  }
  #sidebar.active {
    margin-left: 0;
  }
  #content {
    margin-left: 0;
  }
  #content.active {
    margin-left: 240px;
  }
  #sidebarCollapse {
    display: block; /* Show for mobile/tablet */
  }
  .navbar-toggler {
    display: block;
  }
  .search-box {
    margin-top: 15px;
  }
  .table-responsive {
    border: none;
  }
  .entries-select,
  .search-box {
    width: 100%;
  }
  .d-flex {
    flex-direction: column;
  }
  .d-flex.justify-content-between {
    align-items: flex-start;
  }
  .search-box input {
    width: 100%;
  }
  .pagination {
    margin-top: 20px;
  }
  .content-header.d-flex {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .login-card {
    width: 90%;
  }

  .upload-box {
    padding: 30px 20px;
  }
}

/* Add Game/Interest Page */
.form-group {
  margin-bottom: 25px;
}

.form-label {
  font-weight: 500;
  margin-bottom: 10px;
  color: #444;
}

.form-control:focus {
  border-color: #6bca16;
  box-shadow: none;
  transform: none;
  outline: none;
}

.form-select {
  cursor: pointer;
  padding: 12px 15px;
  border-radius: 8px;
  border: 1px solid #ddd;
  transition: all 0.3s ease;
}

.form-select:focus {
  border-color: #6bca16;
  box-shadow: 0 0 0 0.2rem rgba(107, 202, 22, 0.25);
}

.input-group {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.input-group:focus-within {
  box-shadow: none;
}

.input-group:focus-within .input-group-text {
  border-color: #6bca16 !important;
  background-color: #f8fcf3 !important;
  color: #6bca16 !important;
  box-shadow: none !important;
}

.input-group-text {
  background-color: #f8f9fa !important;
  border: 1px solid #e0e6ed !important;
  color: #6c757d !important;
  transition: all 0.3s ease !important;
}

.button-group {
  display: flex;
  gap: 15px;
  margin-top: 35px;
}

.btn-cancel {
  background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
  color: #555;
  border: none;
  padding: 0 28px;
  height: 48px;
  min-width: 140px;
  font-weight: 600;
  font-size: 16px;
  border-radius: 16px;
  box-shadow: 0 2px 10px rgba(160, 160, 160, 0.10);
  transition: background 0.2s, box-shadow 0.2s, color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-right: 0;
}

.btn-cancel:disabled,
.btn-cancel[disabled] {
  background: linear-gradient(135deg, #e0e0e0 0%, #bdbdbd 100%) !important;
  color: #555 !important;
  cursor: not-allowed;
  box-shadow: none;
  opacity: 1 !important;
}

.btn-cancel:hover:not(:disabled),
.btn-cancel:focus:not(:disabled) {
  background: linear-gradient(135deg, #e0e0e0 0%, #d0d0d0 100%);
  box-shadow: 0 4px 12px rgba(160, 160, 160, 0.18);
  color: #333;
}

/* Custom animations and transitions */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-backdrop {
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  transition: all 0.15s ease-out;
}

.modal.fade .modal-dialog {
  transform: scale(0.8);
  opacity: 0;
  transition: all 0.15s ease-out;
}

.modal.show .modal-dialog {
  transform: scale(1);
  opacity: 1;
}

.modal-content {
  border: none;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.modal-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1.25rem 1.5rem;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #4568dc 0%, #b06ab3 100%);
  color: white;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  position: relative;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  flex: 1;
}

.modal-header .btn-close {
  position: absolute;
  right: 1.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: transparent
    url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e")
    center/1em auto no-repeat;
  opacity: 0.8;
  padding: 1rem;
  margin: -0.5rem -0.5rem -0.5rem auto;
  width: 2rem;
  height: 2rem;
}

.modal-header .btn-close:hover {
  opacity: 1;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1.25rem 1.5rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}

/* When modal is open, blur the background content */
body.modal-open .content-wrapper,
body.modal-open #sidebar {
  filter: blur(1px);
  transition: filter 0.12s ease-out;
}

/* When modal is closing, remove blur faster */
body .content-wrapper,
body #sidebar {
  filter: blur(0);
  transition: filter 0.06s ease-out;
}

/* Stats Card Improvements */
.stats-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
  background: white;
  transition: all 0.3s ease;
  height: 100%;
}

.stats-card:hover {
  transform: none;
  box-shadow: none;
}

.stats-card-body {
  padding: 20px;
  display: flex;
  align-items: center;
}

.stats-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  margin-right: 15px;
  color: white;
  font-size: 28px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stats-info h5 {
  color: #777;
  font-size: 14px;
  margin-bottom: 5px;
}

.stats-info h2 {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 5px;
  color: #333;
}

.stats-info .badge {
  font-size: 12px;
  padding: 5px 8px;
  border-radius: 6px;
}

/* Dashboard Stats Overview */
.dashboard-overview {
  margin-bottom: 30px;
}

.dashboard-overview .row {
  margin-bottom: 20px;
}

.dashboard-stat-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
  background: white;
  transition: all 0.3s ease;
  position: relative;
  height: 100%;
  padding: 25px;
}

.dashboard-stat-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.dashboard-stat-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
}

.dashboard-stat-card.users::before {
  background: linear-gradient(90deg, #4e73df, #224abe);
}

.dashboard-stat-card.interests::before {
  background: linear-gradient(90deg, #1cc88a, #169a6f);
}

.dashboard-stat-card.games::before {
  background: linear-gradient(90deg, #f6c23e, #dda20a);
}

.dashboard-stat-card.stocks::before {
  background: linear-gradient(90deg, #e74a3b, #c0392b);
}

.dashboard-stat-card.ideas::before {
  background: linear-gradient(90deg, #36b9cc, #258391);
}

.dashboard-stat-card.crypto::before {
  background: linear-gradient(90deg, #8e44ad, #6c3483);
}

.dashboard-stat-card h3 {
  color: #5a5c69;
  font-size: 0.8rem;
  font-weight: 700;
  text-transform: uppercase;
  margin-bottom: 1rem;
}

.dashboard-stat-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: #444;
  margin-bottom: 0.5rem;
}

.dashboard-stat-icon {
  position: absolute;
  right: 20px;
  top: 15px;
  color: rgba(0, 0, 0, 0.1);
  font-size: 2rem;
}

/* Improved buttons styling */
.btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  padding: 10px 20px;
  position: relative;
  overflow: hidden;
  text-transform: none;
  letter-spacing: 0.5px;
}

.btn::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: width 0.3s, height 0.3s, opacity 0.3s;
}

.btn:active::after {
  width: 300px;
  height: 300px;
  opacity: 1;
  transition: 0s;
}

.btn-primary {
  background: linear-gradient(135deg, #5c44b8 0%, #4d39a3 100%);
  border: none;
  color: white;
  box-shadow: 0 4px 15px rgba(92, 68, 184, 0.3);
}

.btn-primary:hover,
.btn-primary:focus {
  background: linear-gradient(135deg, #4d39a3 0%, #3d2b94 100%);
  transform: translateY(-3px);
  box-shadow: 0 7px 20px rgba(92, 68, 184, 0.4);
}

.btn-primary:active {
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(92, 68, 184, 0.3);
}

.btn-success {
  background: linear-gradient(135deg, #6bca16 0%, #5ab013 100%);
  border: none;
  color: white;
  box-shadow: 0 4px 15px rgba(107, 202, 22, 0.3);
}

.btn-success:hover,
.btn-success:focus {
  background: linear-gradient(135deg, #5ab013 0%, #4a9010 100%);
  transform: translateY(-3px);
  box-shadow: 0 7px 20px rgba(107, 202, 22, 0.4);
}

.btn-success:active {
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(107, 202, 22, 0.3);
}

.btn-danger {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
  border: none;
  color: white;
  box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

.btn-danger:hover,
.btn-danger:focus {
  background: linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%);
  transform: translateY(-3px);
  box-shadow: 0 7px 20px rgba(244, 67, 54, 0.4);
}

.btn-danger:active {
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(244, 67, 54, 0.3);
}

.btn-info {
  background: linear-gradient(135deg, #03a9f4 0%, #039be5 100%);
  border: none;
  color: white;
  box-shadow: 0 4px 15px rgba(3, 169, 244, 0.3);
}

.btn-info:hover,
.btn-info:focus {
  background: linear-gradient(135deg, #039be5 0%, #0277bd 100%);
  transform: translateY(-3px);
  box-shadow: 0 7px 20px rgba(3, 169, 244, 0.4);
}

.btn-info:active {
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(3, 169, 244, 0.3);
}

.btn-warning {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  border: none;
  color: white;
  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
}

.btn-warning:hover,
.btn-warning:focus {
  background: linear-gradient(135deg, #f57c00 0%, #e65100 100%);
  transform: translateY(-3px);
  box-shadow: 0 7px 20px rgba(255, 152, 0, 0.4);
}

.btn-warning:active {
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(255, 152, 0, 0.3);
}

.btn-cancel {
  background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
  color: #555;
  border: none;
  padding: 0 28px;
  height: 48px;
  min-width: 140px;
  font-weight: 600;
  font-size: 16px;
  border-radius: 16px;
  box-shadow: 0 2px 10px rgba(160, 160, 160, 0.10);
  transition: background 0.2s, box-shadow 0.2s, color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-right: 0;
}

.btn-cancel:disabled,
.btn-cancel[disabled] {
  background: linear-gradient(135deg, #e0e0e0 0%, #bdbdbd 100%) !important;
  color: #555 !important;
  cursor: not-allowed;
  box-shadow: none;
  opacity: 1 !important;
}

.btn-cancel:hover:not(:disabled),
.btn-cancel:focus:not(:disabled) {
  background: linear-gradient(135deg, #e0e0e0 0%, #d0d0d0 100%);
  box-shadow: 0 4px 12px rgba(160, 160, 160, 0.18);
  color: #333;
}

.btn-submit {
  background: linear-gradient(135deg, #4CAF50 0%, #43A047 100%);
  color: #fff;
  border: none;
  padding: 0 28px;
  height: 48px;
  min-width: 140px;
  font-weight: 600;
  font-size: 16px;
  border-radius: 16px;
  box-shadow: 0 2px 10px rgba(76, 175, 80, 0.10);
  transition: background 0.2s, box-shadow 0.2s, color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-right: 0;
}

.btn-submit:disabled,
.btn-submit[disabled] {
  background: linear-gradient(135deg, #4CAF50 0%, #43A047 100%) !important;
  color: #fff !important;
  cursor: not-allowed;
  box-shadow: none;
  opacity: 1 !important;
}

.btn-submit:hover:not(:disabled),
.btn-submit:focus:not(:disabled) {
  background: linear-gradient(135deg, #43A047 0%, #388E3C 100%);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.18);
  color: #fff;
}

.btn-submit:active {
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(107, 202, 22, 0.3);
}

/* Improved modal styling */
.modal-content {
  border: none;
  border-radius: 12px;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.2);
  animation: fadeIn 0.4s ease-out, slideIn 0.3s ease-out;
  overflow: hidden;
}

@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-header {
  background: linear-gradient(135deg, #5c44b8 0%, #3d2b94 100%);
  color: white;
  padding: 20px 25px;
  border: none;
}

.modal-header .btn-close {
  color: white;
  box-shadow: none;
  opacity: 0.8;
  transition: all 0.3s ease;
  background: transparent
    url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e")
    center/1em auto no-repeat;
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  padding: 12px;
  margin: 0;
}

.modal-header .btn-close:hover {
  opacity: 1;
  transform: translateY(-50%) rotate(90deg);
}

.modal-header .btn-close:focus {
  box-shadow: none;
  opacity: 1;
}

.modal-title {
  font-weight: 600;
  display: flex;
  align-items: center;
}

.modal-title i {
  margin-right: 12px;
  font-size: 20px;
}

.modal-body {
  padding: 25px;
}

.modal-footer {
  border-top: none;
  padding: 20px 25px;
  justify-content: flex-end;
  gap: 15px;
}

.modal-footer .btn {
  min-width: 120px;
}

/* Form elements in modals */
.modal .form-label {
  font-weight: 500;
  margin-bottom: 8px;
  color: #444;
}

.modal .form-text {
  color: #888;
  font-size: 12px;
  margin-top: 5px;
}

.modal .input-group-text {
  background-color: #f5f5f5;
  border-color: #ddd;
  color: #666;
}

/* Crypto select dropdown */
.select-crypto .form-select {
  border-radius: 10px;
  padding: 12px 20px;
  font-size: 15px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background-color: rgba(255, 255, 255, 0.9);
  color: #444;
  font-weight: 500;
  min-width: 200px;
  box-shadow: none;
  transition: all 0.3s ease;
}

.select-crypto .form-select:focus {
  border-color: #6bca16;
  box-shadow: none;
  outline: none;
}

.select-crypto .form-select:hover {
  background-color: rgba(255, 255, 255, 1);
}

.crypto-select {
  position: relative;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236bca16' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 15px center;
  background-size: 16px;
  padding-right: 40px !important;
  appearance: none;
}

/* Clean form inputs */
.clean-input {
  border-width: 2px !important;
  border-color: #e0e6ed !important;
  border-radius: 8px !important;
  padding: 12px 15px !important;
  font-size: 14px !important;
  background-color: #fff !important;
  color: #444 !important;
  transition: all 0.3s ease !important;
  box-shadow: none !important;
}

.clean-input:focus {
  border-color: #6bca16 !important;
  box-shadow: none !important;
  outline: none !important;
  transform: none !important;
}

.clean-input::placeholder {
  color: #aab7c4 !important;
  opacity: 0.8 !important;
}

.clean-input:focus::placeholder {
  opacity: 0.5 !important;
}

.input-group:focus-within .input-group-text {
  border-color: #6bca16 !important;
  background-color: #f8fcf3 !important;
  color: #6bca16 !important;
  box-shadow: none !important;
}

.input-group-text {
  background-color: #f8f9fa !important;
  border: 1px solid #e0e6ed !important;
  color: #6c757d !important;
  transition: all 0.3s ease !important;
}

/* Single-line logout in sidebar */
.sidebar-footer {
  padding: 15px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-footer a.logout-link {
  color: #b9bdc5;
  text-decoration: none;
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-radius: 6px;
  transition: all 0.3s ease;
  background-color: rgba(0, 0, 0, 0.2);
}

.sidebar-footer a.logout-link i {
  margin-right: 8px;
  font-size: 16px;
}

.sidebar-footer a.logout-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.sidebar-footer a.logout-link:hover i {
  transform: none;
  color: #6bca16;
}

/* Disable all card and container bounce effects */
.card:hover,
.stats-card:hover,
.dashboard-stat-card:hover,
.upload-area:hover,
.upload-dropzone:hover {
  transform: none !important;
  box-shadow: none !important;
}

/* Remove all hover transform effects */
.login-card:hover,
.logo:hover,
.user-profile:hover img,
.page-item .page-link:hover,
.upload-box:hover,
.btn-cancel:hover,
.btn-submit:hover,
.btn-upload:hover,
.btn-success:hover,
.btn-primary:hover,
.btn-info:hover,
.btn-danger:hover,
.btn-warning:hover {
  transform: none !important;
  box-shadow: none !important;
}

/* Remove transform from sidebar icon hover */
#sidebar ul li a:hover i {
  transform: none;
  color: #6bca16;
}

/* Fix page link hover */
.page-item.active .page-link {
  background-color: #6bca16;
  border-color: #6bca16;
  color: white;
  font-weight: 500;
  transform: none;
  box-shadow: none;
}

/* Layout for crypto header section */
.content-header .d-flex.align-items-center {
  gap: 15px;
  box-shadow: none !important;
}

/* Ensure button containers are clean */
.btn-add-container,
.content-header .d-flex.align-items-center > div,
.content-header .d-flex.align-items-center > button {
  box-shadow: none !important;
}

/* Final fix for crypto select and button appearance */
.content-header .d-flex.align-items-center {
  gap: 15px;
  box-shadow: none !important;
}

/* Thicker borders for all form fields */
input.form-control,
select.form-select,
textarea.form-control,
.input-group,
.clean-input,
.form-select {
  border-width: 2px !important;
  border-color: #e0e6ed !important;
}

.input-group .form-control,
.input-group .form-select,
.input-group-text {
  border-width: 2px !important;
}

.input-group-text {
  border-right: none !important;
}

.input-group .form-control,
.input-group .form-select {
  border-left: none !important;
}

/* Focus states with thicker borders */
input.form-control:focus,
select.form-select:focus,
textarea.form-control:focus,
.clean-input:focus {
  border-width: 2px !important;
  border-color: #6bca16 !important;
}

.input-group:focus-within .input-group-text {
  border-width: 2px !important;
  border-color: #6bca16 !important;
}

.input-group:focus-within .form-control,
.input-group:focus-within .form-select {
  border-width: 2px !important;
  border-color: #6bca16 !important;
}

/* Consistent border radius for all form fields */
input.form-control,
select.form-select,
textarea.form-control,
.clean-input,
.form-select,
.input-group,
.input-group-text,
.input-group .form-control,
.input-group .form-select {
  border-radius: 8px !important;
}

/* Proper border radius for input groups */
.input-group .input-group-text {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  border-top-left-radius: 8px !important;
  border-bottom-left-radius: 8px !important;
}

.input-group .form-control,
.input-group .form-select {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  border-top-right-radius: 8px !important;
  border-bottom-right-radius: 8px !important;
}

/* User Profile Detail Page Styles */
.user-profile-container {
  padding: 20px;
  background-color: #f8f9fd;
}

.user-profile-container .row {
  display: flex;
  flex-wrap: wrap;
}

.user-profile-container .col-md-6 {
  display: flex;
  flex-direction: column;
}

/* Set both columns to have the same height */
.user-profile-container .col-md-6:first-child .profile-card,
.user-profile-container .col-md-6:last-child .user-lists {
  height: 100%;
}

.profile-card {
  background-color: #fff;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  border: none;
}

.profile-header {
  display: flex;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
  margin-bottom: 20px;
  gap: 30px;
}

.profile-avatar-container {
  flex-shrink: 0;
}

.profile-avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #f0f4ff;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.profile-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.profile-info {
  flex-grow: 1;
}

.profile-name {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.profile-join-date {
  font-size: 14px;
  color: #888;
  margin-bottom: 15px;
}

.profile-stats {
  display: flex;
  gap: 25px;
}

.stat-item {
  display: flex;
  align-items: baseline;
  gap: 8px;
  background-color: #f8f9fd;
  padding: 8px 15px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background-color: #f0f4ff;
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(92, 68, 184, 0.1);
}

.stat-count {
  font-size: 18px;
  font-weight: 600;
  color: #5c44b8;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.user-lists {
  display: flex;
  flex-direction: row;
  gap: 20px;
  height: 100%;
}

.user-list {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.user-list.profile-card {
  display: flex;
  flex-direction: column;
  height: calc(100% - 20px); /* Accounting for margin-bottom */
  padding-bottom: 15px;
}

.user-list-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.user-list-title .list-count {
  background-color: #f0f4ff;
  color: #5c44b8;
  font-size: 14px;
  padding: 4px 12px;
  border-radius: 20px;
  font-weight: 600;
}

.user-list-title::before {
  content: "";
  display: inline-block;
  width: 6px;
  height: 18px;
  background-color: #5c44b8;
  margin-right: 10px;
  border-radius: 3px;
}

.user-list-container {
  flex: 1;
  overflow-y: auto;
  padding-right: 5px;
  min-height: 100px;
  margin-bottom: 0;
  display: flex;
  flex-direction: column;
  max-height: 590px; /* Fixed height to match profile details section */
}

@media (max-width: 1199px) {
  .user-list-container {
    max-height: 650px;
  }
}

@media (max-width: 991px) {
  .user-list-container {
    max-height: 750px;
  }
}

@media (max-width: 768px) {
  .user-list-container {
    max-height: 350px;
  }
}

/* Hide scrollbar by default in browsers that support it */
.user-list-container::-webkit-scrollbar {
  width: 6px;
  display: none;
}

.user-list-container:hover::-webkit-scrollbar {
  display: block;
}

.user-list-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.user-list-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

.user-list-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Firefox scrollbar */
.user-list-container {
  scrollbar-width: none; /* Hide scrollbar by default */
}

.user-list-container:hover {
  scrollbar-width: thin; /* Show on hover */
  scrollbar-color: #c1c1c1 #f1f1f1;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-radius: 10px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
  background-color: #f8f9fd;
  border: 1px solid #eaedf5;
  flex-shrink: 0;
}

.user-item:hover {
  background-color: #f0f4ff;
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(92, 68, 184, 0.1);
  border-color: #d8deeb;
}

.user-item:last-child {
  margin-bottom: 0;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 15px;
  border: 2px solid #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.user-item:hover .user-avatar {
  border-color: #5c44b8;
  transform: scale(1.1);
}

.user-name {
  font-weight: 500;
  color: #333;
  transition: color 0.3s ease;
}

.user-item:hover .user-name {
  color: #5c44b8;
}

.profile-details-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.profile-detail-item {
  display: flex;
  margin-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 15px;
}

.profile-detail-item:hover {
  background-color: transparent;
  border-radius: 0;
  padding-left: 0;
}

.profile-detail-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.detail-label {
  width: 150px;
  font-weight: 500;
  color: #666;
}

.detail-value {
  flex: 1;
  color: #333;
}

.user-interests {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.interest-tag {
  background-color: #f0f4ff;
  color: #5c44b8;
  font-size: 12px;
  padding: 4px 10px;
  border-radius: 15px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.interest-tag:hover {
  background-color: #5c44b8;
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 3px 8px rgba(92, 68, 184, 0.2);
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #fff;
  background-color: #5c44b8;
  text-decoration: none;
  font-weight: 500;
  padding: 10px 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 3px 10px rgba(92, 68, 184, 0.2);
}

.back-button:hover {
  background-color: #4a3699;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(92, 68, 184, 0.3);
}

.back-button i {
  font-size: 14px;
  transition: transform 0.3s ease;
}

.back-button:hover i {
  transform: translateX(-3px);
}

/* Enhanced dashboard statistics hover effects */
.dashboard-stat-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.dashboard-stat-card:hover .dashboard-stat-value {
  color: #5c44b8;
  transform: scale(1.05);
}

.dashboard-stat-card:hover .dashboard-stat-icon {
  transform: scale(1.1) rotate(5deg);
  color: #5c44b8;
}

/* Content header with back button styling */
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.content-header h1 {
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .profile-header {
    flex-direction: column;
    align-items: center;
    gap: 15px;
    text-align: center;
  }

  .profile-stats {
    justify-content: center;
  }

  .user-lists {
    margin-top: 20px;
  }

  .detail-label,
  .detail-value {
    width: 100%;
  }

  .profile-detail-item {
    flex-direction: column;
    gap: 5px;
  }

  .content-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .back-button {
    margin-top: 0;
  }
}

/* Add a fill element to push content to the top */
.user-list-container::after {
  content: "";
  flex: 1;
  min-height: 10px;
}

/* Enhanced Dashboard Statistics Styles - Updated with unique colors */
.dashboard-stat-card {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
  background: white;
  position: relative;
  height: 100%;
  padding: 30px;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  z-index: 1;
  border: none;
}

.dashboard-stat-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  z-index: 2;
}

/* Create a pseudo-element for hover glow effect */
.dashboard-stat-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at top right,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  opacity: 0;
  transition: opacity 0.5s ease, transform 0.5s ease;
  pointer-events: none;
  z-index: 0;
  transform: translateX(-100%);
}

.dashboard-stat-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.dashboard-stat-card:hover::after {
  opacity: 0.8;
  transform: translateX(0);
}

.dashboard-stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: #444;
  margin-bottom: 0.8rem;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
  transform-origin: left;
  display: inline-block;
}

.dashboard-stat-card:hover .dashboard-stat-value {
  transform: scale(1.08);
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

.dashboard-stat-icon {
  position: absolute;
  right: 25px;
  top: 20px;
  font-size: 3rem;
  transition: all 0.4s ease;
  z-index: 2;
  opacity: 0.7;
  filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.05));
}

.dashboard-stat-card:hover .dashboard-stat-icon {
  transform: scale(1.3) rotate(15deg);
  opacity: 0.9;
  filter: drop-shadow(0 0 10px rgba(0, 0, 0, 0.1));
}

/* Unique color schemes for dashboard user cards */
/* Total Users - Blue theme */
.dashboard-stat-card.users::before {
  background: linear-gradient(180deg, #2563eb, #1e40af);
}

.dashboard-stat-card.users .dashboard-stat-icon,
.dashboard-stat-card.users .dashboard-stat-value {
  color: #3b82f6;
}

.dashboard-stat-card.users:hover .dashboard-stat-icon,
.dashboard-stat-card.users:hover .dashboard-stat-value {
  color: #2563eb;
}

/* Active Users - Green theme */
.dashboard-stat-card.active-users::before {
  background: linear-gradient(180deg, #059669, #047857);
}

.dashboard-stat-card.active-users .dashboard-stat-icon,
.dashboard-stat-card.active-users .dashboard-stat-value {
  color: #10b981;
}

.dashboard-stat-card.active-users:hover .dashboard-stat-icon,
.dashboard-stat-card.active-users:hover .dashboard-stat-value {
  color: #059669;
}

/* Inactive Users - Orange theme */
.dashboard-stat-card.inactive-users::before {
  background: linear-gradient(180deg, #d97706, #b45309);
}

.dashboard-stat-card.inactive-users .dashboard-stat-icon,
.dashboard-stat-card.inactive-users .dashboard-stat-value {
  color: #f59e0b;
}

.dashboard-stat-card.inactive-users:hover .dashboard-stat-icon,
.dashboard-stat-card.inactive-users:hover .dashboard-stat-value {
  color: #d97706;
}

/* Web Users - Purple theme */
.dashboard-stat-card.web-users::before {
  background: linear-gradient(180deg, #7c3aed, #5b21b6);
}

.dashboard-stat-card.web-users .dashboard-stat-icon,
.dashboard-stat-card.web-users .dashboard-stat-value {
  color: #8b5cf6;
}

.dashboard-stat-card.web-users:hover .dashboard-stat-icon,
.dashboard-stat-card.web-users:hover .dashboard-stat-value {
  color: #7c3aed;
}

/* Other card styles (for games, interests, etc.) remain unchanged */
.dashboard-stat-card.interests::before {
  background: linear-gradient(180deg, #1cc88a, #169a6f);
}

.dashboard-stat-card.interests .dashboard-stat-icon,
.dashboard-stat-card.interests .dashboard-stat-value {
  color: #1cc88a;
}

.dashboard-stat-card.games::before {
  background: linear-gradient(180deg, #f6c23e, #dda20a);
}

.dashboard-stat-card.games .dashboard-stat-icon,
.dashboard-stat-card.games .dashboard-stat-value {
  color: #f6c23e;
}

.dashboard-stat-card.stocks::before {
  background: linear-gradient(180deg, #e74a3b, #c0392b);
}

.dashboard-stat-card.stocks .dashboard-stat-icon,
.dashboard-stat-card.stocks .dashboard-stat-value {
  color: #e74a3b;
}

.dashboard-stat-card.ideas::before {
  background: linear-gradient(180deg, #36b9cc, #258391);
}

.dashboard-stat-card.ideas .dashboard-stat-icon,
.dashboard-stat-card.ideas .dashboard-stat-value {
  color: #36b9cc;
}

.dashboard-stat-card.crypto::before {
  background: linear-gradient(180deg, #8e44ad, #6c3483);
}

.dashboard-stat-card.crypto .dashboard-stat-icon,
.dashboard-stat-card.crypto .dashboard-stat-value {
  color: #8e44ad;
}

/* Add hover effect on card headers and icon colors */
.dashboard-stat-card h3 {
  color: #444;
  font-size: 1.2rem; /* Increased from 0.9rem */
  font-weight: 700;
  text-transform: uppercase;
  margin-bottom: 1rem;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
}

/* More vibrant hover effects for titles */
.dashboard-stat-card:hover h3 {
  transform: translateX(5px);
  color: #333;
  font-weight: 800;
  font-size: 1.25rem; /* Slightly larger on hover */
}

/* Additional hover effects for specific cards */
.dashboard-stat-card.users:hover h3 {
  color: #2563eb;
}

.dashboard-stat-card.active-users:hover h3 {
  color: #059669;
}

.dashboard-stat-card.inactive-users:hover h3 {
  color: #d97706;
}

.dashboard-stat-card.web-users:hover h3 {
  color: #7c3aed;
}

.dashboard-stat-card.interests:hover h3 {
  color: #18d47e;
}

.dashboard-stat-card.games:hover h3 {
  color: #ffba00;
}

.dashboard-stat-card.stocks:hover h3 {
  color: #ff3b2a;
}

.dashboard-stat-card.ideas:hover h3 {
  color: #25d5eb;
}

.dashboard-stat-card.crypto:hover h3 {
  color: #a347d5;
}

/* Validation styling */
.form-control.is-invalid,
.form-select.is-invalid {
  border-color: #dc3545;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #dc3545;
}

.form-control.is-invalid ~ .invalid-feedback,
.form-select.is-invalid ~ .invalid-feedback {
  display: block;
}

/* Add Game Form Styles */
.add-game-form {
  max-width: 800px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
}

.add-game-form .row {
  margin-bottom: 15px;
}

.add-game-form .form-label {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.add-game-form .form-control,
.add-game-form .form-select {
  border: 2px solid #e0e6ed;
  border-radius: 8px;
  padding: 12px 15px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.add-game-form .form-control:focus,
.add-game-form .form-select:focus {
  border-color: #6bca16;
  box-shadow: 0 0 0 0.25rem rgba(107, 202, 22, 0.1);
}

.add-game-form .input-group {
  border-radius: 8px;
  overflow: hidden;
}

.add-game-form .input-group-text {
  background-color: #f8f9fd;
  border: 2px solid #e0e6ed;
  border-right: none;
  color: #5c44b8;
  padding: 0 15px;
}

.add-game-form .input-group .form-control {
  border-left: none;
}

.add-game-form .btn-submit {
  background: linear-gradient(135deg, #6bca16 0%, #5c44b8 100%);
  border: none;
  padding: 12px 25px;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  box-shadow: 0 5px 15px rgba(92, 68, 184, 0.2);
  transition: all 0.3s ease;
}

.add-game-form .btn-submit:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(92, 68, 184, 0.3);
}

/* Responsive styles for dashboard stat cards */
@media (max-width: 1200px) {
  .dashboard-stat-card h3 {
    font-size: 1.1rem;
  }
}

@media (max-width: 992px) {
  .dashboard-stat-card h3 {
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .dashboard-stat-card {
    padding: 25px;
  }

  .dashboard-stat-card h3 {
    font-size: 1.1rem;
  }

  .dashboard-stat-value {
    font-size: 2.2rem;
  }
}

@media (max-width: 576px) {
  .dashboard-stat-card h3 {
    font-size: 1rem;
  }

  .dashboard-stat-value {
    font-size: 2rem;
  }
}

/* Content Header with Icons Styling */
.content-header .d-flex.align-items-center {
  display: flex;
  align-items: center;
  gap: 15px;
}

.content-header .d-flex.align-items-center i {
  color: white;
  font-size: 28px;
  transition: all 0.3s ease;
  filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));
}

.content-header .d-flex.align-items-center:hover i {
  transform: scale(1.15);
  filter: drop-shadow(0 3px 8px rgba(0, 0, 0, 0.3));
}

.content-header #page-title {
  margin: 0;
  font-size: 26px;
  font-weight: 600;
  letter-spacing: 0.5px;
  position: relative;
  z-index: 1;
  color: white;
  transition: all 0.3s ease;
}

.content-header .d-flex.align-items-center:hover #page-title {
  transform: translateX(5px);
}

/* Icon styles for different page types */
.content-header.users-header i {
  color: #e0f2ff;
}

.content-header.games-header i {
  color: #fff9e0;
}

.content-header.interests-header i {
  color: #e0ffe8;
}

.content-header.stocks-header i {
  color: #ffe0e0;
}

.content-header.ideas-header i {
  color: #e0f7ff;
}

.content-header.crypto-header i {
  color: #f1e0ff;
}

/* Custom header backgrounds for different page types (optional) */
.content-header.users-header {
  background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
}

.content-header.games-header {
  background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);
}

.content-header.interests-header {
  background: linear-gradient(135deg, #1cc88a 0%, #169a6f 100%);
}

.content-header.stocks-header {
  background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);
}

.content-header.ideas-header {
  background: linear-gradient(135deg, #36b9cc 0%, #258391 100%);
}

.content-header.crypto-header {
  background: linear-gradient(135deg, #8e44ad 0%, #6c3483 100%);
}

/* Web User Details Page Styles */
.btn-back {
  background: linear-gradient(135deg, #f3f4f6 0%, #e0e3ea 100%);
  color: #444;
  border: none;
  padding: 0 28px;
  height: 48px;
  min-width: 170px;
  font-weight: 600;
  font-size: 16px;
  border-radius: 16px;
  box-shadow: 0 2px 10px rgba(120, 120, 120, 0.10);
  transition: background 0.2s, box-shadow 0.2s, color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-right: 0;
  text-decoration: none;
}

.btn-back i {
  font-size: 18px;
  margin-right: 8px;
  color: #7367f0;
}

.btn-back:hover,
.btn-back:focus {
  background: linear-gradient(135deg, #e0e3ea 0%, #cfd2da 100%);
  color: #222;
  box-shadow: 0 4px 16px rgba(120, 120, 120, 0.16);
  text-decoration: none;
}

.btn-back:active {
  background: linear-gradient(135deg, #e0e3ea 0%, #cfd2da 100%);
  color: #222;
  box-shadow: 0 2px 8px rgba(120, 120, 120, 0.12);
}

/* Tabs Styling */
.nav-tabs {
  border-bottom: 2px solid #dee2e6;
}

.nav-tabs .nav-link {
  border: none;
  color: #6c757d;
  font-weight: 500;
  padding: 1rem 1.5rem;
  margin-right: 0.5rem;
  transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
  border: none;
  color: #7367f0;
}

.nav-tabs .nav-link.active {
  border: none;
  color: #7367f0;
  position: relative;
}

.nav-tabs .nav-link.active::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #7367f0;
}

/* Profile Settings Styles */
.profile-settings {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem 0;
}

.profile-settings h2 {
  color: #5e5873;
  font-weight: 500;
}

.profile-settings .form-label {
  color: #6e6b7b;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.profile-settings .form-control {
  background-color: #f8f8f8;
  border: 1px solid #ddd;
  padding: 0.75rem 1rem;
}

.profile-settings .form-control:read-only {
  background-color: #f8f8f8;
  cursor: not-allowed;
}

/* Table Styles for Referral and Reward tabs */
.tab-content .table {
  margin-bottom: 0;
}

.tab-content .table th {
  background-color: #f8f8f8;
  color: #5e5873;
  font-weight: 600;
  border-bottom: 2px solid #dee2e6;
}

.tab-content .table td {
  color: #6e6b7b;
  vertical-align: middle;
}

.tab-content .badge {
  padding: 0.5em 0.75em;
  font-weight: 500;
}

/* Card Styles */
.card {
  border: none;
  box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.1);
  border-radius: 0.428rem;
}

.card-body {
  padding: 2rem;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .nav-tabs .nav-link {
    padding: 0.75rem 1rem;
  }

  .card-body {
    padding: 1.5rem;
  }

  .profile-settings {
    padding: 1rem 0;
  }
}

/* Custom Table Styles for Web User Details */
.custom-table {
  border-collapse: separate;
  border-spacing: 0 8px;
  margin-top: -8px;
}

.custom-table thead th {
  background-color: #f8f9fd;
  border: none;
  color: #5e5873;
  font-weight: 600;
  padding: 1rem 1.5rem;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 0.5px;
}

.custom-table tbody tr {
  background-color: white;
  box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.05);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.custom-table tbody tr:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px 0 rgba(34, 41, 47, 0.1);
}

.custom-table tbody td {
  border: none;
  padding: 1.25rem 1.5rem;
  vertical-align: middle;
  color: #6e6b7b;
}

.custom-table tbody tr td:first-child {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.custom-table tbody tr td:last-child {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}

/* User Avatar in Table */
.custom-table .user-avatar {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #fff;
  box-shadow: 0 2px 8px rgba(34, 41, 47, 0.1);
}

/* Status Badge */
.badge.bg-success-light {
  background-color: rgba(40, 199, 111, 0.12) !important;
  color: #28c76f !important;
  font-weight: 500;
  padding: 0.5rem 0.9rem;
  border-radius: 6px;
}

/* Points Badge */
.points-badge {
  background-color: rgba(115, 103, 240, 0.12);
  color: #7367f0;
  padding: 0.5rem 0.9rem;
  border-radius: 6px;
  font-weight: 600;
}

/* Tab Content Container */
.tab-content {
  padding: 1.5rem 0;
}

/* Responsive styles for tables */
@media (max-width: 768px) {
  .custom-table thead th {
    padding: 1rem;
    font-size: 0.8rem;
  }

  .custom-table tbody td {
    padding: 1rem;
  }

  .custom-table .user-avatar {
    width: 32px;
    height: 32px;
  }
}

/* Updated Web User Details Styles */
.nav-tabs {
  border-bottom: 1px solid #dee2e6;
  gap: 2rem;
  padding-left: 1rem;
  display: flex;
}

.nav-tabs .nav-link {
  border: none;
  color: #6e6b7b;
  font-weight: 500;
  padding: 1rem 0;
  margin-right: 0;
  position: relative;
  background: transparent;
}

.nav-tabs .nav-link:hover {
  border: none;
  color: #7367f0;
}

.nav-tabs .nav-link.active {
  border: none;
  color: #7367f0;
  background: transparent;
}

.nav-tabs .nav-link.active::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #7367f0;
}

.custom-table {
  border-collapse: separate;
  border-spacing: 0;
  margin-top: 0;
}

.custom-table thead th {
  background-color: #7367f0;
  border: none;
  color: #fff;
  font-weight: 500;
  padding: 1rem 1.5rem;
  font-size: 0.9rem;
  white-space: nowrap;
}

.custom-table thead th:first-child {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.custom-table thead th:last-child {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}

.custom-table tbody tr {
  background-color: white;
  border-bottom: 1px solid #ebe9f1;
}

.custom-table tbody tr:last-child {
  border-bottom: none;
}

.custom-table tbody td {
  border: none;
  padding: 1.25rem 1.5rem;
  vertical-align: middle;
  color: #6e6b7b;
}

/* Updated Web User Details Table Styles */
.custom-table {
  border-collapse: separate;
  border-spacing: 0;
  margin-top: 0;
}

.custom-table thead th {
  background-color: #7367f0 !important;
  border: none;
  color: #ffffff !important;
  font-weight: 500;
  padding: 1rem 1.5rem;
  font-size: 0.9rem;
  white-space: nowrap;
  transition: none;
}

.custom-table thead th:hover {
  background-color: #7367f0 !important;
  cursor: default;
  color: #ffffff !important;
}

.custom-table tbody tr {
  background-color: white;
  border-bottom: 1px solid #ebe9f1;
  transition: none;
}

.custom-table tbody tr:hover {
  transform: none;
  box-shadow: none;
}

.custom-table tbody td {
  border: none;
  padding: 1.25rem 1.5rem;
  vertical-align: middle;
  color: #6e6b7b;
}

/* Remove hover effects from table elements */
.custom-table thead th:hover,
.custom-table tbody tr:hover,
.custom-table tbody td:hover {
  transform: none;
  box-shadow: none;
}

/* Points Badge */
.points-badge {
  background-color: rgba(115, 103, 240, 0.12);
  color: #7367f0;
  padding: 0.5rem 0.9rem;
  border-radius: 6px;
  font-weight: 500;
}

/* Updated Tab Navigation Styles */
.nav-tabs {
  border-bottom: 1px solid #dee2e6;
  display: flex;
  width: 100%;
  padding: 0;
  margin-bottom: 1.5rem;
}

.nav-tabs .nav-item {
  flex: 1;
  text-align: center;
}

.nav-tabs .nav-link {
  border: none;
  color: #6e6b7b;
  font-weight: 500;
  padding: 1rem;
  margin: 0;
  position: relative;
  background: transparent;
  width: 100%;
}

.nav-tabs .nav-link:hover {
  border: none;
  color: #7367f0;
}

.nav-tabs .nav-link.active {
  border: none;
  color: #7367f0;
  background: transparent;
}

.nav-tabs .nav-link.active::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #7367f0;
}

/* Ensure tab content has proper spacing */
.tab-content {
  padding-top: 1.5rem;
}

/* Web User Details Three Column Layout */
.card {
  border: none;
  box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.1);
  height: 100%;
  background: #fff;
  border-radius: 0.5rem;
}

.card-header {
  padding: 1.25rem 1.5rem;
  border-bottom: none;
  border-radius: 0.5rem 0.5rem 0 0;
}

.card-header h5 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.card-header i {
  font-size: 1.1rem;
}

.card-header .badge {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Profile Section */
.profile-settings {
  padding: 1rem;
}

.profile-settings .form-control {
  background-color: #f8f9fa;
  border: 1px solid #e0e6ed;
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  color: #5e5873;
}

.profile-settings .form-label {
  font-size: 0.95rem;
  color: #5e5873;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.profile-settings .btn {
  padding: 0.75rem 1rem;
  font-weight: 500;
}

/* Table Styles */
.custom-table {
  margin-bottom: 0;
}

.custom-table thead th {
  background-color: #7367f0;
  color: #fff;
  font-weight: 500;
  padding: 1rem 1.25rem;
  font-size: 0.95rem;
  white-space: nowrap;
}

.custom-table tbody td {
  padding: 1rem 1.25rem;
  vertical-align: middle;
  color: #6e6b7b;
  font-size: 0.95rem;
  border-bottom: 1px solid #ebe9f1;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.points-badge {
  background-color: #7367f0;
  color: #fff;
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  font-size: 0.875rem;
}

.badge.bg-success-light {
  background-color: rgba(40, 199, 111, 0.12) !important;
  color: #28c76f !important;
  font-weight: 500;
}

/* Created At Column */
.created-at {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  white-space: nowrap;
}

.created-at .date {
  color: #5e5873;
  font-weight: 600;
  font-size: 0.95rem;
}

.created-at .time {
  color: #7367f0;
  font-size: 0.95rem;
  font-weight: 500;
  position: relative;
  padding-left: 0.75rem;
}

.created-at .time:before {
  content: "•";
  position: absolute;
  left: 0;
  color: #d0d2d6;
}

/* Sticky header for scrollable tables */
.table-responsive thead th {
  position: sticky;
  top: 0;
  z-index: 1;
  background: #7367f0;
}

/* Custom scrollbar styles */
.table-responsive::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.table-responsive::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: #7367f0;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: #5e50ee;
}

/* For Firefox */
.table-responsive {
  scrollbar-width: thin;
  scrollbar-color: #7367f0 #f1f1f1;
}

/* Table hover effects */
.custom-table tbody tr {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.custom-table tbody tr:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 10px rgba(115, 103, 240, 0.1);
  background-color: rgba(115, 103, 240, 0.02);
}

/* Points badge animation */
.points-badge {
  transition: transform 0.2s ease;
}

.points-badge:hover {
  transform: scale(1.1);
}

/* Created at time styling */
.created-at {
  display: flex;
  align-items: center;
  gap: 8px;
}

.created-at .date {
  color: #5e5873;
  font-weight: 500;
}

.created-at .time {
  color: #7367f0;
  position: relative;
  padding-left: 8px;
}

.created-at .time:before {
  content: "•";
  position: absolute;
  left: 0;
  color: #d0d2d6;
}

/* Card body padding fix for tables */
.card-body.p-0 .table {
  margin-bottom: 0;
}

.card-body.p-0 .table td:first-child,
.card-body.p-0 .table th:first-child {
  padding-left: 1.5rem;
}

.card-body.p-0 .table td:last-child,
.card-body.p-0 .table th:last-child {
  padding-right: 1.5rem;
}

/* Table Styles */
.custom-table {
  border-collapse: separate;
  border-spacing: 0;
  margin-top: 0;
  width: 100%;
}

.custom-table thead {
  background-color: #7367f0;
}

.custom-table thead th {
  background-color: #7367f0 !important;
  border: none;
  color: #ffffff !important;
  font-weight: 500;
  padding: 0.875rem;
  font-size: 0.9rem;
  white-space: nowrap;
  transition: none;
  position: sticky;
  top: 0;
  z-index: 2;
}

/* Optimize column widths */
.custom-table th:first-child {
  width: 50px; /* For No. column */
}

.custom-table td:first-child {
  padding-right: 0.5rem;
}

/* Username and avatar column */
.custom-table td:has(.d-flex.align-items-center) {
  padding-right: 1rem; /* Reduced from 2.5rem */
  min-width: 180px; /* Fixed width for username column */
}

/* Email column */
.custom-table td:nth-child(2) {
  padding-right: 0.75rem;
  max-width: 200px; /* Limit email width */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Status/Points column */
.custom-table td:last-child {
  padding-left: 0.5rem;
  width: 100px; /* Fixed width for status/points */
  text-align: center;
}

/* Table row hover effects */
.custom-table tbody tr {
  background-color: white;
  border-bottom: 1px solid #ebe9f1;
  transition: all 0.2s ease;
}

.custom-table tbody tr:hover {
  background-color: #f8f8ff;
  transform: translateY(-2px);
  box-shadow: 0 3px 10px rgba(115, 103, 240, 0.1);
  border-radius: 4px;
}

.custom-table tbody tr:hover td:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.custom-table tbody tr:hover td:last-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

/* Enhanced hover effects for interactive elements */
.custom-table tbody tr:hover .user-avatar {
  transform: scale(1.05);
  box-shadow: 0 3px 8px rgba(115, 103, 240, 0.2);
}

.custom-table tbody tr:hover .badge.bg-success-light {
  background-color: rgba(40, 199, 111, 0.2) !important;
  transform: scale(1.05);
}

.custom-table tbody tr:hover .points-badge {
  background-color: rgba(115, 103, 240, 0.2);
  transform: scale(1.05);
}

/* Table container */
.table-responsive {
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden; /* Hide horizontal scroll */
  border-radius: 8px;
  padding-right: 1px; /* Prevent content shift when scrollbar appears */
}

/* User info layout */
.d-flex.align-items-center {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  width: 38px;
  height: 38px;
  min-width: 38px;
  border-radius: 50%;
  object-fit: cover;
  transition: all 0.2s ease;
}

/* Status badge */
.badge.bg-success-light {
  background-color: rgba(40, 199, 111, 0.12) !important;
  color: #28c76f !important;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

/* Points badge */
.points-badge {
  background-color: rgba(115, 103, 240, 0.12);
  color: #7367f0;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: inline-block;
}

/* Post Content Image Styles */
.post-content img {
  width: 100%;
  height: auto;
  max-height: 400px;
  object-fit: cover;
  border-radius: 0;
  margin: 0;
  display: block;
}

/* Comment Styles */
.comment-item {
  padding: 15px;
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s;
  margin-bottom: 15px;
}

.comment-item:hover {
  background-color: #f8f9fa;
}

/* Comment actions for main comments - improved */
.comment-item .comment-actions {
  display: flex;
  align-items: center;
  margin-top: 10px;
  gap: 12px;
}

.comment-item .btn-link {
  padding: 6px 12px;
  border-radius: 20px;
  transition: all 0.2s ease;
  background-color: transparent;
  display: flex;
  align-items: center;
}

.comment-item .btn-link:hover {
  background-color: rgba(108, 92, 231, 0.05);
  box-shadow: none;
}

.comment-item .delete-btn {
  padding: 6px 12px;
  border-radius: 20px;
  background-color: transparent;
  transition: all 0.2s ease;
}

.comment-item .delete-btn:hover {
  background-color: rgba(220, 53, 69, 0.05);
  box-shadow: none;
}

/* Enhanced sub-comment styles */
.sub-comment-item {
  padding: 12px;
  transition: background-color 0.2s;
  overflow: visible;
  border-radius: 12px;
  margin-bottom: 10px;
  background-color: #f8f8ff;
}

.sub-comment-item:hover {
  background-color: #f0f0ff;
}

.sub-comment-item .user-avatar {
  width: 32px;
  height: 32px;
  border: 2px solid #fff;
  box-shadow: 0 2px 5px rgba(108, 92, 231, 0.2);
}

/* Improved sub-comments container */
.sub-comments {
  margin-left: 20px;
  padding-left: 15px;
  border-left: 2px solid #e9ecef;
  overflow: visible;
  margin-top: 15px;
  padding-top: 5px;
}

.sub-comment-item .comment-content {
  flex-grow: 1;
}

.sub-comment-item .comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}

.sub-comment-item .comment-header h6 {
  margin: 0;
  font-size: 14px;
  color: #5e5873;
  font-weight: 600;
}

.sub-comment-item .comment-header .created-at {
  margin-left: 8px;
  font-size: 12px;
  color: #7367f0;
}

.sub-comment-item .comment-text {
  font-size: 14px;
  color: #6e6b7b;
  line-height: 1.4;
}

.sub-comment-item .comment-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
  flex-wrap: nowrap;
  overflow: visible;
}

.sub-comment-item .comment-actions button {
  background: none;
  border: none;
  color: #7367f0;
  font-size: 13px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.sub-comment-item .comment-actions button:hover {
  background: rgba(115, 103, 240, 0.1);
  color: #5e50ee;
}

.sub-comment-item .comment-actions button i {
  font-size: 14px;
}

.sub-comment-item .delete-btn {
  color: #dc3545;
  font-size: 13px;
  margin-left: 0;
  padding: 4px 8px;
}

.sub-comment-item .delete-btn:hover {
  background: rgba(220, 53, 69, 0.1);
  color: #c82333;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.created-at {
  font-size: 0.875rem;
  color: #6c757d;
}

.created-at .date {
  margin-right: 0.5rem;
}

.comment-actions button {
  color: #6c757d;
  text-decoration: none;
}

.comment-actions button:hover {
  color: #0d6efd;
}

.sub-comments {
  margin-left: 2.5rem;
  padding-left: 1rem;
  border-left: 2px solid #e9ecef;
}

.sub-comment-item {
  padding: 0.5rem 0;
}

.sub-comment-item .btn-link {
  text-decoration: none;
}

.sub-comment-item .btn-link:hover {
  text-decoration: none;
}

/* New styles for comments container */
.comments-container {
  padding: 1rem;
}

.comments-container::-webkit-scrollbar {
  width: 6px;
}

.comments-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.comments-container::-webkit-scrollbar-thumb {
  background: #6f42c1;
  border-radius: 3px;
}

.comments-container::-webkit-scrollbar-thumb:hover {
  background: #5a32a3;
}

.comment-item {
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-item:hover {
  background-color: #f8f9fa;
}

.delete-btn {
  color: #dc3545;
  text-decoration: none;
  font-size: 0.875rem;
}

.delete-btn:hover {
  color: #bb2d3b;
}

/* Enhanced Referral and Reward Sections */
.card.h-100 {
  box-shadow: 0 6px 32px rgba(115, 103, 240, 0.1),
    0 1.5px 6px rgba(115, 103, 240, 0.08);
  border-radius: 18px;
  border: 1.5px solid #ede7f6;
  background: #fff;
  padding: 0.5rem 0 1.5rem 0;
}
.card-header {
  border-radius: 18px 18px 0 0;
  background: linear-gradient(90deg, #7367f0 0%, #a18fff 100%);
  color: #fff;
  font-weight: 600;
  font-size: 1.15rem;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(115, 103, 240, 0.08);
  border-bottom: 1.5px solid #ede7f6;
  padding: 1.25rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.card-header.bg-success {
  background: linear-gradient(90deg, #28c76f 0%, #81fbb8 100%);
  color: #fff;
}
.card-header.bg-info {
  background: linear-gradient(90deg, #00cfe8 0%, #7367f0 100%);
  color: #fff;
}
.card-body.p-0 {
  padding: 0 1.5rem 1.5rem 1.5rem;
}
.table-responsive {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(115, 103, 240, 0.06);
  background: #fafaff;
}
.custom-table th,
.custom-table td {
  border: none;
  background: transparent;
}
.custom-table thead th {
  background: #ede7f6 !important;
  color: #5e5873 !important;
  font-weight: 700;
  font-size: 1rem;
  letter-spacing: 0.5px;
  border-radius: 0;
}
.custom-table tbody tr {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 1px 4px rgba(115, 103, 240, 0.04);
  transition: box-shadow 0.2s;
}
.custom-table tbody tr:hover {
  box-shadow: 0 4px 16px rgba(115, 103, 240, 0.1);
  background: #f6f5ff;
}
.custom-table td {
  font-size: 0.97rem;
  color: #6e6b7b;
  padding: 1rem 1.25rem;
}
.custom-table .user-avatar {
  border: 2px solid #7367f0;
  box-shadow: 0 1px 4px rgba(115, 103, 240, 0.08);
}
.badge.bg-success-light {
  background: linear-gradient(90deg, #e0ffe8 0%, #b2f7cc 100%) !important;
  color: #28c76f !important;
  font-weight: 600;
  border-radius: 8px;
  font-size: 0.95rem;
}
.points-badge {
  background: linear-gradient(90deg, #ede7f6 0%, #d1c4e9 100%);
  color: #7367f0;
  font-weight: 700;
  border-radius: 8px;
  font-size: 0.95rem;
  padding: 0.5rem 1rem;
  box-shadow: 0 1px 4px rgba(115, 103, 240, 0.08);
}

/* Profile Details Section Enhancements */
.profile-details-card {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px rgba(115, 103, 240, 0.06);
  border: 1.5px solid #ede7f6;
  margin-bottom: 0.5rem;
}
.profile-detail-label {
  font-size: 0.92rem;
  color: #7367f0;
  font-weight: 600;
  letter-spacing: 0.2px;
  text-transform: uppercase;
}
.profile-detail-item {
  min-width: 120px;
  padding: 0.5rem 0;
}
.profile-detail-separator {
  width: 3px;
  height: 44px;
  background: #7367f0;
  border-radius: 2px;
  box-shadow: 0 0 4px #7367f0aa;
  margin: 0 1.2rem;
  align-self: center;
}
@media (max-width: 767.98px) {
  .profile-detail-separator {
    width: 80%;
    height: 3px;
    background: #7367f0;
    border-radius: 2px;
    margin: 0.7rem auto;
    box-shadow: 0 0 4px #7367f0aa;
  }
}
/* Prevent wrapping and add spacing in referral users table */
.nowrap-username {
  white-space: nowrap;
  font-weight: 500;
  font-size: 1rem;
  color: #5e5873;
}
.nowrap-email {
  white-space: nowrap;
  padding-left: 2.5rem !important;
  font-size: 0.97rem;
  color: #7367f0;
}

/* Fix sticky table header corners for custom-table */
.custom-table thead th:first-child {
  border-top-left-radius: 12px;
  background: #ede7f6 !important;
  position: sticky;
  left: 0;
  z-index: 2;
}
.custom-table thead th:last-child {
  border-top-right-radius: 12px;
  background: #ede7f6 !important;
  position: sticky;
  right: 0;
  z-index: 2;
}
/* Ensure sticky header background covers full width */
.custom-table thead th {
  background: #ede7f6 !important;
}

/* Modern Profile Details Section Styles */
.profile-details-card-modern {
  background: linear-gradient(135deg, #ede7f6 0%, #fff 100%);
  border-radius: 18px;
  box-shadow: 0 4px 24px 0 rgba(115, 103, 240, 0.08);
  border: 1.5px solid #ede7f6;
  margin-bottom: 0.5rem;
  padding: 0;
}
.profile-detail-modern {
  min-width: 160px;
  padding: 1.2rem 0.5rem;
}
.profile-detail-icon {
  font-size: 2.1rem;
  color: #7367f0;
  background: #f3f0fa;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(115, 103, 240, 0.08);
}
.profile-detail-label-modern {
  font-size: 0.95rem;
  color: #7367f0;
  font-weight: 700;
  letter-spacing: 0.5px;
  margin-bottom: 0.2rem;
  text-transform: uppercase;
}
.profile-detail-value-modern {
  font-size: 1.13rem;
  font-weight: 600;
  color: #5e5873;
  word-break: break-all;
}
.profile-detail-modern-separator {
  width: 2px;
  height: 60px;
  background: linear-gradient(180deg, #b2b2f7 0%, #7367f0 100%);
  border-radius: 2px;
  margin: 0 1.5rem;
  align-self: center;
}
@media (max-width: 767.98px) {
  .profile-details-card-modern {
    padding: 0;
  }
  .profile-detail-modern-separator {
    width: 80%;
    height: 2px;
    background: linear-gradient(90deg, #b2b2f7 0%, #7367f0 100%);
    margin: 0.7rem auto;
  }
  .profile-detail-modern {
    min-width: 0;
    padding: 0.8rem 0;
  }
  .profile-detail-icon {
    font-size: 1.6rem;
    width: 36px;
    height: 36px;
  }
}

/* Fix comment header layout issues */
.created-at {
  display: inline-flex;
  align-items: center;
  color: #6c757d;
  font-size: 0.85rem;
  white-space: nowrap;
  margin-left: 8px;
}

.created-at .date {
  margin-right: 3px;
}

.created-at .time {
  color: #6c757d;
}

.created-at .time:before {
  content: "•";
  margin: 0 4px;
  opacity: 0.7;
}

/* Comment item styles */
.comment-item {
  padding: 15px;
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s;
}

.comment-item:hover {
  background-color: #f8f9fa;
}

.comment-item .user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.comment-item:hover .user-avatar {
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

/* Fix for the username and timestamp alignment */
.comment-item .d-flex.align-items-center,
.sub-comment-item .d-flex.align-items-center {
  flex-wrap: nowrap !important;
}

.comment-item .d-flex.align-items-center .me-auto,
.sub-comment-item .d-flex.align-items-center .me-auto {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60%;
}

/* Sub-comments styles */
.sub-comments {
  margin-left: 20px;
  padding-left: 15px;
  border-left: 2px solid #e9ecef;
  overflow: visible;
}

.sub-comment-item {
  padding: 10px 0;
  transition: background-color 0.2s;
  overflow: visible;
}

.sub-comment-item:hover {
  background-color: #f8f9fa;
}

.sub-comment-item .user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Fix alignment between fullname and username in comments */
.comment-item .d-flex.align-items-center h6,
.sub-comment-item .d-flex.align-items-center h6 {
  margin-right: 5px;
  margin-bottom: 0;
}

.comment-item .d-flex.align-items-center .text-muted,
.sub-comment-item .d-flex.align-items-center .text-muted {
  margin-right: 5px;
}

.comment-item .d-flex.align-items-center,
.sub-comment-item .d-flex.align-items-center {
  gap: 0;
}

/* Ensure username and fullname stay together */
.comment-item .d-flex.align-items-center,
.sub-comment-item .d-flex.align-items-center {
  display: flex;
  flex-wrap: nowrap;
}

/* Remove margin auto from username to keep it next to fullname */
.comment-item .d-flex.align-items-center h6.mb-0.me-auto,
.sub-comment-item .d-flex.align-items-center h6.mb-0.me-auto {
  margin-right: 5px !important;
}

/* Enhanced created-at date styling */
.created-at {
  display: inline-flex;
  align-items: center;
  font-size: 0.85rem;
  white-space: nowrap;
  margin-left: 8px;
  background-color: #f8f5ff;
  padding: 3px 10px;
  border-radius: 20px;
  color: #6c5ce7;
  font-weight: 500;
  box-shadow: 0 1px 2px rgba(92, 68, 184, 0.08);
  transition: all 0.2s ease;
  border: 1px solid rgba(108, 92, 231, 0.1);
}

.created-at:hover {
  background-color: #f0e9ff;
  box-shadow: 0 2px 4px rgba(92, 68, 184, 0.12);
  transform: translateY(-1px);
}

.created-at .date {
  margin-right: 2px;
  color: #6c5ce7;
  font-weight: 500;
}

.created-at .time {
  color: #8a7eea;
}

.created-at .time:before {
  content: "";
  margin: 0;
}

/* Enhanced heart icon and likes count styling */
.comment-item .btn-link .fa-heart,
.sub-comment-item .btn-link .fa-heart {
  color: #ff5252;
  transition: transform 0.2s ease;
}

.comment-item .btn-link:hover .fa-heart,
.sub-comment-item .btn-link:hover .fa-heart {
  transform: scale(1.15);
  color: #ff1a1a;
}

/* Heart icon specific count color */
.comment-item .btn-link .fa-heart + span,
.sub-comment-item .btn-link .fa-heart + span {
  color: #ff5252;
  font-weight: 600;
}

/* Delete button styling - separate styles for main comments and replies */
/* Main comment delete button - red */
.comment-item > .d-flex > .flex-grow-1 .delete-btn i,
.comment-item > .d-flex > .flex-grow-1 .delete-btn span {
  color: #dc3545 !important;
  transition: all 0.2s ease;
}

.comment-item > .d-flex > .flex-grow-1 .delete-btn:hover i,
.comment-item > .d-flex > .flex-grow-1 .delete-btn:hover span {
  color: #bd2130 !important;
  text-shadow: 0 0 1px rgba(189, 33, 48, 0.3);
  transform: translateY(-1px);
}

/* Reply delete button - purple */
.sub-comment-item .delete-btn i,
.sub-comment-item .delete-btn span {
  color: #6c5ce7 !important;
  transition: all 0.2s ease;
}

.sub-comment-item .delete-btn:hover i,
.sub-comment-item .delete-btn:hover span {
  color: #5c44b8 !important;
  text-shadow: 0 0 1px rgba(92, 68, 184, 0.3);
  transform: translateY(-1px);
}

/* Improved hover effect for delete buttons */
.delete-btn {
  transition: all 0.2s ease;
  position: relative;
  overflow: visible;
}

.delete-btn:hover {
  transform: translateY(-1px);
}

.delete-btn:active {
  transform: translateY(0);
}

/* Enhanced comment icon and count styling */
.comment-item .btn-link .fa-comment,
.sub-comment-item .btn-link .fa-comment {
  color: #6c5ce7;
  transition: transform 0.2s ease;
}

.comment-item .btn-link:hover .fa-comment,
.sub-comment-item .btn-link:hover .fa-comment {
  transform: scale(1.15);
  color: #5c44b8;
}

/* Enhanced count text styling */
.comment-item .btn-link span,
.sub-comment-item .btn-link span {
  font-weight: 500;
  transition: color 0.2s ease;
}

.comment-item .btn-link:hover span,
.sub-comment-item .btn-link:hover span {
  color: #6c5ce7;
}

/* Comment icon specific count color */
.comment-item .btn-link .fa-comment + span,
.sub-comment-item .btn-link .fa-comment + span {
  color: #6c5ce7;
}

/* Enhanced post stats styling */
.post-stats {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.stat-item {
  display: flex;
  align-items: center;
  transition: transform 0.2s ease;
  padding: 3px;
  margin-right: 15px;
  overflow: visible;
  position: relative;
}

.stat-item:hover {
  transform: translateY(-1px);
}

.stat-item .fa-heart {
  color: #ff5252;
  font-size: 1.1rem;
}

.stat-item .fa-comment {
  color: #6c5ce7;
  font-size: 1.1rem;
}

.stat-item .fa-eye {
  color: #8a7eea;
  font-size: 1.1rem;
}

.stat-item span {
  font-weight: 500;
  margin-left: 5px;
  color: #666;
}

.stat-item:hover .fa-heart {
  color: #ff1a1a;
}

.stat-item:hover .fa-comment {
  color: #5c44b8;
}

.stat-item:hover .fa-eye {
  color: #7a6de0;
}

.stat-item:hover span {
  color: #6c5ce7;
}

/* Fix for icons getting cut off on hover */
.comment-item .btn-link,
.sub-comment-item .btn-link {
  overflow: visible;
  padding: 5px;
  margin: -5px;
  position: relative;
  z-index: 1;
}

.comment-item .btn-link .fa-heart,
.sub-comment-item .btn-link .fa-heart,
.comment-item .btn-link .fa-comment,
.sub-comment-item .btn-link .fa-comment {
  display: inline-block;
  transform-origin: center;
  padding: 2px;
}

.comment-actions {
  margin-top: 10px;
  margin-bottom: 5px;
  padding: 3px 0;
}

.stat-item i {
  display: inline-block;
  transform-origin: center;
  padding: 2px;
}

/* Additional fixes for icon cutoff */
.comment-item,
.sub-comment-item {
  overflow: visible;
}

.comment-item .d-flex,
.sub-comment-item .d-flex {
  overflow: visible;
}

.flex-grow-1 {
  overflow: visible;
}

/* Ensure no overflow hidden anywhere in the comment structure */
.comments-container {
  overflow: auto;
}

.comments-container > div {
  overflow: visible;
}

.collapse {
  overflow: visible;
}

/* Improved spacing and appearance for reply action buttons */
.sub-comment-item .btn-link {
  padding: 5px 10px;
  margin: 0 5px 0 0;
  border-radius: 20px;
  transition: all 0.2s ease;
  background-color: transparent;
}

.sub-comment-item .btn-link:hover {
  background-color: rgba(108, 92, 231, 0.05);
  box-shadow: none;
}

.sub-comment-item .btn-link:first-child {
  margin-left: 0;
}

.sub-comment-item .btn-link .fa-heart,
.sub-comment-item .btn-link .fa-comment {
  margin-right: 4px;
}

/* Style for delete button in replies */
.sub-comment-item .delete-btn {
  padding: 5px 10px;
  margin-left: 8px;
  border-radius: 20px;
  background-color: rgba(108, 92, 231, 0.1);
  transition: all 0.2s ease;
}

.sub-comment-item .delete-btn:hover {
  background-color: rgba(108, 92, 231, 0.2);
  box-shadow: 0 2px 5px rgba(108, 92, 231, 0.15);
}

/* Improved action buttons container in replies */
.sub-comment-item .comment-actions {
  display: flex;
  align-items: center;
  margin-top: 8px;
  padding: 3px 0;
  gap: 12px;
}

/* Comment Styles - additional fixes */
.comment-item {
  padding: 15px;
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s;
  margin-bottom: 15px;
}

.comment-item:hover {
  background-color: #f8f9fa;
}

.comment-item .user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  object-fit: cover;
}

.comment-item:hover .user-avatar {
  box-shadow: 0 3px 8px rgba(108, 92, 231, 0.2);
}

/* Sub-comment item user avatar */
.sub-comment-item .user-avatar {
  width: 32px;
  height: 32px;
  min-width: 32px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 2px 5px rgba(108, 92, 231, 0.2);
  object-fit: cover;
}

.sub-comment-item:hover .user-avatar {
  box-shadow: 0 3px 8px rgba(108, 92, 231, 0.3);
}

/* Reduce gap between icon and count in replies */
.sub-comment-item .btn-link i {
  margin-right: 2px;
}

.sub-comment-item .btn-link span {
  margin-left: 2px !important;
}

/* Reduce gap between icon and count in replies - improved */
.sub-comment-item .btn-link .fa-heart {
  margin-right: 1px;
}

.sub-comment-item .btn-link .fa-heart + span {
  margin-left: 0 !important;
  padding-left: 1px;
}

/* Game Crypto Count Styles */
.game-crypto-count-wrapper {
  display: flex;
  align-items: center;
  gap: 15px;
  width: 100%;
  max-width: 550px;
}

.game-crypto-count {
  background: linear-gradient(135deg, rgba(142, 68, 173, 0.9) 0%, rgba(108, 52, 131, 0.9) 100%);
  color: white;
  padding: 10px 15px;
  border-radius: 10px;
  font-weight: 600;
  font-size: 16px;
  box-shadow: 0 4px 15px rgba(142, 68, 173, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  text-align: center;
  height: 45px;
}

.game-crypto-count span {
  white-space: nowrap;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.select-type {
  flex: 1;
}

.select-type .form-select {
  border-radius: 10px;
  padding: 12px 15px;
  font-size: 15px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background-color: rgba(255, 255, 255, 0.9);
  color: #444;
  font-weight: 500;
  width: 100%;
  height: 45px;
  box-shadow: none;
  transition: all 0.3s ease;
  position: relative;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%238e44ad' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 15px center;
  background-size: 16px;
  padding-right: 40px !important;
  appearance: none;
}

.select-type .form-select:focus {
  border-color: #8e44ad;
  box-shadow: 0 0 0 0.2rem rgba(142, 68, 173, 0.25);
  outline: none;
}

.select-type .form-select:hover {
  background-color: rgba(255, 255, 255, 1);
}

.btn-sync {
  min-width: 140px;
  height: 48px;
  background: linear-gradient(135deg, #4CAF50 0%, #43A047 100%);
  color: #fff;
  font-weight: 600;
  font-size: 16px;
  border: none;
  border-radius: 16px;
  box-shadow: 0 2px 10px rgba(115, 103, 240, 0.10);
  padding: 0 28px;
  transition: background 0.2s, box-shadow 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-right: 0;
}

.btn-sync:disabled:not(.loading) {
  background: linear-gradient(135deg, #bdbdbd 0%, #888 100%) !important;
  color: #f3f0fa !important;
  cursor: not-allowed;
  box-shadow: none;
}

.btn-sync.loading,
.btn-sync:disabled.loading {
  background: linear-gradient(135deg, #4CAF50 0%, #43A047 100%) !important;
  color: #fff !important;
  cursor: wait;
}

.btn-sync i {
  font-size: 14px;
  margin-right: 8px;
  transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.btn-sync:hover,
.btn-sync:focus {
  background: linear-gradient(135deg, #43A047 0%, #388E3C 100%);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
  color: white;
  outline: none;
}

.btn-sync:hover i {
  transform: rotate(180deg);
}

.btn-sync:active {
  box-shadow: 0 1px 3px rgba(76, 175, 80, 0.2);
  background-color: #388E3C;
  color: white;
  outline: none;
}

/* Stock Sync Form Styles */
.stock-sync-form {
  margin: 0;
  padding: 0;
}

.stock-form-wrapper {
  display: flex;
  align-items: center;
  gap: 15px;
  position: relative;
}

/* For WebKit Browsers (Chrome, Safari) - Custom Dropdown Styling */
.select-type .form-select::-webkit-scrollbar {
  width: 8px;
}

.select-type .form-select::-webkit-scrollbar-track {
  background: #f8f5ff;
  border-radius: 4px;
}

.select-type .form-select::-webkit-scrollbar-thumb {
  background: #8e44ad;
  border-radius: 4px;
}

.select-type .form-select::-webkit-scrollbar-thumb:hover {
  background: #7d3b9c;
}

/* Responsive styles for crypto controls */
@media (max-width: 992px) {
  .crypto-header-controls {
    gap: 10px;
  }
  
  .select-type .form-select {
    width: 200px;
  }
}

@media (max-width: 768px) {
  .content-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .crypto-header-controls {
    margin-top: 15px;
    width: 100%;
    justify-content: flex-start;
  }

  .select-type .form-select {
    width: 220px;
  }
  
  .error-message {
    white-space: nowrap;
  }
}

@media (max-width: 576px) {
  .crypto-header-controls {
    flex-direction: column;
    align-items: stretch;
    width: 100%;
    gap: 10px;
  }
  
  .game-crypto-count,
  .select-type,
  .btn-sync {
    width: 100%;
  }
  
  .select-type .form-select {
    width: 100%;
  }
  
  .stock-form-wrapper {
    flex-direction: column;
    width: 100%;
    gap: 30px;
  }
  
  .error-message {
    bottom: -22px;
    width: 100%;
    text-align: center;
    white-space: normal;
  }
}

/* Enhanced Select Dropdown Styling */
.select-type {
  position: relative;
  height: 40px;
}

.select-type .form-select {
  appearance: none;
  background-color: white;
  border: 1px solid rgba(142, 68, 173, 0.15);
  border-radius: 10px;
  color: #444;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  height: 40px;
  line-height: 40px;
  outline: none;
  padding: 0 40px 0 16px;
  transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  width: 185px;
  box-shadow: 0 2px 6px rgba(142, 68, 173, 0.06);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%238e44ad' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 14px;
  text-overflow: ellipsis;
}

.select-type .form-select:focus {
  border-color: rgba(142, 68, 173, 0.5);
  box-shadow: 0 0 0 2px rgba(142, 68, 173, 0.1), 0 4px 10px rgba(142, 68, 173, 0.08);
  outline: none;
  transform: translateY(-1px);
}

.select-type .form-select:hover {
  border-color: rgba(142, 68, 173, 0.4);
  background-color: #fefeff;
  box-shadow: 0 4px 12px rgba(142, 68, 173, 0.08);
  transform: translateY(-1px);
}

/* Dropdown arrow animation */
.select-type::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 16px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  pointer-events: none;
  transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.select-type:hover::after {
  transform: translateY(-50%) rotate(180deg);
}

/* Custom styling for dropdown options (works in modern browsers) */
.select-type .form-select option {
  background-color: white;
  color: #444;
  font-size: 14px;
  padding: 12px;
}

/* Game Crypto Count and Controls Styles */
.crypto-header-controls {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 15px;
  flex-wrap: nowrap;
}

.game-crypto-count {
  background: linear-gradient(135deg, rgba(142, 68, 173, 0.9) 0%, rgba(108, 52, 131, 0.9) 100%);
  color: white;
  padding: 10px 16px;
  border-radius: 10px;
  font-weight: 500;
  font-size: 15px;
  box-shadow: 0 4px 10px rgba(142, 68, 173, 0.2);
  display: flex;
  align-items: center;
  white-space: nowrap;
  height: 40px;
}

.game-crypto-count i {
  color: #ffc107;
  font-size: 16px;
  margin-right: 8px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

/* --- Unified Header Controls Modern Design --- */
.crypto-header-controls {
  display: flex;
  align-items: stretch;
  justify-content: flex-end;
  gap: 18px;
  flex-wrap: nowrap;
  background: none;
  border-radius: 0;
  box-shadow: none;
  padding: 0;
  min-height: unset;
}

.game-crypto-count,
.select-type,
.btn-sync {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 10px rgba(142, 68, 173, 0.10);
  margin-right: 18px;
  min-height: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.game-crypto-count {
  min-width: 210px;
  font-weight: 600;
  font-size: 17px;
  color: #6c47b6;
  background: #fff;
  border: 1.5px solid #ede7f6;
  box-shadow: 0 2px 10px rgba(142, 68, 173, 0.10);
  padding: 0 22px;
  margin-right: 18px;
}

.game-crypto-count i {
  color: #ffd700;
  font-size: 20px;
  margin-right: 10px;
  filter: drop-shadow(0 1px 2px rgba(0,0,0,0.18));
}

.select-type {
  min-width: 180px;
  max-width: 220px;
  padding: 0 10px;
  border: 1.5px solid #ede7f6;
  margin-right: 18px;
  background: #fff;
  box-shadow: 0 2px 10px rgba(142, 68, 173, 0.10);
  border-radius: 16px;
  display: flex;
  align-items: center;
  position: relative;
}

.select-type .form-select {
  width: 100%;
  height: 38px;
  border: none;
  background: transparent;
  font-size: 15px;
  color: #5e5873;
  font-weight: 500;
  padding: 0 32px 0 10px;
  border-radius: 8px;
  box-shadow: none;
  outline: none;
  appearance: none;
  transition: background 0.2s, color 0.2s;
}

.select-type .form-select:focus {
  background: #f3f0fa;
  color: #7367f0;
}

.error-message {
  position: absolute;
  left: 0;
  right: 0;
  top: 110%;
  font-size: 12px;
  color: #e74c3c;
  background: none;
  padding: 2px 0 0 0;
  text-align: left;
  white-space: normal;
  z-index: 2;
}

.btn-sync {
  min-width: 140px;
  height: 48px;
  background: linear-gradient(135deg, #7367f0 0%, #8e44ad 100%);
  color: #fff;
  font-weight: 600;
  font-size: 16px;
  border: none;
  border-radius: 16px;
  box-shadow: 0 2px 10px rgba(115, 103, 240, 0.10);
  padding: 0 28px;
  transition: background 0.2s, box-shadow 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-right: 0;
}

.btn-sync:disabled {
  background: linear-gradient(135deg, #bdbdbd 0%, #888 100%);
  color: #f3f0fa;
  cursor: not-allowed;
  box-shadow: none;
}

.btn-sync i {
  font-size: 18px;
  margin-right: 8px;
}

.crypto-header-controls > *:last-child {
  margin-right: 0 !important;
}

@media (max-width: 992px) {
  .crypto-header-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 0;
  }
  .game-crypto-count,
  .select-type,
  .btn-sync {
    min-width: 0;
    width: 100%;
    max-width: 100%;
    margin-right: 0 !important;
    margin-bottom: 12px;
  }
  .crypto-header-controls > *:last-child {
    margin-bottom: 0 !important;
  }
}

@media (max-width: 576px) {
  .game-crypto-count,
  .select-type,
  .btn-sync {
    height: 44px;
    min-height: 44px;
    font-size: 15px;
    padding: 0 10px;
  }
  .btn-sync {
    min-width: 0;
    padding: 0 10px;
  }
}

.select-type {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  position: relative;
}

.select-type .form-select {
  width: 100%;
  height: 38px;
  border: none;
  background: transparent;
  font-size: 15px;
  color: #5e5873;
  font-weight: 500;
  padding: 0 32px 0 10px;
  border-radius: 8px;
  box-shadow: none;
  outline: none;
  appearance: none;
  /* Custom SVG arrow, always visible, not blurry */
  background-image: url('data:image/svg+xml;utf8,<svg fill="%237367f0" height="18" viewBox="0 0 24 24" width="18" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/></svg>');
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 18px 18px;
  transition: none;
}

/* Remove hover/focus effects */
.select-type .form-select:hover,
.select-type .form-select:focus {
  background: transparent;
  color: #5e5873;
  box-shadow: none;
  border: none;
}

/* Make icons sharp and visible */
.game-crypto-count i {
  color: #ffb300;
  font-size: 22px;
  margin-right: 10px;
  filter: none;
  text-shadow: 0 1px 2px rgba(0,0,0,0.08);
}

.btn-sync i {
  color: #fff;
  font-size: 20px;
  margin-right: 8px;
  filter: none;
  text-shadow: 0 1px 2px rgba(0,0,0,0.08);
}

/* Error message below the select, outside the pill */
.select-type .error-message {
  position: static;
  margin-top: 6px;
  font-size: 13px;
  color: #e74c3c;
  background: none;
  padding: 0 2px;
  text-align: left;
  white-space: normal;
  z-index: 2;
  min-height: 18px;
}

@media (max-width: 992px) {
  .select-type .error-message {
    margin-bottom: 8px;
  }
}

/* Remove hover/focus effects for select-type dropdown */
.select-type .form-select:hover,
.select-type .form-select:focus {
  background: transparent !important;
  color: #5e5873 !important;
  box-shadow: none !important;
  border: none !important;
  outline: none !important;
  transform: none !important;
}

/* Remove any other select-type hover/focus rules */

.select-type .form-select,
.select-type .form-select:hover,
.select-type .form-select:focus {
  background-image: url('data:image/svg+xml;utf8,<svg fill="%237367f0" height="18" viewBox="0 0 24 24" width="18" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/></svg>') !important;
  background-repeat: no-repeat !important;
  background-position: right 12px center !important;
  background-size: 18px 18px !important;
}

.select-type {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
}

.select-type .form-select {
  width: 100%;
  height: 38px;
  border: none;
  background: transparent;
  font-size: 15px;
  color: #5e5873;
  font-weight: 500;
  padding: 0 32px 0 10px;
  border-radius: 8px;
  box-shadow: none;
  outline: none;
  appearance: none;
  background-image: url('data:image/svg+xml;utf8,<svg fill="%237367f0" height="18" viewBox="0 0 24 24" width="18" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/></svg>') !important;
  background-repeat: no-repeat !important;
  background-position: right 12px center !important;
  background-size: 18px 18px !important;
  transition: none;
}

.select-type .error-message {
  position: relative;
  margin-top: 6px;
  margin-left: 2px;
  font-size: 13px;
  color: #e74c3c;
  background: none;
  padding: 0;
  text-align: left;
  white-space: normal;
  z-index: 2;
  min-height: 18px;
  left: 0;
}
