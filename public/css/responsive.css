/* Responsive Design Styles */

/* Large devices (desktops, less than 1200px) */
@media (max-width: 1199.98px) {
    .container {
        max-width: 960px;
    }
    
    .sidebar {
        width: 250px;
    }
    
    .main-content {
        margin-left: 250px;
    }
    
    /* Dashboard stats responsive adjustments */
    .dashboard-stat-value {
        font-size: 2.2rem;
    }
    
    .dashboard-stat-icon {
        font-size: 2.5rem;
    }
}

/* Medium devices (tablets, less than 992px) */
@media (max-width: 991.98px) {
    .container {
        max-width: 720px;
    }
    
    .sidebar {
        width: 200px;
    }
    
    .main-content {
        margin-left: 200px;
    }
    
    .table-responsive {
        overflow-x: auto;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    /* Dashboard stats responsive adjustments */
    .dashboard-stat-card {
        padding: 20px;
    }
    
    .dashboard-stat-value {
        font-size: 2rem;
    }
    
    .dashboard-stat-icon {
        font-size: 2.2rem;
        right: 15px;
        top: 15px;
    }
    
    /* Modal responsive adjustments */
    .modal-dialog {
        max-width: 90%;
        margin: 1.75rem auto;
    }
    
    .modal-header {
        padding: 1rem 1.25rem;
    }
    
    .modal-header .btn-close {
        right: 1.25rem;
    }
    
    .modal-body {
        padding: 1.25rem;
    }
    
    .modal-footer {
        padding: 1rem 1.25rem;
    }
}

/* Small devices (landscape phones, less than 768px) */
@media (max-width: 767.98px) {
    .container {
        max-width: 540px;
    }
    
    .sidebar {
        position: fixed;
        left: -250px;
        transition: 0.3s;
        z-index: 1000;
    }
    
    .sidebar.active {
        left: 0;
    }
    
    .main-content {
        margin-left: 0;
        width: 100%;
    }
    
    .navbar {
        padding: 0.5rem 1rem;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem;
    }
    
    .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    /* Dashboard stats responsive adjustments */
    .dashboard-overview .row {
        margin-bottom: 10px;
    }
    
    .dashboard-stat-card {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .dashboard-stat-value {
        font-size: 1.8rem;
    }
    
    .dashboard-stat-card .text-success,
    .dashboard-stat-card .text-danger {
        font-size: 0.8rem;
    }
    
    .dashboard-stat-icon {
        font-size: 2rem;
        right: 15px;
        top: 15px;
    }
    
    .dashboard-stat-card h3 {
        font-size: 0.8rem;
        margin-bottom: 0.8rem;
    }
    
    /* Modal responsive adjustments */
    .modal-dialog {
        max-width: 95%;
        margin: 1rem auto;
    }
    
    .modal-header {
        padding: 0.875rem 1rem;
    }
    
    .modal-header .btn-close {
        right: 1rem;
        padding: 0.75rem;
    }
    
    .modal-title {
        font-size: 1.1rem;
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    .modal-footer {
        padding: 0.875rem 1rem;
    }
}

/* Extra small devices (portrait phones, less than 576px) */
@media (max-width: 575.98px) {
    .container {
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
    }
    
    .sidebar {
        width: 100%;
    }
    
    .navbar-nav {
        flex-direction: row;
        justify-content: space-between;
    }
    
    .nav-item {
        margin: 0 0.25rem;
    }
    
    .table {
        font-size: 0.875rem;
    }
    
    .card-title {
        font-size: 1.1rem;
    }
    
    .modal-dialog {
        max-width: 100%;
        margin: 0.5rem;
    }
    
    .form-control {
        font-size: 0.875rem;
    }
    
    .btn-group {
        display: flex;
        flex-direction: column;
    }
    
    .btn-group .btn {
        margin-bottom: 0.25rem;
    }
    
    /* Dashboard stats responsive adjustments */
    .dashboard-stat-card {
        padding: 15px;
    }
    
    .dashboard-stat-value {
        font-size: 1.5rem;
    }
    
    .dashboard-stat-icon {
        font-size: 1.8rem;
        right: 10px;
        top: 10px;
    }
    
    .dashboard-stat-card h3 {
        font-size: 0.75rem;
    }
    
    .dashboard-stat-card::before {
        width: 4px;
    }
    
    .dashboard-stat-card .text-success,
    .dashboard-stat-card .text-danger {
        font-size: 0.75rem;
    }
    
    /* Modal responsive adjustments */
    .modal-header {
        padding: 0.75rem;
    }
    
    .modal-header .btn-close {
        right: 0.75rem;
        padding: 0.5rem;
    }
    
    .modal-title {
        font-size: 1rem;
    }
    
    .modal-body {
        padding: 0.75rem;
    }
    
    .modal-footer {
        padding: 0.75rem;
    }
    
    /* Adjust blur effect for better performance on mobile */
    body.modal-open .content-wrapper,
    body.modal-open #sidebar {
        filter: blur(0);
    }
}

/* Print styles */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
        width: 100% !important;
    }
    
    .container {
        width: 100% !important;
        max-width: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .table {
        border-collapse: collapse !important;
    }
    
    .table td,
    .table th {
        background-color: #fff !important;
    }
}

/* Utility classes for responsive design */
.d-none-xs {
    display: none !important;
}

@media (min-width: 576px) {
    .d-none-xs {
        display: block !important;
    }
}

.d-flex-xs {
    display: flex !important;
}

@media (min-width: 576px) {
    .d-flex-xs {
        display: none !important;
    }
}

/* Responsive typography */
@media (max-width: 767.98px) {
    h1 {
        font-size: 1.75rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    h3 {
        font-size: 1.25rem;
    }
    
    h4 {
        font-size: 1.1rem;
    }
    
    p {
        font-size: 0.875rem;
    }
}

/* Responsive images */
.img-fluid {
    max-width: 100%;
    height: auto;
}

/* Responsive tables */
.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* Responsive forms */
@media (max-width: 767.98px) {
    .form-row {
        flex-direction: column;
    }
    
    .form-row > .col,
    .form-row > [class*="col-"] {
        padding-right: 0;
        padding-left: 0;
        margin-bottom: 1rem;
    }
}

/* Responsive grid system */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.col {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
}

@media (min-width: 576px) {
    .col-sm {
        flex-basis: 0;
        flex-grow: 1;
        max-width: 100%;
    }
}

@media (min-width: 768px) {
    .col-md {
        flex-basis: 0;
        flex-grow: 1;
        max-width: 100%;
    }
}

@media (min-width: 992px) {
    .col-lg {
        flex-basis: 0;
        flex-grow: 1;
        max-width: 100%;
    }
}

@media (min-width: 1200px) {
    .col-xl {
        flex-basis: 0;
        flex-grow: 1;
        max-width: 100%;
    }
}

/* Dashboard Statistics Card Responsive Styles */
@media (max-width: 1200px) {
    .dashboard-stat-card h3 {
        font-size: 1.1rem;
    }
    
    .dashboard-stat-value {
        font-size: 2.2rem;
    }
    
    /* Ensure user card colors remain visible */
    .dashboard-stat-card.users .dashboard-stat-icon,
    .dashboard-stat-card.active-users .dashboard-stat-icon,
    .dashboard-stat-card.inactive-users .dashboard-stat-icon,
    .dashboard-stat-card.web-users .dashboard-stat-icon {
        opacity: 0.8;
    }
}

@media (max-width: 992px) {
    .dashboard-stat-card h3 {
        font-size: 1rem;
    }
    
    .dashboard-stat-card {
        padding: 20px;
    }
    
    .dashboard-stat-value {
        font-size: 1.9rem;
    }
    
    /* Adjust icon size and position */
    .dashboard-stat-icon {
        font-size: 2.2rem;
        right: 15px;
        top: 15px;
    }
    
    /* Make card border indicator more visible */
    .dashboard-stat-card::before {
        width: 6px;
    }
}

@media (max-width: 768px) {
    .dashboard-stat-card h3 {
        font-size: 1.1rem;
        margin-bottom: 0.8rem;
    }
    
    .dashboard-stat-value {
        font-size: 2rem;
    }
    
    .dashboard-stat-card {
        padding: 20px;
        margin-bottom: 15px;
    }
    
    /* Ensure color schemes remain distinct at smaller sizes */
    .dashboard-stat-card.users .dashboard-stat-value,
    .dashboard-stat-card.active-users .dashboard-stat-value,
    .dashboard-stat-card.inactive-users .dashboard-stat-value,
    .dashboard-stat-card.web-users .dashboard-stat-value {
        font-weight: 700;
    }
    
    /* Make user cards' colors more vibrant on mobile */
    .dashboard-stat-card.users .dashboard-stat-icon {
        color: #3b82f6;
    }
    
    .dashboard-stat-card.active-users .dashboard-stat-icon {
        color: #10b981;
    }
    
    .dashboard-stat-card.inactive-users .dashboard-stat-icon {
        color: #f59e0b;
    }
    
    .dashboard-stat-card.web-users .dashboard-stat-icon {
        color: #8b5cf6;
    }
}

@media (max-width: 576px) {
    .dashboard-stat-card h3 {
        font-size: 1rem;
    }
    
    .dashboard-stat-value {
        font-size: 1.8rem;
    }
    
    .dashboard-stat-card {
        padding: 18px;
    }
    
    .dashboard-stat-icon {
        font-size: 1.8rem;
        opacity: 0.8;
    }
    
    /* Adjust hover behavior for touch screens */
    .dashboard-stat-card:hover {
        transform: translateY(-5px);
    }
    
    .dashboard-stat-card:hover .dashboard-stat-value {
        transform: scale(1.05);
    }
}

@media (max-width: 480px) {
    .dashboard-stat-card h3 {
        font-size: 0.95rem;
    }
    
    .dashboard-stat-value {
        font-size: 1.7rem;
    }
    
    /* Enhance color distinction on very small screens */
    .dashboard-stat-card::before {
        width: 4px;
    }
    
    /* Align user cards text colors more prominently */
    .dashboard-stat-card.users h3,
    .dashboard-stat-card.active-users h3,
    .dashboard-stat-card.inactive-users h3, 
    .dashboard-stat-card.web-users h3 {
        margin-bottom: 0.6rem;
        color: #333;
    }
}

/* Content Header with Icons Responsive Styles */
@media (max-width: 1200px) {
    .content-header .d-flex.align-items-center {
        display: flex !important;
        flex-direction: row !important;
    }
    
    .content-header .d-flex.align-items-center i {
        font-size: 26px;
    }
    
    .content-header #page-title {
        font-size: 24px;
    }
}

@media (max-width: 992px) {
    .content-header {
        padding: 25px 20px;
    }
    
    .content-header .d-flex.align-items-center {
        display: flex !important;
        flex-direction: row !important;
    }
    
    .content-header .d-flex.align-items-center i {
        font-size: 24px;
    }
    
    .content-header #page-title {
        font-size: 22px;
    }
}

@media (max-width: 768px) {
    .content-header {
        padding: 20px 15px;
    }
    
    .content-header .d-flex.align-items-center {
        display: flex !important;
        flex-direction: row !important;
        gap: 10px;
        align-items: center !important;
        white-space: nowrap;
    }
    
    .content-header .d-flex.align-items-center i {
        font-size: 22px;
        min-width: 22px;
    }
    
    .content-header #page-title {
        font-size: 20px;
    }
    
    .content-header .d-flex.align-items-center:hover i {
        transform: scale(1.1);
    }
    
    .content-header .d-flex.align-items-center:hover #page-title {
        transform: translateX(3px);
    }
}

@media (max-width: 576px) {
    .content-header {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .content-header .d-flex.align-items-center {
        display: flex !important;
        flex-direction: row !important;
        align-items: center !important;
    }
    
    .content-header .d-flex.align-items-center i {
        font-size: 20px;
        min-width: 20px;
    }
    
    .content-header #page-title {
        font-size: 18px;
        letter-spacing: 0.3px;
    }
}

@media (max-width: 480px) {
    .content-header .d-flex.align-items-center {
        display: flex !important;
        flex-direction: row !important;
    }
    
    .content-header .d-flex.align-items-center i {
        font-size: 18px;
        min-width: 18px;
    }
    
    .content-header #page-title {
        font-size: 16px;
        letter-spacing: 0.2px;
        font-weight: 700;
    }
}

/* Web User Details Page Responsive Styles */
@media (max-width: 1199.98px) {
    .custom-table thead th,
    .custom-table tbody td {
        padding: 0.75rem;
    }

    /* Adjust column widths for smaller screens */
    .custom-table td:has(.d-flex.align-items-center) {
        min-width: 150px;
    }

    .custom-table td:nth-child(2) {
        max-width: 160px;
    }

    .custom-table td:last-child {
        width: 90px;
    }

    .table-responsive {
        max-height: 350px;
    }

    .badge.bg-success-light,
    .points-badge {
        padding: 0.4rem 0.65rem;
    }
}

@media (max-width: 991.98px) {
    .col-lg-4 {
        margin-bottom: 1.5rem;
    }

    .col-lg-4:last-child {
        margin-bottom: 0;
    }

    .custom-table thead th,
    .custom-table tbody td {
        padding: 0.625rem;
        font-size: 0.875rem;
    }

    /* Further reduce widths */
    .custom-table td:has(.d-flex.align-items-center) {
        min-width: 140px;
    }

    .custom-table td:nth-child(2) {
        max-width: 140px;
    }

    .custom-table td:last-child {
        width: 80px;
    }

    /* Adjust hover effects for touch devices */
    .custom-table tbody tr:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(115, 103, 240, 0.08);
    }

    .badge.bg-success-light,
    .points-badge {
        padding: 0.35rem 0.6rem;
        font-size: 0.875rem;
    }

    .user-avatar {
        width: 34px;
        height: 34px;
        min-width: 34px;
    }
}

@media (max-width: 767.98px) {
    .table-responsive {
        max-height: 300px;
    }

    .custom-table thead th,
    .custom-table tbody td {
        padding: 0.5rem;
        font-size: 0.8125rem;
    }

    /* Adjust column widths for mobile */
    .custom-table td:has(.d-flex.align-items-center) {
        min-width: 130px;
    }

    .custom-table td:nth-child(2) {
        max-width: 120px;
    }

    .custom-table td:last-child {
        width: 70px;
    }

    .user-avatar {
        width: 32px;
        height: 32px;
        min-width: 32px;
    }

    /* Stack user info in mobile view */
    .d-flex.align-items-center {
        gap: 0.5rem;
    }

    /* Adjust badge sizes */
    .badge.bg-success-light,
    .points-badge {
        padding: 0.3rem 0.5rem;
        font-size: 0.8125rem;
    }

    /* Disable hover animations on mobile */
    .custom-table tbody tr:hover,
    .custom-table tbody tr:hover .user-avatar,
    .custom-table tbody tr:hover .badge.bg-success-light,
    .custom-table tbody tr:hover .points-badge {
        transform: none;
        box-shadow: none;
    }
}

@media (max-width: 575.98px) {
    .custom-table thead th,
    .custom-table tbody td {
        padding: 0.5rem 0.375rem;
        font-size: 0.75rem;
    }

    /* Minimal widths for very small screens */
    .custom-table td:has(.d-flex.align-items-center) {
        min-width: 120px;
    }

    .custom-table td:nth-child(2) {
        max-width: 110px;
    }

    .custom-table td:last-child {
        width: 65px;
    }

    .user-avatar {
        width: 30px;
        height: 30px;
        min-width: 30px;
    }

    .badge.bg-success-light,
    .points-badge {
        padding: 0.25rem 0.4rem;
        font-size: 0.75rem;
    }

    /* Ensure table header text stays visible */
    .custom-table thead th {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

/* Ensure proper spacing in card headers across all devices */
@media (max-width: 1199.98px) {
    .card-header {
        padding: 1rem 1.25rem;
    }

    .card-header h5 {
        font-size: 1rem;
    }

    .card-header .badge {
        padding: 0.4rem 0.75rem;
    }
}

@media (max-width: 991.98px) {
    .card-header {
        padding: 0.875rem 1rem;
    }

    .card-header h5 {
        font-size: 0.95rem;
    }

    .card-header .badge {
        padding: 0.35rem 0.65rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 767.98px) {
    .card-header {
        padding: 0.75rem 0.875rem;
    }

    .card-header h5 {
        font-size: 0.9rem;
    }

    .card-header .badge {
        padding: 0.3rem 0.5rem;
        font-size: 0.8125rem;
    }
}

/* Tab navigation responsive adjustments */
@media (max-width: 767.98px) {
    .nav-tabs {
        gap: 1rem;
        padding-left: 0.5rem;
    }

    .nav-tabs .nav-link {
        padding: 0.75rem 0;
        font-size: 0.9rem;
    }
}

@media (max-width: 575.98px) {
    .nav-tabs {
        gap: 0.5rem;
        padding-left: 0.25rem;
    }

    .nav-tabs .nav-link {
        padding: 0.625rem 0;
        font-size: 0.85rem;
    }
}

/* Idea Details Page Responsive Styles */
@media (max-width: 992px) {
    .post-content img {
        max-height: 400px;
        object-fit: cover;
    }

    .comment-item .d-flex {
        gap: 10px;
    }
}

@media (max-width: 768px) {
    .post-header {
        padding: 10px 0;
    }

    .post-content {
        padding: 15px 0;
    }

    .post-content p {
        font-size: 14px;
    }

    .post-stats {
        padding: 10px 0;
    }

    .stat-item {
        font-size: 13px;
    }

    .stat-item i {
        font-size: 16px;
    }

    .comment-item {
        padding: 10px 0;
    }

    .comment-item .user-avatar {
        width: 35px;
        height: 35px;
    }

    .comment-item h6 {
        font-size: 14px;
    }

    .comment-item p {
        font-size: 13px;
    }

    .sub-comment-item .user-avatar {
        width: 28px;
        height: 28px;
    }

    .sub-comment-item h6 {
        font-size: 13px;
    }

    .sub-comment-item p {
        font-size: 12px;
    }

    .created-at {
        font-size: 12px;
    }
}

@media (max-width: 576px) {
    .post-header .user-avatar {
        width: 40px;
        height: 40px;
    }

    .post-content img {
        max-height: 300px;
    }

    .card-body {
        padding: 15px;
    }

    .comment-item .user-avatar {
        width: 32px;
        height: 32px;
    }

    .sub-comment-item .user-avatar {
        width: 25px;
        height: 25px;
    }

    .sub-comments {
        padding-left: 8px;
        margin-left: 8px;
    }
}

/* Responsive styles for comments section */
@media (max-width: 768px) {
    .comment-item {
        padding: 0.75rem;
    }

    .sub-comments {
        margin-left: 1.5rem;
        padding-left: 0.75rem;
    }

    .user-avatar {
        width: 32px;
        height: 32px;
    }

    .comment-actions button {
        font-size: 0.8rem;
    }

    .delete-btn {
        font-size: 0.8rem;
    }

    .created-at {
        font-size: 0.75rem;
    }

    .created-at .date {
        margin-right: 0.25rem;
    }

    /* Adjust header spacing for mobile */
    .d-flex.align-items-center.mb-2 {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .d-flex.align-items-center.mb-2 h6 {
        font-size: 0.9rem;
    }

    .d-flex.align-items-center.mb-2 .text-muted {
        font-size: 0.8rem;
    }

    /* Adjust delete button position on mobile */
    .d-flex.align-items-center.mb-2 .delete-btn {
        margin-left: 0.5rem !important;
    }

    /* Adjust sub-comment spacing */
    .sub-comment-item {
        padding: 0.25rem 0;
    }

    .sub-comment-item .user-avatar {
        width: 28px;
        height: 28px;
    }

    .sub-comment-item h6 {
        font-size: 0.85rem;
    }

    .sub-comment-item .text-muted {
        font-size: 0.75rem;
    }

    .sub-comment-item .created-at {
        font-size: 0.7rem;
    }

    .sub-comment-item .btn-link {
        font-size: 0.75rem;
    }
}

@media (max-width: 576px) {
    .comments-container {
        padding: 0.75rem;
    }

    .comment-item {
        padding: 0.5rem;
    }

    .sub-comments {
        margin-left: 1rem;
        padding-left: 0.5rem;
    }

    /* Further reduce avatar size for very small screens */
    .user-avatar {
        width: 28px;
        height: 28px;
    }

    .sub-comment-item .user-avatar {
        width: 24px;
        height: 24px;
    }

    /* Adjust text sizes for very small screens */
    .d-flex.align-items-center.mb-2 h6 {
        font-size: 0.85rem;
    }

    .d-flex.align-items-center.mb-2 .text-muted {
        font-size: 0.75rem;
    }

    .created-at {
        font-size: 0.7rem;
    }

    .comment-actions button {
        font-size: 0.75rem;
    }

    .delete-btn {
        font-size: 0.75rem;
    }

    /* Adjust spacing for very small screens */
    .d-flex.align-items-center.mb-2 {
        gap: 0.25rem;
    }

    .sub-comment-item {
        padding: 0.2rem 0;
    }
}

/* Ensure proper scrolling on all devices */
.comments-container {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: #6f42c1 #f1f1f1;
}

/* Hide scrollbar on mobile devices while maintaining functionality */
@media (max-width: 768px) {
    .comments-container::-webkit-scrollbar {
        width: 4px;
    }
}

/* Adjust card padding for better mobile display */
@media (max-width: 768px) {
    .card-body {
        padding: 0.75rem;
    }
}

/* Ensure proper spacing in comment actions */
@media (max-width: 576px) {
    .comment-actions {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .comment-actions button {
        margin-right: 0.5rem !important;
    }
}

/* Responsive styles for modern Profile Details section */
@media (max-width: 767.98px) {
  .profile-details-card-modern {
    padding: 0;
  }
  .profile-detail-modern {
    min-width: 0;
    padding: 0.8rem 0;
  }
  .profile-detail-icon {
    font-size: 1.6rem;
    width: 36px;
    height: 36px;
  }
  .profile-detail-modern-separator {
    width: 80%;
    height: 2px;
    background: linear-gradient(90deg, #b2b2f7 0%, #7367f0 100%);
    margin: 0.7rem auto;
  }
  .profile-detail-label-modern {
    font-size: 0.85rem;
    margin-bottom: 0.1rem;
  }
  .profile-detail-value-modern {
    font-size: 1rem;
  }
} 