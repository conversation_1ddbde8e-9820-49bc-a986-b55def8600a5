{"name": "nextjs_wetrade_admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 6002", "build": "next build", "start": "next start", "lint": "next lint", "generate-models": "node generate-models.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.741.0", "@aws-sdk/lib-storage": "^3.738.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/poppins": "^5.1.1", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.9.1", "@mui/material": "^7.0.2", "@mui/x-date-pickers": "^8.0.0", "@next/env": "^15.1.4", "@reduxjs/toolkit": "^2.6.0", "animate.css": "^4.1.1", "aos": "^2.3.4", "autoprefixer": "^10.4.20", "aws-sdk": "^2.1692.0", "axios": "^1.7.9", "bcryptjs": "^2.4.3", "bootstrap": "^5.3.4", "crypto-random-string": "^5.0.0", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "font-awesome": "^4.7.0", "jimp": "^1.6.0", "jquery": "^3.7.1", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.0", "next": "^15.2.4", "next-auth": "^4.24.11", "nextjs-toploader": "^3.7.15", "nodemailer": "^6.10.0", "nprogress": "^0.2.0", "papaparse": "^5.5.2", "path": "^0.12.7", "pg-hstore": "^2.3.4", "react": "^19.0.0", "react-countup": "^6.5.3", "react-data-table-component": "^7.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-select": "^5.10.1", "react-select-async-paginate": "^0.7.10", "react-slick": "^0.30.3", "redux-persist": "^6.0.0", "sequelize": "^6.37.7", "sequelize-paginate": "^1.1.6", "sharp": "^0.33.5", "slick-carousel": "^1.8.1", "swiper": "^11.1.15", "themify-icons": "^1.0.0", "webpack": "^5.98.0", "xlsx": "^0.18.5", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/bootstrap": "^5.2.10", "@types/jsonwebtoken": "^9.0.8", "@types/react": "^19.0.10", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8", "sequelize-auto": "^0.8.8", "tailwindcss": "^4", "webpack-dev-server": "^5.2.1"}, "browser": {"fs": false, "path": false, "os": false, "crypto": false, "stream": false, "http": false, "tls": false, "zlib": false, "https": false, "net": false, "util": false, "buffer": false}}